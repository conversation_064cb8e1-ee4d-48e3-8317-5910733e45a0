/* Layout styles for LapXpert Admin Dashboard */
@use './variables.scss' as *;

/* Layout Wrapper */
.layout-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  &.layout-overlay {
    .layout-sidebar {
      position: fixed;
      z-index: $z-index-fixed;
      transform: translateX(-100%);
      @include transition(transform);
    }
    
    &.layout-overlay-active {
      .layout-sidebar {
        transform: translateX(0);
      }
      
      .layout-mask {
        display: block;
      }
    }
  }
  
  &.layout-static {
    .layout-sidebar {
      position: relative;
      transform: translateX(0);
    }
    
    &.layout-static-inactive {
      .layout-sidebar {
        transform: translateX(-#{$sidebar-width - $sidebar-width-collapsed});
      }
    }
  }
  
  &.layout-mobile-active {
    .layout-sidebar {
      transform: translateX(0);
    }
    
    .layout-mask {
      display: block;
    }
  }
}

/* Layout Mask */
.layout-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: $z-index-modal-backdrop;
  display: none;
}

/* Layout Topbar */
.layout-topbar {
  height: $topbar-height;
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  @include flex-between;
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  
  .layout-topbar-logo-container {
    @include flex-center;
    gap: 1rem;
  }
  
  .layout-menu-button {
    @include button-reset;
    width: 2.5rem;
    height: 2.5rem;
    @include flex-center;
    border-radius: $border-radius;
    color: var(--text-color);
    @include transition();
    
    &:hover {
      background: var(--surface-hover);
    }
  }
  
  .layout-topbar-logo {
    @include flex-center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 600;
    
    svg {
      width: 2rem;
      height: 2rem;
    }
  }
  
  .layout-topbar-actions {
    @include flex-center;
    gap: 0.5rem;
  }
  
  .layout-topbar-action {
    @include button-reset;
    width: 2.5rem;
    height: 2.5rem;
    @include flex-center;
    border-radius: $border-radius;
    color: var(--text-color);
    @include transition();
    
    &:hover {
      background: var(--surface-hover);
    }
    
    &.layout-topbar-action-highlight {
      background: var(--primary-color);
      color: var(--primary-contrast-color);
      
      &:hover {
        background: var(--primary-color);
        opacity: 0.9;
      }
    }
  }
  
  .layout-topbar-menu {
    .layout-topbar-menu-content {
      @include flex-center;
      gap: 1rem;
    }
    
    .layout-topbar-action {
      @include flex-center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      width: auto;
      font-size: 0.875rem;
      
      span {
        display: none;
        
        @media (min-width: $breakpoint-lg) {
          display: inline;
        }
      }
    }
  }
}

/* Layout Sidebar */
.layout-sidebar {
  width: $sidebar-width;
  background: var(--surface-card);
  border-right: 1px solid var(--surface-border);
  @include transition(transform);
  
  @media (max-width: $breakpoint-lg) {
    position: fixed;
    height: 100vh;
    z-index: $z-index-fixed;
    transform: translateX(-100%);
  }
}

/* Layout Main Container */
.layout-main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0;
  
  @media (min-width: $breakpoint-lg) {
    .layout-static & {
      margin-left: $sidebar-width;
    }
    
    .layout-static.layout-static-inactive & {
      margin-left: $sidebar-width-collapsed;
    }
  }
}

/* Layout Main */
.layout-main {
  flex: 1;
  padding: 1.5rem;
  
  @media (max-width: $breakpoint-md) {
    padding: 1rem;
  }
}

/* Layout Footer */
.layout-footer {
  height: $footer-height;
  background: var(--surface-card);
  border-top: 1px solid var(--surface-border);
  @include flex-center;
  padding: 0 1.5rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}
