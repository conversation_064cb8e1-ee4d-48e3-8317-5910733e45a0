/* Global styles for LapXpert Admin Dashboard */
@use 'primeicons/primeicons.css';
@use './variables.scss';
@use './layout.scss';
@use './components.scss';

/* Global CSS Variables */
:root {
  --primary-color: var(--p-primary-color);
  --primary-contrast-color: var(--p-primary-contrast-color);
  --text-color: var(--p-text-color);
  --text-color-secondary: var(--p-text-muted-color);
  --surface-border: var(--p-content-border-color);
  --surface-card: var(--p-content-background);
  --surface-hover: var(--p-content-hover-background);
  --surface-overlay: var(--p-overlay-popover-background);
  --transition-duration: 0.2s;
}

/* Global utility classes */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors;
}

/* Animation classes */
.animate-fadein {
  animation: fadein 0.15s ease-in-out;
}

.animate-fadeout {
  animation: fadeout 0.15s ease-in-out;
}

.animate-scalein {
  animation: scalein 0.15s ease-in-out;
}

@keyframes fadein {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeout {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes scalein {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Dark mode specific styles */
.app-dark {
  color-scheme: dark;
}

/* Layout specific styles */
.layout-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.layout-main {
  flex: 1;
  padding: 1.5rem;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .layout-main {
    padding: 1rem;
  }
}
