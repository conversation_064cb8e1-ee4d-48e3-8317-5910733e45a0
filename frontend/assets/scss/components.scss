/* Component styles for LapXpert Admin Dashboard */
@use './variables.scss' as *;

/* Menu Styles */
.layout-menu {
  list-style: none;
  margin: 0;
  padding: 1rem 0;
  
  .menu-separator {
    height: 1px;
    background: var(--surface-border);
    margin: 0.5rem 1rem;
  }
}

.layout-menuitem {
  .layout-menuitem-root-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-color-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 1rem 1rem 0.5rem 1rem;
  }
  
  .layout-menuitem-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    @include transition();
    border-radius: 0;
    margin: 0 0.5rem;
    
    &:hover {
      background: var(--surface-hover);
      border-radius: $border-radius;
    }
    
    &.router-link-active {
      background: var(--primary-color);
      color: var(--primary-contrast-color);
      border-radius: $border-radius;
    }
    
    .layout-menuitem-icon {
      width: 1.25rem;
      height: 1.25rem;
      flex-shrink: 0;
    }
    
    .layout-menuitem-text {
      font-weight: 500;
    }
    
    .layout-submenu-toggler {
      margin-left: auto;
      @include transition(transform);
    }
  }
  
  &.active-menuitem {
    > .layout-menuitem-link {
      .layout-submenu-toggler {
        transform: rotate(90deg);
      }
    }
  }
}

.layout-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  
  .layout-menuitem-link {
    padding-left: 3rem;
    font-size: 0.875rem;
  }
}

/* Card Component */
.card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  padding: 1.5rem;
  
  .card-header {
    @include flex-between;
    margin-bottom: 1.5rem;
    
    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }
    
    .card-subtitle {
      font-size: 0.875rem;
      color: var(--text-color-secondary);
      margin: 0.25rem 0 0 0;
    }
  }
  
  .card-content {
    color: var(--text-color);
  }
  
  .card-footer {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
  }
}

/* Button Variants */
.btn {
  @include button-reset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: $border-radius;
  font-weight: 500;
  font-size: 0.875rem;
  @include transition();
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  &.btn-primary {
    background: var(--primary-color);
    color: var(--primary-contrast-color);
    
    &:hover:not(:disabled) {
      background: var(--primary-color);
      opacity: 0.9;
    }
  }
  
  &.btn-secondary {
    background: var(--surface-hover);
    color: var(--text-color);
    
    &:hover:not(:disabled) {
      background: var(--surface-border);
    }
  }
  
  &.btn-outline {
    background: transparent;
    border: 1px solid var(--surface-border);
    color: var(--text-color);
    
    &:hover:not(:disabled) {
      background: var(--surface-hover);
    }
  }
  
  &.btn-text {
    background: transparent;
    color: var(--text-color);
    
    &:hover:not(:disabled) {
      background: var(--surface-hover);
    }
  }
}

/* Form Controls */
.form-group {
  margin-bottom: 1rem;
  
  .form-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }
  
  .form-help {
    font-size: 0.8125rem;
    color: var(--text-color-secondary);
    margin-top: 0.25rem;
  }
  
  .form-error {
    font-size: 0.8125rem;
    color: var(--error-color);
    margin-top: 0.25rem;
  }
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-color-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-error { color: var(--error-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-surface { background-color: var(--surface-card) !important; }
.bg-hover { background-color: var(--surface-hover) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-surface { border-color: var(--surface-border) !important; }

.shadow-sm { box-shadow: $shadow-sm !important; }
.shadow { box-shadow: $shadow !important; }
.shadow-md { box-shadow: $shadow-md !important; }
.shadow-lg { box-shadow: $shadow-lg !important; }
