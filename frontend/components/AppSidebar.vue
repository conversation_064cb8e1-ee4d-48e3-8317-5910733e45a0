<template>
  <div class="layout-sidebar">
    <AppMenu />
  </div>
</template>

<script setup>
// No additional logic needed - just a wrapper for the menu
</script>

<style lang="scss" scoped>
.layout-sidebar {
  height: 100vh;
  overflow-y: auto;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--surface-border);
    border-radius: 3px;
    
    &:hover {
      background: var(--text-color-secondary);
    }
  }
}
</style>
