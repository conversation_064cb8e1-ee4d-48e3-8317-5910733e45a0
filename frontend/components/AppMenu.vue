<template>
  <ul class="layout-menu">
    <template v-for="(item, i) in model" :key="item">
      <li v-if="item.separator" class="menu-separator"></li>
      <li v-else class="layout-menuitem" :class="item.class">
        <div v-if="item.items" class="layout-menuitem-root-text">{{ item.label }}</div>
        <AppMenuitem v-else :item="item" :index="i" />
        <ul v-if="item.items" class="layout-submenu">
          <AppMenuitem
            v-for="(child, j) in item.items"
            :key="child"
            :item="child"
            :index="j"
            :parentItemKey="item.key"
          />
        </ul>
      </li>
    </template>
  </ul>
</template>

<script setup>
const model = ref([
  {
    label: 'Dashboard',
    items: [
      {
        label: 'Tổng quan',
        icon: 'pi pi-fw pi-home',
        to: '/'
      },
      {
        label: 'Thống kê',
        icon: 'pi pi-fw pi-chart-line',
        to: '/statistics'
      }
    ]
  },
  {
    label: 'Quản lý sản phẩm',
    items: [
      {
        label: 'Danh sách sản phẩm',
        icon: 'pi pi-fw pi-desktop',
        to: '/products'
      },
      {
        label: 'Thêm sản phẩm',
        icon: 'pi pi-fw pi-plus',
        to: '/products/create'
      },
      {
        label: 'Danh mục',
        icon: 'pi pi-fw pi-tags',
        to: '/categories'
      },
      {
        label: 'Thương hiệu',
        icon: 'pi pi-fw pi-bookmark',
        to: '/brands'
      }
    ]
  },
  {
    label: 'Quản lý đơn hàng',
    items: [
      {
        label: 'Danh sách đơn hàng',
        icon: 'pi pi-fw pi-shopping-cart',
        to: '/orders'
      },
      {
        label: 'Đơn hàng chờ xử lý',
        icon: 'pi pi-fw pi-clock',
        to: '/orders/pending'
      },
      {
        label: 'Đơn hàng đã giao',
        icon: 'pi pi-fw pi-check-circle',
        to: '/orders/delivered'
      }
    ]
  },
  {
    label: 'Quản lý người dùng',
    items: [
      {
        label: 'Danh sách người dùng',
        icon: 'pi pi-fw pi-users',
        to: '/users'
      },
      {
        label: 'Thêm người dùng',
        icon: 'pi pi-fw pi-user-plus',
        to: '/users/create'
      },
      {
        label: 'Phân quyền',
        icon: 'pi pi-fw pi-shield',
        to: '/users/roles'
      }
    ]
  },
  {
    label: 'Khuyến mãi',
    items: [
      {
        label: 'Mã giảm giá',
        icon: 'pi pi-fw pi-ticket',
        to: '/vouchers'
      },
      {
        label: 'Chương trình khuyến mãi',
        icon: 'pi pi-fw pi-percentage',
        to: '/promotions'
      }
    ]
  },
  {
    label: 'Báo cáo',
    items: [
      {
        label: 'Doanh thu',
        icon: 'pi pi-fw pi-chart-bar',
        to: '/reports/revenue'
      },
      {
        label: 'Sản phẩm bán chạy',
        icon: 'pi pi-fw pi-star',
        to: '/reports/bestsellers'
      },
      {
        label: 'Khách hàng',
        icon: 'pi pi-fw pi-user',
        to: '/reports/customers'
      }
    ]
  },
  {
    separator: true
  },
  {
    label: 'Cài đặt',
    items: [
      {
        label: 'Cấu hình hệ thống',
        icon: 'pi pi-fw pi-cog',
        to: '/settings'
      },
      {
        label: 'Sao lưu dữ liệu',
        icon: 'pi pi-fw pi-download',
        to: '/backup'
      }
    ]
  }
])
</script>
