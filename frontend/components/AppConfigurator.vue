<template>
  <div class="hidden absolute top-full right-0 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-6 z-50">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Theme Configuration
      </h3>
      <Button
        icon="pi pi-times"
        text
        rounded
        severity="secondary"
        @click="closeConfigurator"
      />
    </div>

    <div class="space-y-6">
      <!-- Theme Mode -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Theme Mode
        </label>
        <div class="flex gap-2">
          <Button
            :class="{ 'bg-blue-500 text-white': !isDarkTheme }"
            class="flex-1"
            outlined
            @click="setLightMode"
          >
            <i class="pi pi-sun mr-2"></i>
            Light
          </Button>
          <Button
            :class="{ 'bg-blue-500 text-white': isDarkTheme }"
            class="flex-1"
            outlined
            @click="setDarkMode"
          >
            <i class="pi pi-moon mr-2"></i>
            Dark
          </Button>
        </div>
      </div>

      <!-- Primary Color -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Primary Color
        </label>
        <div class="grid grid-cols-5 gap-2">
          <button
            v-for="color in primaryColors"
            :key="color.name"
            :class="[
              'w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600',
              { 'ring-2 ring-blue-500': selectedPrimary === color.name }
            ]"
            :style="{ backgroundColor: color.value }"
            @click="setPrimaryColor(color.name)"
          ></button>
        </div>
      </div>

      <!-- Menu Mode -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Menu Mode
        </label>
        <div class="flex gap-2">
          <Button
            :class="{ 'bg-blue-500 text-white': layoutConfig.menuMode === 'static' }"
            class="flex-1"
            outlined
            @click="setMenuMode('static')"
          >
            Static
          </Button>
          <Button
            :class="{ 'bg-blue-500 text-white': layoutConfig.menuMode === 'overlay' }"
            class="flex-1"
            outlined
            @click="setMenuMode('overlay')"
          >
            Overlay
          </Button>
        </div>
      </div>

      <!-- Reset Button -->
      <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          label="Reset to Default"
          icon="pi pi-refresh"
          outlined
          class="w-full"
          @click="resetToDefault"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
const { layoutConfig, isDarkTheme, toggleDarkMode } = useLayout()

const selectedPrimary = ref('emerald')

const primaryColors = [
  { name: 'emerald', value: '#10b981' },
  { name: 'blue', value: '#3b82f6' },
  { name: 'purple', value: '#8b5cf6' },
  { name: 'pink', value: '#ec4899' },
  { name: 'orange', value: '#f97316' },
  { name: 'red', value: '#ef4444' },
  { name: 'yellow', value: '#eab308' },
  { name: 'green', value: '#22c55e' },
  { name: 'indigo', value: '#6366f1' },
  { name: 'teal', value: '#14b8a6' }
]

const closeConfigurator = () => {
  // This will be handled by the parent component's v-styleclass directive
}

const setLightMode = () => {
  if (isDarkTheme.value) {
    toggleDarkMode()
  }
}

const setDarkMode = () => {
  if (!isDarkTheme.value) {
    toggleDarkMode()
  }
}

const setPrimaryColor = (colorName) => {
  selectedPrimary.value = colorName
  layoutConfig.primary = colorName
  
  // Apply color to CSS custom properties
  const color = primaryColors.find(c => c.name === colorName)
  if (color && process.client) {
    document.documentElement.style.setProperty('--p-primary-color', color.value)
  }
}

const setMenuMode = (mode) => {
  layoutConfig.menuMode = mode
}

const resetToDefault = () => {
  selectedPrimary.value = 'emerald'
  layoutConfig.primary = 'emerald'
  layoutConfig.menuMode = 'static'
  
  if (process.client) {
    document.documentElement.style.removeProperty('--p-primary-color')
  }
}
</script>

<style lang="scss" scoped>
// Additional styles if needed
</style>
