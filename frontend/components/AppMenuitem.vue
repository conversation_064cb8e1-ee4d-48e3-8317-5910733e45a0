<template>
  <li class="layout-menuitem">
    <NuxtLink
      v-if="item.to"
      :to="item.to"
      class="layout-menuitem-link"
      :class="{ 'router-link-active': isActiveRoute }"
      @click="itemClick"
    >
      <i :class="item.icon" class="layout-menuitem-icon"></i>
      <span class="layout-menuitem-text">{{ item.label }}</span>
      <i
        v-if="item.items"
        class="pi pi-fw pi-angle-down layout-submenu-toggler"
      ></i>
    </NuxtLink>
    
    <a
      v-else
      :href="item.url"
      class="layout-menuitem-link"
      :target="item.target"
      tabindex="0"
      @click="itemClick"
    >
      <i :class="item.icon" class="layout-menuitem-icon"></i>
      <span class="layout-menuitem-text">{{ item.label }}</span>
      <i
        v-if="item.items"
        class="pi pi-fw pi-angle-down layout-submenu-toggler"
      ></i>
    </a>
    
    <Transition
      v-if="item.items"
      name="layout-submenu"
      @enter="onEnter"
      @leave="onLeave"
    >
      <ul v-show="isActiveMenu" class="layout-submenu">
        <AppMenuitem
          v-for="(child, i) in item.items"
          :key="child"
          :item="child"
          :index="i"
          :parentItemKey="item.key"
        />
      </ul>
    </Transition>
  </li>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  index: {
    type: Number,
    default: 0
  },
  parentItemKey: {
    type: String,
    default: null
  }
})

const route = useRoute()
const { layoutState, setActiveMenuItem } = useLayout()

const isActiveMenu = ref(false)
const itemKey = ref(null)

onMounted(() => {
  itemKey.value = props.parentItemKey ? `${props.parentItemKey}-${props.index}` : String(props.index)
  
  // Check if current route matches this menu item
  if (props.item.to && route.path === props.item.to) {
    setActiveMenuItem(itemKey.value)
  }
})

const isActiveRoute = computed(() => {
  return props.item.to && route.path === props.item.to
})

watch(
  () => layoutState.activeMenuItem,
  (newVal) => {
    isActiveMenu.value = newVal === itemKey.value || newVal?.startsWith(itemKey.value + '-')
  }
)

const itemClick = (event) => {
  if (props.item.disabled) {
    event.preventDefault()
    return
  }

  if (props.item.command) {
    props.item.command({ originalEvent: event, item: props.item })
  }

  if (props.item.items) {
    setActiveMenuItem(isActiveMenu.value ? null : itemKey.value)
  } else {
    setActiveMenuItem(itemKey.value)
  }
}

const onEnter = (el) => {
  el.style.height = '0px'
  el.style.overflow = 'hidden'
  el.offsetHeight // force reflow
  el.style.height = el.scrollHeight + 'px'
  el.style.transition = 'height 0.3s ease-in-out'
}

const onLeave = (el) => {
  el.style.height = el.scrollHeight + 'px'
  el.offsetHeight // force reflow
  el.style.height = '0px'
  el.style.transition = 'height 0.3s ease-in-out'
}
</script>

<style lang="scss" scoped>
.layout-submenu-enter-active,
.layout-submenu-leave-active {
  overflow: hidden;
}

.layout-submenu-enter-from,
.layout-submenu-leave-to {
  height: 0;
}
</style>
