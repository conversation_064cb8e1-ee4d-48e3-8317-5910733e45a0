<template>
  <div class="card">
    <div class="card-header">
      <div>
        <h3 class="card-title">{{ title }}</h3>
        <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          v-if="showRefresh"
          icon="pi pi-refresh"
          outlined
          @click="refreshData"
          :loading="loading"
        />
        <Button
          v-if="showExport"
          icon="pi pi-download"
          label="Export"
          outlined
          @click="exportData"
        />
        <Button
          v-if="showAdd"
          icon="pi pi-plus"
          label="Thêm người dùng"
          @click="$emit('add-user')"
        />
      </div>
    </div>

    <div class="card-content">
      <!-- Search and Filters -->
      <div class="flex flex-col sm:flex-row gap-4 mb-6">
        <div class="flex-1">
          <IconField iconPosition="left">
            <InputIcon class="pi pi-search" />
            <InputText
              v-model="searchQuery"
              placeholder="Tìm kiếm theo tên, email..."
              class="w-full"
            />
          </IconField>
        </div>
        <div class="flex gap-2">
          <Dropdown
            v-model="selectedRole"
            :options="roleOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Vai trò"
            class="w-40"
            showClear
          />
          <Dropdown
            v-model="selectedStatus"
            :options="statusOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Trạng thái"
            class="w-40"
            showClear
          />
        </div>
      </div>

      <!-- Data Table -->
      <DataTable
        :value="filteredUsers"
        :loading="loading"
        :paginator="true"
        :rows="pageSize"
        :totalRecords="totalRecords"
        :lazy="lazy"
        @page="onPage"
        @sort="onSort"
        sortMode="multiple"
        removableSort
        class="p-datatable-sm"
        :globalFilterFields="['hoTen', 'email', 'soDienThoai']"
        :rowsPerPageOptions="[10, 25, 50]"
        currentPageReportTemplate="Hiển thị {first} đến {last} trong tổng số {totalRecords} người dùng"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
      >
        <template #empty>
          <div class="text-center py-8">
            <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600 dark:text-gray-400">Không tìm thấy người dùng nào</p>
          </div>
        </template>

        <template #loading>
          <div class="text-center py-8">
            <ProgressSpinner />
            <p class="text-gray-600 dark:text-gray-400 mt-4">Đang tải dữ liệu...</p>
          </div>
        </template>

        <Column field="id" header="ID" sortable style="width: 80px">
          <template #body="{ data }">
            <span class="font-mono text-sm">#{{ data.id }}</span>
          </template>
        </Column>

        <Column field="hoTen" header="Họ tên" sortable>
          <template #body="{ data }">
            <div class="flex items-center gap-3">
              <Avatar
                :image="data.avatar"
                :label="data.hoTen?.charAt(0)"
                shape="circle"
                size="normal"
                class="flex-shrink-0"
              />
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ data.hoTen }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ data.email }}
                </div>
              </div>
            </div>
          </template>
        </Column>

        <Column field="soDienThoai" header="Số điện thoại" sortable>
          <template #body="{ data }">
            <span class="font-mono">{{ data.soDienThoai || '-' }}</span>
          </template>
        </Column>

        <Column field="vaiTro" header="Vai trò" sortable>
          <template #body="{ data }">
            <Tag
              :value="getRoleLabel(data.vaiTro)"
              :severity="getRoleSeverity(data.vaiTro)"
            />
          </template>
        </Column>

        <Column field="trangThai" header="Trạng thái" sortable>
          <template #body="{ data }">
            <Tag
              :value="data.trangThai ? 'Hoạt động' : 'Vô hiệu hóa'"
              :severity="data.trangThai ? 'success' : 'danger'"
            />
          </template>
        </Column>

        <Column field="ngayTao" header="Ngày tạo" sortable>
          <template #body="{ data }">
            <span class="text-sm">{{ formatDate(data.ngayTao) }}</span>
          </template>
        </Column>

        <Column header="Thao tác" style="width: 120px">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button
                icon="pi pi-eye"
                size="small"
                text
                rounded
                @click="$emit('view-user', data)"
                v-tooltip.top="'Xem chi tiết'"
              />
              <Button
                icon="pi pi-pencil"
                size="small"
                text
                rounded
                severity="info"
                @click="$emit('edit-user', data)"
                v-tooltip.top="'Chỉnh sửa'"
              />
              <Button
                :icon="data.trangThai ? 'pi pi-ban' : 'pi pi-check'"
                size="small"
                text
                rounded
                :severity="data.trangThai ? 'warning' : 'success'"
                @click="$emit('toggle-status', data)"
                :v-tooltip.top="data.trangThai ? 'Vô hiệu hóa' : 'Kích hoạt'"
              />
              <Button
                icon="pi pi-trash"
                size="small"
                text
                rounded
                severity="danger"
                @click="$emit('delete-user', data)"
                v-tooltip.top="'Xóa'"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  users: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Danh sách người dùng'
  },
  subtitle: {
    type: String,
    default: ''
  },
  showRefresh: {
    type: Boolean,
    default: true
  },
  showExport: {
    type: Boolean,
    default: true
  },
  showAdd: {
    type: Boolean,
    default: true
  },
  lazy: {
    type: Boolean,
    default: false
  },
  totalRecords: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'add-user',
  'view-user',
  'edit-user',
  'delete-user',
  'toggle-status',
  'refresh',
  'export',
  'page',
  'sort'
])

const searchQuery = ref('')
const selectedRole = ref(null)
const selectedStatus = ref(null)
const pageSize = ref(10)

const roleOptions = [
  { label: 'Admin', value: 'ADMIN' },
  { label: 'Nhân viên', value: 'STAFF' },
  { label: 'Khách hàng', value: 'CUSTOMER' }
]

const statusOptions = [
  { label: 'Hoạt động', value: true },
  { label: 'Vô hiệu hóa', value: false }
]

const filteredUsers = computed(() => {
  let filtered = [...props.users]

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.hoTen?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query) ||
      user.soDienThoai?.includes(query)
    )
  }

  // Role filter
  if (selectedRole.value) {
    filtered = filtered.filter(user => user.vaiTro === selectedRole.value)
  }

  // Status filter
  if (selectedStatus.value !== null) {
    filtered = filtered.filter(user => user.trangThai === selectedStatus.value)
  }

  return filtered
})

const getRoleLabel = (role) => {
  const labels = {
    'ADMIN': 'Admin',
    'STAFF': 'Nhân viên',
    'CUSTOMER': 'Khách hàng'
  }
  return labels[role] || role
}

const getRoleSeverity = (role) => {
  const severities = {
    'ADMIN': 'danger',
    'STAFF': 'info',
    'CUSTOMER': 'success'
  }
  return severities[role] || 'secondary'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('vi-VN')
}

const refreshData = () => {
  emit('refresh')
}

const exportData = () => {
  emit('export')
}

const onPage = (event) => {
  emit('page', event)
}

const onSort = (event) => {
  emit('sort', event)
}
</script>
