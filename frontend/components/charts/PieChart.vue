<template>
  <div class="chart-container">
    <Pie
      :data="chartData"
      :options="chartOptions"
      :height="height"
    />
  </div>
</template>

<script setup lang="ts">
import { Pie } from 'vue-chartjs'
import type { ChartData, ChartOptions } from '~/composables/useChart'

interface Props {
  data: ChartData
  options?: ChartOptions
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
})

// Initialize chart utilities
const chartUtils = useChart()

const chartData = computed(() => props.data)

const chartOptions = computed(() => {
  const defaultOptions = chartUtils.getDefaultOptions()
  
  // Pie chart specific options
  const pieOptions = {
    ...defaultOptions,
    scales: undefined, // Remove scales for pie chart
    plugins: {
      ...defaultOptions.plugins,
      legend: {
        ...defaultOptions.plugins?.legend,
        position: 'bottom' as const,
        labels: {
          ...defaultOptions.plugins?.legend?.labels,
          padding: 20,
          generateLabels: function(chart: any) {
            const data = chart.data
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const dataset = data.datasets[0]
                const value = dataset.data[i]
                const total = dataset.data.reduce((sum: number, val: number) => sum + val, 0)
                const percentage = ((value / total) * 100).toFixed(1)
                
                return {
                  text: `${label}: ${percentage}%`,
                  fillStyle: dataset.backgroundColor[i],
                  strokeStyle: dataset.borderColor,
                  lineWidth: dataset.borderWidth,
                  hidden: false,
                  index: i
                }
              })
            }
            return []
          }
        }
      }
    }
  }
  
  return {
    ...pieOptions,
    ...props.options
  }
})
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
}
</style>
