<template>
  <div class="chart-container">
    <Bar
      :data="chartData"
      :options="chartOptions"
      :height="height"
    />
  </div>
</template>

<script setup lang="ts">
import { Bar } from 'vue-chartjs'
import type { ChartData, ChartOptions } from '~/composables/useChart'

interface Props {
  data: ChartData
  options?: ChartOptions
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
})

// Initialize chart utilities
const chartUtils = useChart()

const chartData = computed(() => props.data)

const chartOptions = computed(() => {
  const defaultOptions = chartUtils.getDefaultOptions()
  return {
    ...defaultOptions,
    ...props.options
  }
})
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
}
</style>
