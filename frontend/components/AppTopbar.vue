<template>
  <div class="layout-topbar">
    <div class="layout-topbar-logo-container">
      <button class="layout-menu-button layout-topbar-action" @click="toggleMenu">
        <i class="pi pi-bars"></i>
      </button>
      <NuxtLink to="/" class="layout-topbar-logo">
        <svg viewBox="0 0 54 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            fill="var(--primary-color)"
            d="M2524 1391 c-67 -41 -98 -122 -75 -194 26 -78 132 -135 201 -109 21
8 34 5 65 -14 496 -306 1091 -528 1668 -624 186 -30 466 -60 567 -60 48 0 60
-4 81 -26 114 -124 324 -10 279 151 -35 123 -181 165 -272 78 l-27 -26 -143 6
c-320 15 -693 83 -1053 192 -277 84 -693 270 -945 423 -80 48 -95 62 -101 88
-9 49 -46 100 -83 118 -47 22 -123 21 -162 -3z"
          />
        </svg>
        <span>LAPXPERT</span>
      </NuxtLink>
    </div>

    <div class="layout-topbar-actions">
      <!-- Theme Toggle -->
      <div class="layout-config-menu">
        <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
          <i :class="['pi', { 'pi-moon': isDarkTheme, 'pi-sun': !isDarkTheme }]"></i>
        </button>
        
        <!-- Theme Configurator -->
        <div class="relative">
          <button
            v-styleclass="{
              selector: '@next',
              enterFromClass: 'hidden',
              enterActiveClass: 'animate-scalein',
              leaveToClass: 'hidden',
              leaveActiveClass: 'animate-fadeout',
              hideOnOutsideClick: true,
            }"
            type="button"
            class="layout-topbar-action layout-topbar-action-highlight"
          >
            <i class="pi pi-palette"></i>
          </button>
          <AppConfigurator />
        </div>
      </div>

      <!-- User Menu -->
      <div class="layout-topbar-menu hidden lg:block">
        <div class="layout-topbar-menu-content">
          <button type="button" class="layout-topbar-action">
            <i class="pi pi-calendar"></i>
            <span>Calendar</span>
          </button>
          <button type="button" class="layout-topbar-action">
            <i class="pi pi-inbox"></i>
            <span>Messages</span>
          </button>
          <button type="button" class="layout-topbar-action">
            <i class="pi pi-user"></i>
            <span>Profile</span>
          </button>
          <button
            type="button"
            class="layout-topbar-action text-red-500 hover:text-red-600"
            @click="handleLogout"
          >
            <i class="pi pi-sign-out"></i>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { toggleMenu, toggleDarkMode, isDarkTheme } = useLayout()
const { logout } = useAuth()
const router = useRouter()

const handleLogout = async () => {
  await logout()
  await router.push('/login')
}
</script>

<style lang="scss" scoped>
.layout-topbar-logo {
  svg {
    width: 2rem;
    height: 2rem;
  }
}
</style>
