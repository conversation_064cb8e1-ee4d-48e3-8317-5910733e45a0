/**
 * Chart composable for LapXpert Admin Dashboard
 * Provides chart configuration and utilities for Chart.js integration
 */

// Chart.js is registered via the chartjs.client.ts plugin

export interface ChartData {
  labels: string[]
  datasets: any[]
}

export interface ChartOptions {
  responsive?: boolean
  maintainAspectRatio?: boolean
  plugins?: any
  scales?: any
  elements?: any
}

export const useChart = () => {
  /**
   * Get theme-aware colors
   */
  const getThemeColors = () => {
    // For now, we'll use a simple dark mode detection
    // This can be enhanced later with proper theme integration
    const isDark = false // Default to light mode for now
    
    return {
      primary: isDark ? '#3B82F6' : '#2563EB',
      secondary: isDark ? '#10B981' : '#059669',
      accent: isDark ? '#F59E0B' : '#D97706',
      danger: isDark ? '#EF4444' : '#DC2626',
      warning: isDark ? '#F59E0B' : '#D97706',
      info: isDark ? '#06B6D4' : '#0891B2',
      success: isDark ? '#10B981' : '#059669',
      
      // Background colors
      background: isDark ? '#1F2937' : '#FFFFFF',
      surface: isDark ? '#374151' : '#F9FAFB',
      
      // Text colors
      textPrimary: isDark ? '#F9FAFB' : '#111827',
      textSecondary: isDark ? '#D1D5DB' : '#6B7280',
      
      // Grid colors
      gridColor: isDark ? '#374151' : '#E5E7EB',
      borderColor: isDark ? '#4B5563' : '#D1D5DB'
    }
  }

  /**
   * Default chart options with theme support
   */
  const getDefaultOptions = (): ChartOptions => {
    const colors = getThemeColors()
    
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          labels: {
            color: colors.textPrimary,
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: colors.surface,
          titleColor: colors.textPrimary,
          bodyColor: colors.textSecondary,
          borderColor: colors.borderColor,
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context: any) {
              const label = context.dataset.label || ''
              const value = context.parsed.y
              
              // Format currency for revenue charts
              if (label.toLowerCase().includes('doanh thu') || label.toLowerCase().includes('revenue')) {
                return `${label}: ${formatCurrency(value)}`
              }
              
              // Format numbers with thousand separators
              return `${label}: ${formatNumber(value)}`
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            color: colors.gridColor,
            drawBorder: false
          },
          ticks: {
            color: colors.textSecondary,
            font: {
              size: 12
            }
          }
        },
        y: {
          grid: {
            color: colors.gridColor,
            drawBorder: false
          },
          ticks: {
            color: colors.textSecondary,
            font: {
              size: 12
            },
            callback: function(value: any) {
              // Format large numbers
              if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M'
              } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K'
              }
              return value
            }
          }
        }
      },
      elements: {
        point: {
          radius: 4,
          hoverRadius: 6
        },
        line: {
          tension: 0.4
        }
      }
    }
  }

  /**
   * Generate revenue chart data
   */
  const generateRevenueData = (period: string = 'month'): ChartData => {
    const colors = getThemeColors()
    
    let labels: string[] = []
    let data: number[] = []
    
    switch (period) {
      case 'hôm nay':
        labels = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        data = [2500000, 1800000, 4200000, 8500000, 12000000, 15500000, 18200000]
        break
      case 'tuần này':
        labels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN']
        data = [15000000, 18000000, 22000000, 19000000, 25000000, 28000000, 16000000]
        break
      case 'tháng này':
      default:
        labels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12']
        data = [85000000, 92000000, 78000000, 105000000, 118000000, 125000000, 132000000, 128000000, 145000000, 138000000, 155000000, 162000000]
        break
      case 'quý này':
        labels = ['Tháng 1', 'Tháng 2', 'Tháng 3']
        data = [285000000, 312000000, 345000000]
        break
      case 'năm này':
        labels = ['Q1', 'Q2', 'Q3', 'Q4']
        data = [942000000, 1125000000, 1285000000, 1456000000]
        break
    }
    
    return {
      labels,
      datasets: [
        {
          label: 'Doanh thu',
          data,
          borderColor: colors.primary,
          backgroundColor: colors.primary + '20',
          fill: true,
          tension: 0.4
        }
      ]
    }
  }

  /**
   * Generate orders chart data
   */
  const generateOrdersData = (period: string = 'month'): ChartData => {
    const colors = getThemeColors()
    
    let labels: string[] = []
    let completedData: number[] = []
    let processingData: number[] = []
    let cancelledData: number[] = []
    
    switch (period) {
      case 'tuần này':
        labels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN']
        completedData = [45, 52, 38, 67, 73, 89, 42]
        processingData = [8, 12, 15, 9, 11, 7, 13]
        cancelledData = [2, 1, 3, 1, 2, 1, 4]
        break
      case 'tháng này':
      default:
        labels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12']
        completedData = [156, 189, 142, 198, 234, 267, 298, 276, 312, 289, 345, 378]
        processingData = [23, 28, 19, 31, 35, 42, 38, 33, 29, 37, 41, 45]
        cancelledData = [8, 5, 12, 7, 9, 6, 11, 8, 7, 9, 8, 12]
        break
    }
    
    return {
      labels,
      datasets: [
        {
          label: 'Hoàn thành',
          data: completedData,
          backgroundColor: colors.success,
          borderColor: colors.success,
          borderWidth: 1
        },
        {
          label: 'Đang xử lý',
          data: processingData,
          backgroundColor: colors.warning,
          borderColor: colors.warning,
          borderWidth: 1
        },
        {
          label: 'Đã hủy',
          data: cancelledData,
          backgroundColor: colors.danger,
          borderColor: colors.danger,
          borderWidth: 1
        }
      ]
    }
  }

  /**
   * Generate pie chart data for sales performance
   */
  const generateSalesPerformanceData = (): ChartData => {
    const colors = getThemeColors()

    return {
      labels: ['Laptop Gaming', 'Laptop Văn phòng', 'Laptop Đồ họa', 'Phụ kiện'],
      datasets: [
        {
          data: [45, 30, 15, 10],
          backgroundColor: [
            colors.primary,
            colors.secondary,
            colors.accent,
            colors.info
          ],
          borderColor: colors.background,
          borderWidth: 2
        }
      ]
    }
  }

  /**
   * Generate customer trends data
   */
  const generateCustomerTrendsData = (period: string = 'month'): ChartData => {
    const colors = getThemeColors()

    let labels: string[] = []
    let newCustomers: number[] = []
    let returningCustomers: number[] = []

    switch (period) {
      case 'tuần này':
        labels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN']
        newCustomers = [12, 15, 8, 22, 18, 25, 14]
        returningCustomers = [35, 42, 28, 48, 52, 67, 38]
        break
      case 'tháng này':
      default:
        labels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12']
        newCustomers = [45, 52, 38, 67, 73, 89, 76, 82, 95, 88, 102, 118]
        returningCustomers = [156, 189, 142, 198, 234, 267, 245, 276, 312, 289, 345, 378]
        break
    }

    return {
      labels,
      datasets: [
        {
          label: 'Khách hàng mới',
          data: newCustomers,
          backgroundColor: colors.primary,
          borderColor: colors.primary,
          borderWidth: 1
        },
        {
          label: 'Khách hàng quay lại',
          data: returningCustomers,
          backgroundColor: colors.secondary,
          borderColor: colors.secondary,
          borderWidth: 1
        }
      ]
    }
  }

  return {
    getThemeColors,
    getDefaultOptions,
    generateRevenueData,
    generateOrdersData,
    generateSalesPerformanceData,
    generateCustomerTrendsData
  }
}
