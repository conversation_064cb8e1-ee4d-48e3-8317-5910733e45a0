/**
 * API composable for LapXpert Admin Dashboard
 * Provides a unified interface for API calls with authentication and error handling
 */

import type { $Fetch } from 'ofetch'

export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface ApiError {
  message: string
  status?: number
  code?: string
  originalError?: any
}

export const useApi = () => {
  // Use globalThis.$fetch for proper TypeScript typing
  const $fetch = globalThis.$fetch as $Fetch
  const config = useRuntimeConfig()
  const { currentToken, logout } = useAuth()
  const toast = useToast()

  /**
   * Create authenticated API request
   */
  const createRequest = <T = any>(
    url: string,
    options: any = {}
  ): Promise<T> => {
    const defaultOptions = {
      baseURL: `${config.public.apiBase}/v1`,
      headers: {
        'Content-Type': 'application/json',
        ...(currentToken.value && {
          Authorization: `Bearer ${currentToken.value}`
        }),
        ...options.headers
      },
      onResponseError: async ({ response }: any) => {
        if (response.status === 401) {
          // Token expired or invalid
          await logout()
          throw new Error('Phiên đăng nhập đã hết hạn')
        }
        
        const errorMessage = response._data?.message || response.statusText || 'Đã xảy ra lỗi'
        throw new Error(errorMessage)
      },
      onRequestError: ({ error }: any) => {
        console.error('Request error:', error)
        throw new Error('Không thể kết nối đến máy chủ')
      }
    }

    return $fetch<T>(url, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    })
  }

  /**
   * Create public API request (no authentication required)
   */
  const createPublicRequest = <T = any>(
    url: string,
    options: any = {}
  ): Promise<T> => {
    return $fetch<T>(url, {
      baseURL: config.public.apiBase,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
  }

  /**
   * GET request
   */
  const get = <T = any>(url: string, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'GET',
      ...options
    })
  }

  /**
   * POST request
   */
  const post = <T = any>(url: string, body?: any, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'POST',
      body,
      ...options
    })
  }

  /**
   * PUT request
   */
  const put = <T = any>(url: string, body?: any, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'PUT',
      body,
      ...options
    })
  }

  /**
   * PATCH request
   */
  const patch = <T = any>(url: string, body?: any, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'PATCH',
      body,
      ...options
    })
  }

  /**
   * DELETE request
   */
  const del = <T = any>(url: string, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'DELETE',
      ...options
    })
  }

  /**
   * Upload file
   */
  const upload = <T = any>(url: string, formData: FormData, options: any = {}): Promise<T> => {
    return createRequest<T>(url, {
      method: 'POST',
      body: formData,
      headers: {
        // Remove Content-Type to let browser set it with boundary
        ...options.headers,
        'Content-Type': undefined
      },
      ...options
    })
  }

  /**
   * Handle API errors with toast notifications
   */
  const handleError = (error: any, context?: string) => {
    console.error(`API Error${context ? ` (${context})` : ''}:`, error)
    
    const message = error.message || 'Đã xảy ra lỗi không xác định'
    
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: message,
      life: 5000
    })
    
    return {
      message,
      status: error.status,
      code: error.code,
      originalError: error
    } as ApiError
  }

  /**
   * Execute API call with error handling
   */
  const execute = async <T = any>(
    apiCall: () => Promise<T>,
    options: {
      successMessage?: string
      errorContext?: string
      showSuccessToast?: boolean
    } = {}
  ): Promise<{ data?: T; error?: ApiError }> => {
    try {
      const data = await apiCall()
      
      if (options.showSuccessToast && options.successMessage) {
        toast.add({
          severity: 'success',
          summary: 'Thành công',
          detail: options.successMessage,
          life: 3000
        })
      }
      
      return { data }
    } catch (error: any) {
      const apiError = handleError(error, options.errorContext)
      return { error: apiError }
    }
  }

  return {
    // Core methods
    get,
    post,
    put,
    patch,
    delete: del,
    upload,
    
    // Public API
    publicRequest: createPublicRequest,
    
    // Utilities
    execute,
    handleError,
    
    // Raw request creators
    createRequest,
    createPublicRequest
  }
}
