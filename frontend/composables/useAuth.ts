/**
 * Authentication composable for LapXpert Admin Dashboard
 * Handles JWT authentication, role-based access, and user state management
 */

export interface User {
  id: number
  email: string
  hoTen: string
  vaiTro: 'ADMIN' | 'STAFF' | 'CUSTOMER'
  trangThai: boolean
  avatar?: string
}

export interface LoginCredentials {
  taiKhoan: string
  matKhau: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

const authState = reactive<AuthState>({
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false
})

export const useAuth = () => {
  const { $fetch } = useNuxtApp()
  const router = useRouter()
  const config = useRuntimeConfig()

  /**
   * Login user with credentials
   */
  const login = async (credentials: LoginCredentials): Promise<string> => {
    authState.isLoading = true
    
    try {
      const response = await $fetch<{ token: string; user: User }>('/auth/login', {
        method: 'POST',
        baseURL: config.public.apiBase,
        body: credentials
      })

      const { token, user } = response

      if (token && user) {
        // Store in state
        authState.token = token
        authState.user = user
        authState.isAuthenticated = true

        // Store in localStorage for persistence
        if (process.client) {
          localStorage.setItem('token', token)
          localStorage.setItem('vaiTro', user.vaiTro)
          localStorage.setItem('nguoiDung', JSON.stringify(user))
        }

        return token
      }

      throw new Error('Invalid response from server')
    } catch (error: any) {
      console.error('Login failed:', error.message)
      throw error
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Logout user and clear authentication state
   */
  const logout = async () => {
    // Clear state
    authState.token = null
    authState.user = null
    authState.isAuthenticated = false

    // Clear localStorage
    if (process.client) {
      localStorage.removeItem('token')
      localStorage.removeItem('vaiTro')
      localStorage.removeItem('nguoiDung')
    }

    // Redirect to login
    await router.push('/login')
  }

  /**
   * Initialize authentication state from localStorage
   */
  const initializeAuth = () => {
    if (!process.client) return

    const token = localStorage.getItem('token')
    const userJson = localStorage.getItem('nguoiDung')

    if (token && userJson && token !== '0' && token !== 'undefined') {
      try {
        const user = JSON.parse(userJson)
        authState.token = token
        authState.user = user
        authState.isAuthenticated = true
      } catch (error) {
        console.error('Failed to parse stored user data:', error)
        logout()
      }
    }
  }

  /**
   * Check if user has required role
   */
  const hasRole = (requiredRole: string | string[]): boolean => {
    if (!authState.user) return false

    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    return roles.includes(authState.user.vaiTro)
  }

  /**
   * Check if user can access admin features
   */
  const isAdmin = computed(() => hasRole('ADMIN'))

  /**
   * Check if user can access staff features
   */
  const isStaff = computed(() => hasRole(['ADMIN', 'STAFF']))

  /**
   * Check if user is a customer (should not access admin dashboard)
   */
  const isCustomer = computed(() => hasRole('CUSTOMER'))

  /**
   * Get current user
   */
  const currentUser = computed(() => authState.user)

  /**
   * Get current token
   */
  const currentToken = computed(() => authState.token)

  /**
   * Check authentication status
   */
  const isAuthenticated = computed(() => authState.isAuthenticated)

  /**
   * Check loading status
   */
  const isLoading = computed(() => authState.isLoading)

  /**
   * Refresh user data
   */
  const refreshUser = async () => {
    if (!authState.token) return

    try {
      const user = await $fetch<User>('/auth/me', {
        baseURL: config.public.apiBase,
        headers: {
          Authorization: `Bearer ${authState.token}`
        }
      })

      authState.user = user
      
      if (process.client) {
        localStorage.setItem('nguoiDung', JSON.stringify(user))
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error)
      await logout()
    }
  }

  // Initialize auth state on client side
  onMounted(() => {
    initializeAuth()
  })

  return {
    // State
    currentUser,
    currentToken,
    isAuthenticated,
    isLoading,
    
    // Role checks
    hasRole,
    isAdmin,
    isStaff,
    isCustomer,
    
    // Actions
    login,
    logout,
    refreshUser,
    initializeAuth
  }
}
