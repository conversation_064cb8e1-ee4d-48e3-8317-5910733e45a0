/**
 * Product attributes composable for LapXpert Admin Dashboard
 * Manages brands, categories, and product specifications
 */

import type { Brand, Category, Cpu, Ram, Gpu, Color, Storage, Screen } from './useProduct'

export const useProductAttributes = () => {
  const { get, post, put, execute } = useApi()

  // ===== BRANDS =====
  
  /**
   * Get all brands
   */
  const getAllBrands = async (): Promise<Brand[]> => {
    const { data, error } = await execute(
      () => get<Brand[]>('/products/attributes/brand'),
      { errorContext: 'L<PERSON>y danh sách thương hiệu' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save brand
   */
  const saveBrand = async (brand: Brand): Promise<Brand> => {
    const { data, error } = await execute(
      () => put<Brand>('/products/attributes/brand', brand),
      { 
        successMessage: '<PERSON><PERSON><PERSON> thương hiệu thành công',
        showSuccessToast: true,
        errorContext: '<PERSON><PERSON>u thương hiệu'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== CATEGORIES =====
  
  /**
   * Get all categories
   */
  const getAllCategories = async (): Promise<Category[]> => {
    const { data, error } = await execute(
      () => get<Category[]>('/products/attributes/category'),
      { errorContext: 'Lấy danh sách danh mục' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save category
   */
  const saveCategory = async (category: Category): Promise<Category> => {
    const { data, error } = await execute(
      () => put<Category>('/products/attributes/category', category),
      { 
        successMessage: 'Lưu danh mục thành công',
        showSuccessToast: true,
        errorContext: 'Lưu danh mục'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== CPU =====
  
  /**
   * Get all CPUs
   */
  const getAllCpus = async (): Promise<Cpu[]> => {
    const { data, error } = await execute(
      () => get<Cpu[]>('/products/attributes/cpu'),
      { errorContext: 'Lấy danh sách CPU' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save CPU
   */
  const saveCpu = async (cpu: Cpu): Promise<Cpu> => {
    const { data, error } = await execute(
      () => put<Cpu>('/products/attributes/cpu', cpu),
      { 
        successMessage: 'Lưu CPU thành công',
        showSuccessToast: true,
        errorContext: 'Lưu CPU'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== RAM =====
  
  /**
   * Get all RAMs
   */
  const getAllRams = async (): Promise<Ram[]> => {
    const { data, error } = await execute(
      () => get<Ram[]>('/products/attributes/ram'),
      { errorContext: 'Lấy danh sách RAM' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save RAM
   */
  const saveRam = async (ram: Ram): Promise<Ram> => {
    const { data, error } = await execute(
      () => put<Ram>('/products/attributes/ram', ram),
      { 
        successMessage: 'Lưu RAM thành công',
        showSuccessToast: true,
        errorContext: 'Lưu RAM'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== GPU =====
  
  /**
   * Get all GPUs
   */
  const getAllGpus = async (): Promise<Gpu[]> => {
    const { data, error } = await execute(
      () => get<Gpu[]>('/products/attributes/gpu'),
      { errorContext: 'Lấy danh sách GPU' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save GPU
   */
  const saveGpu = async (gpu: Gpu): Promise<Gpu> => {
    const { data, error } = await execute(
      () => put<Gpu>('/products/attributes/gpu', gpu),
      { 
        successMessage: 'Lưu GPU thành công',
        showSuccessToast: true,
        errorContext: 'Lưu GPU'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== COLORS =====
  
  /**
   * Get all colors
   */
  const getAllColors = async (): Promise<Color[]> => {
    const { data, error } = await execute(
      () => get<Color[]>('/products/attributes/color'),
      { errorContext: 'Lấy danh sách màu sắc' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save color
   */
  const saveColor = async (color: Color): Promise<Color> => {
    const { data, error } = await execute(
      () => put<Color>('/products/attributes/color', color),
      { 
        successMessage: 'Lưu màu sắc thành công',
        showSuccessToast: true,
        errorContext: 'Lưu màu sắc'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== STORAGE =====
  
  /**
   * Get all storage options
   */
  const getAllStorages = async (): Promise<Storage[]> => {
    const { data, error } = await execute(
      () => get<Storage[]>('/products/attributes/storage'),
      { errorContext: 'Lấy danh sách ổ cứng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save storage
   */
  const saveStorage = async (storage: Storage): Promise<Storage> => {
    const { data, error } = await execute(
      () => put<Storage>('/products/attributes/storage', storage),
      { 
        successMessage: 'Lưu ổ cứng thành công',
        showSuccessToast: true,
        errorContext: 'Lưu ổ cứng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  // ===== SCREENS =====
  
  /**
   * Get all screen options
   */
  const getAllScreens = async (): Promise<Screen[]> => {
    const { data, error } = await execute(
      () => get<Screen[]>('/products/attributes/screen'),
      { errorContext: 'Lấy danh sách màn hình' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Save screen
   */
  const saveScreen = async (screen: Screen): Promise<Screen> => {
    const { data, error } = await execute(
      () => put<Screen>('/products/attributes/screen', screen),
      { 
        successMessage: 'Lưu màn hình thành công',
        showSuccessToast: true,
        errorContext: 'Lưu màn hình'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  return {
    // Brands
    getAllBrands,
    saveBrand,
    
    // Categories
    getAllCategories,
    saveCategory,
    
    // CPU
    getAllCpus,
    saveCpu,
    
    // RAM
    getAllRams,
    saveRam,
    
    // GPU
    getAllGpus,
    saveGpu,
    
    // Colors
    getAllColors,
    saveColor,
    
    // Storage
    getAllStorages,
    saveStorage,
    
    // Screens
    getAllScreens,
    saveScreen
  }
}
