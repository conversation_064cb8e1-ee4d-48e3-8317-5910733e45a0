/**
 * Guest middleware for LapXpert Admin Dashboard
 * Redirects authenticated users away from guest-only pages (like login)
 */

export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated } = useAuth()

  // Skip check on server-side during SSR
  if (process.server) {
    return
  }

  // If user is already authenticated, redirect to dashboard
  if (isAuthenticated.value) {
    return navigateTo('/')
  }
})
