/**
 * Authentication middleware for LapXpert Admin Dashboard
 * Protects routes that require authentication
 */

export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated, currentUser } = useAuth()

  // Skip authentication check on server-side during SSR
  if (process.server) {
    return
  }

  // Check if user is authenticated
  if (!isAuthenticated.value) {
    // Redirect to login page
    return navigateTo('/login')
  }

  // Check if user is a customer (customers should not access admin dashboard)
  if (currentUser.value?.vaiTro === 'CUSTOMER') {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied. Admin privileges required.'
    })
  }
})
