/**
 * Admin middleware for LapXpert Admin Dashboard
 * Protects routes that require admin privileges
 */

export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated, isAdmin, currentUser } = useAuth()

  // Skip authentication check on server-side during SSR
  if (process.server) {
    return
  }

  // Check if user is authenticated
  if (!isAuthenticated.value) {
    return navigateTo('/login')
  }

  // Check if user has admin role
  if (!isAdmin.value) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied. Admin privileges required.'
    })
  }
})
