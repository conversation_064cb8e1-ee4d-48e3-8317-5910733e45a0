{"material-symbols": {"name": "Material Symbols", "total": 14195, "author": {"name": "Google", "url": "https://github.com/google/material-design-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/google/material-design-icons/blob/master/LICENSE"}, "samples": ["downloading", "privacy-tip", "filter-drama-outline", "assignment-ind", "monitoring", "desktop-access-disabled-outline-sharp"], "height": 24, "category": "Material", "tags": ["<PERSON>"], "palette": false}, "material-symbols-light": {"name": "Material Symbols Light", "total": 14263, "author": {"name": "Google", "url": "https://github.com/google/material-design-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/google/material-design-icons/blob/master/LICENSE"}, "samples": ["downloading", "privacy-tip", "filter-drama-outline", "assignment-ind", "monitoring", "desktop-access-disabled-outline-sharp"], "height": 24, "category": "Material", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "ic": {"name": "Google Material Icons", "total": 10955, "version": "1.0.32", "author": {"name": "Material Design Authors", "url": "https://github.com/material-icons/material-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/material-icons/material-icons/blob/master/LICENSE"}, "samples": ["baseline-notifications-active", "outline-person-outline", "twotone-videocam-off", "sharp-flash-on", "baseline-volume-mute", "twotone-battery-20"], "height": 24, "category": "Material", "tags": [], "palette": false}, "mdi": {"name": "Material Design Icons", "total": 7447, "author": {"name": "Pictogrammers", "url": "https://github.com/Templarian/MaterialDesign"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Templarian/MaterialDesign/blob/master/LICENSE"}, "samples": ["account-check", "bell-alert-outline", "calendar-edit", "skip-previous", "home-variant", "lock-open-outline"], "height": 24, "category": "Material", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "mdi-light": {"name": "Material Design Light", "total": 284, "author": {"name": "Pictogrammers", "url": "https://github.com/Templarian/MaterialDesignLight"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://github.com/Templarian/MaterialDesignLight/blob/master/LICENSE.md"}, "samples": ["cart", "bell", "login", "skip-previous", "home", "lock-open"], "height": 24, "category": "Material", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "line-md": {"name": "Material Line Icons", "total": 1091, "version": "2.0.8", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyberalien/line-md"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/cyberalien/line-md/blob/master/license.txt"}, "samples": ["loading-twotone-loop", "beer-alt-twotone-loop", "image-twotone", "account", "cloud-off-outline-loop", "cog-filled-loop"], "height": 24, "category": "Material", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke", "Contains Animations"], "palette": false}, "solar": {"name": "Solar", "total": 7401, "author": {"name": "480 Design", "url": "https://www.figma.com/community/file/1166831539721848736"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["magnifer-zoom-out-broken", "armchair-2-bold-duotone", "traffic-economy-line-duotone", "user-rounded-bold-duotone", "soundwave-linear", "hamburger-menu-broken"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "tabler": {"name": "Tabler Icons", "total": 5944, "version": "3.34.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/tabler/tabler-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/tabler/tabler-icons/blob/master/LICENSE"}, "samples": ["alien", "device-desktop", "photo", "chevron-right", "check", "square-root"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "hugeicons": {"name": "<PERSON>ge Icons", "total": 4523, "author": {"name": "Hugeicons", "url": "https://icon-sets.iconify.design/icon-sets/hugeicons/"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["analytics-up", "android", "search-02", "tick-02", "not-equal-sign", "text-align-left-01"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "mingcute": {"name": "MingCute Icon", "total": 3098, "author": {"name": "MingCute Design", "url": "https://github.com/Richard9394/MingCute"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Richard9394/MingCute/blob/main/LICENSE"}, "samples": ["edit-3-line", "alert-fill", "riding-line", "layout-9-line", "currency-dollar-2-line", "trello-board-fill"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "ri": {"name": "Remix Icon", "total": 3058, "version": "4.6.0", "author": {"name": "Remix Design", "url": "https://github.com/Remix-Design/RemixIcon"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Remix-Design/RemixIcon/blob/master/License"}, "samples": ["lock-2-line", "mark-pen-fill", "moon-line", "filter-2-fill", "text", "add-line"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "mynaui": {"name": "Myna <PERSON> Icons", "total": 2468, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/praveenjuge/mynaui-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/praveenjuge/mynaui-icons/blob/main/LICENSE"}, "samples": ["signal", "power", "tree", "cart", "filter", "clock-three"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "iconamoon": {"name": "IconaMoon", "total": 1781, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/dariushhpg1/IconaMoon"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["shield-off-thin", "lightning-1-duotone", "player-previous-fill", "folder-remove-duotone", "frame-bold", "menu-kebab-vertical-circle-light"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "iconoir": {"name": "Iconoir", "total": 1671, "version": "7.11.0", "author": {"name": "<PERSON>", "url": "https://github.com/iconoir-icons/iconoir"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/iconoir-icons/iconoir/blob/main/LICENSE"}, "samples": ["chat-bubble-check", "edit", "activity", "check", "droplet", "hashtag"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "lucide": {"name": "Lucide", "total": 1599, "author": {"name": "Lucide Contributors", "url": "https://github.com/lucide-icons/lucide"}, "license": {"title": "ISC", "spdx": "ISC", "url": "https://github.com/lucide-icons/lucide/blob/main/LICENSE"}, "samples": ["check-circle", "award", "home", "check", "mountain", "chevron-up"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "lucide-lab": {"name": "Lucide Lab", "total": 373, "author": {"name": "Lucide Contributors", "url": "https://github.com/lucide-icons/lucide-lab"}, "license": {"title": "ISC", "spdx": "ISC", "url": "https://github.com/lucide-icons/lucide-lab/blob/main/LICENSE"}, "samples": ["venn", "card-credit", "pac-man", "cent", "candlestick-big-lit", "gearbox"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "uil": {"name": "Unicons", "total": 1215, "version": "4.2.0", "author": {"name": "Iconscout", "url": "https://github.com/Iconscout/unicons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Iconscout/unicons/blob/master/LICENSE"}, "samples": ["arrow-circle-right", "chat-bubble-user", "edit-alt", "grids", "ellipsis-v", "bars"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "tdesign": {"name": "TDesign Icons", "total": 2124, "author": {"name": "TDesign", "url": "https://github.com/Tencent/tdesign-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/Tencent/tdesign-icons/blob/main/LICENSE"}, "samples": ["activity", "doge", "dam", "view-list", "rotation", "laptop"], "height": 24, "category": "UI 24px", "palette": false}, "si": {"name": "<PERSON><PERSON><PERSON>", "total": 924, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/planetabhi/sargam-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/planetabhi/sargam-icons/blob/main/LICENSE.txt"}, "samples": ["eject-duotone", "zoom-in-line", "play-duotone", "add-fill", "segment-duotone", "check-line"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "bx": {"name": "BoxIcons", "total": 814, "version": "2.1.4", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/atisawd/boxicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["heart-circle", "last-page", "bar-chart-square", "stop", "heading", "filter-alt"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "bxs": {"name": "BoxIcons Solid", "total": 665, "version": "2.1.4", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/atisawd/boxicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["edit-alt", "tree-alt", "circle-half", "eject", "flag", "download"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "majesticons": {"name": "Majesticons", "total": 760, "version": "2.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/halfmage/majesticons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/halfmage/majesticons/blob/main/LICENSE"}, "samples": ["chats-line", "home", "edit-pen-4-line", "pulse", "send-line", "lightning-bolt"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "gg": {"name": "css.gg", "total": 704, "version": "2.1.4", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/astrit/css.gg"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/astrit/css.gg/blob/master/LICENSE"}, "samples": ["align-left", "server", "overflow", "edit-flip-v", "terrain", "space-between"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "flowbite": {"name": "Flowbite Icons", "total": 751, "author": {"name": "Themesberg", "url": "https://github.com/themesberg/flowbite-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/themesberg/flowbite-icons/blob/main/LICENSE"}, "samples": ["user-outline", "vue-solid", "list-outline", "bars-from-left-outline", "letter-underline-outline", "table-column-solid"], "height": 24, "category": "UI 24px", "tags": ["Uses Stroke"], "palette": false}, "basil": {"name": "<PERSON>", "total": 493, "author": {"name": "Craftwork", "url": "https://www.figma.com/community/file/931906394678748246"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["comment-solid", "search-outline", "lightning-alt-solid", "check-outline", "asana-solid", "moon-solid"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "pixelarticons": {"name": "Pixelarticons", "total": 486, "version": "1.8.1", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/halfmage/pixelarticons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/halfmage/pixelarticons/blob/master/LICENSE"}, "samples": ["drag-and-drop", "arrows-horizontal", "heart", "radio-handheld", "alert", "folder"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "pixel": {"name": "Pixel Icon", "total": 450, "author": {"name": "HackerNoon", "url": "https://github.com/hackernoon/pixel-icon-library"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["folder-solid", "message", "bullet-list", "heading-1-solid", "exclaimation", "trash"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "akar-icons": {"name": "<PERSON><PERSON>", "total": 454, "version": "1.9.31", "author": {"name": "<PERSON>", "url": "https://github.com/artcoholic/akar-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/artcoholic/akar-icons/blob/master/LICENSE"}, "samples": ["paper", "pencil", "location", "sort", "vue-fill", "check"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "ci": {"name": "coolicons", "total": 442, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/krystonschwarze/coolicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["bulb", "house-01", "compass", "chevron-up", "shield-warning", "shrink"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "proicons": {"name": "ProIcons", "total": 518, "version": "4.12.2", "author": {"name": "ProCode", "url": "https://github.com/ProCode-Software/proicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/ProCode-Software/proicons/blob/main/LICENSE"}, "samples": ["code", "checkmark", "photo-filter", "soundwave", "more", "folder"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "typcn": {"name": "Typicons", "total": 336, "version": "2.1.2", "author": {"name": "<PERSON>", "url": "https://github.com/stephenhutchings/typicons.font"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["pin-outline", "cloud-storage", "bell", "media-eject", "adjust-contrast", "css3"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "meteor-icons": {"name": "Meteor Icons", "total": 321, "author": {"name": "zkreations", "url": "https://github.com/zkreations/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/zkreations/icons/blob/main/LICENSE"}, "samples": ["droplet", "bolt", "flipboard", "filter", "chevron-down", "xmark"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "prime": {"name": "Prime Icons", "total": 313, "author": {"name": "PrimeTek", "url": "https://github.com/primefaces/primeicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/primefaces/primeicons/blob/master/LICENSE"}, "samples": ["book", "telegram", "volume-off", "check", "ban", "chevron-up"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "circum": {"name": "Circum Icons", "total": 288, "version": "1.0.0", "author": {"name": "Klarr Agency", "url": "https://github.com/Klarr-Agency/Circum-Icons"}, "license": {"title": "Mozilla Public License 2.0", "spdx": "MPL-2.0", "url": "https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE"}, "samples": ["text", "pill", "zoom-out", "voicemail", "no-waiting-sign", "crop"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "fe": {"name": "Feather Icon", "total": 255, "version": "1.0.2", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/feathericon/feathericon"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/feathericon/feathericon/blob/master/LICENSE"}, "samples": ["add-cart", "comments", "link-external", "check", "bolt", "map"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "eos-icons": {"name": "EOS Icons", "total": 253, "version": "5.4.0", "author": {"name": "SUSE UX/UI team", "url": "https://gitlab.com/SUSE-UIUX/eos-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://gitlab.com/SUSE-UIUX/eos-icons/-/blob/master/LICENSE"}, "samples": ["modified-date-outlined", "arrow-rotate", "package", "enhancement", "quota", "commit"], "height": 24, "category": "UI 24px", "tags": [], "palette": false}, "bitcoin-icons": {"name": "Bitcoin Icons", "total": 250, "version": "0.1.10", "author": {"name": "Bitcoin Design Community", "url": "https://github.com/BitcoinDesign/Bitcoin-Icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/BitcoinDesign/Bitcoin-Icons/blob/main/LICENSE-MIT"}, "samples": ["exchange-outline", "brush-filled", "satoshi-v3-outline", "unlock-filled", "magic-wand-outline", "usb-outline"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "humbleicons": {"name": "Humbleicons", "total": 253, "version": "1.16.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/zraly/humbleicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/zraly/humbleicons/blob/master/license"}, "samples": ["aid", "droplet", "rss", "volume", "times", "check"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "uim": {"name": "Unicons Monochrome", "total": 298, "version": "4.2.0", "author": {"name": "Iconscout", "url": "https://github.com/Iconscout/unicons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Iconscout/unicons/blob/master/LICENSE"}, "samples": ["airplay", "circle-layer", "lock-access", "comment-alt-message", "web-section", "align"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "uit": {"name": "Unicons Thin Line", "total": 216, "version": "4.2.0", "author": {"name": "Iconscout", "url": "https://github.com/Iconscout/unicons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Iconscout/unicons/blob/master/LICENSE"}, "samples": ["circuit", "favorite", "toggle-on", "web-section", "angle-up", "subject"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "uis": {"name": "Unicons Solid", "total": 190, "version": "4.2.0", "author": {"name": "Iconscout", "url": "https://github.com/Iconscout/unicons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Iconscout/unicons/blob/master/LICENSE"}, "samples": ["analysis", "user-md", "bookmark", "window-grid", "check", "clock-nine"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "gridicons": {"name": "Gridicons", "total": 207, "version": "3.4.2", "author": {"name": "Automattic", "url": "https://github.com/Automattic/gridicons"}, "license": {"title": "GPL 2.0", "spdx": "GPL-2.0-only", "url": "https://github.com/Automattic/gridicons/blob/trunk/LICENSE.md"}, "samples": ["code", "multiple-users", "types", "dropdown", "filter", "offline"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "mi": {"name": "Mono Icons", "total": 180, "version": "1.3.1", "author": {"name": "Mono", "url": "https://github.com/mono-company/mono-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/mono-company/mono-icons/blob/master/LICENSE.md"}, "samples": ["bar-chart", "cloud-upload", "log-out", "board", "search", "layout"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "cuida": {"name": "Cuida I<PERSON>s", "total": 180, "version": "1.17.0", "author": {"name": "Sysvale", "url": "https://github.com/Sysvale/cuida-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Sysvale/cuida-icons/blob/main/LICENSE"}, "samples": ["sidebar-expand-outline", "filter-outline", "caret-up-outline", "check-outline", "search-outline", "pause-outline"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "weui": {"name": "WeUI Icon", "total": 162, "author": {"name": "WeUI", "url": "https://github.com/weui/weui-icon"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["search-outlined", "clip-filled", "done-filled", "more-filled", "arrow-filled", "play-outlined"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "duo-icons": {"name": "Duoicons", "total": 91, "version": "1.1.4", "author": {"name": "fernandcf", "url": "https://github.com/fazdiu/duo-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/fazdiu/duo-icons/blob/master/LICENSE"}, "samples": ["credit-card", "check-circle", "box", "folder-open", "clapperboard", "message-2"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "svg-spinners": {"name": "SVG Spinners", "total": 46, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/n3r4zzurr0/svg-spinners"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/n3r4zzurr0/svg-spinners/blob/main/LICENSE"}, "samples": ["tadpole", "pulse", "3-dots-rotate", "90-ring", "clock", "bars-fade"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Contains Animations"], "palette": false}, "lets-icons": {"name": "Lets Icons", "total": 1528, "author": {"name": "<PERSON><PERSON>", "url": "https://www.figma.com/community/file/886554014393250663"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["search-duotone-line", "view-alt", "message-duotone", "remote-light", "blank-alt-duotone", "code"], "height": 24, "category": "UI 24px", "palette": false}, "streamline-ultimate": {"name": "Ultimate free icons", "total": 1999, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["power-button", "arrow-right", "stairs-descend", "mouse-smart-bold", "end-point-circle", "subtract-circle-bold"], "height": 24, "category": "UI 24px", "tags": ["Uses Stroke"], "palette": false}, "streamline-plump": {"name": "Plump free icons", "total": 1499, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["smiley-indiferent", "music-note-2", "web", "circle-clock", "loading-horizontal-2", "steps-1-solid"], "height": 24, "category": "UI 24px", "palette": false}, "streamline-sharp": {"name": "Sharp free icons", "total": 1500, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["home-1", "paperclip-2", "new-folder", "check", "webcam-video-solid", "wave-signal"], "height": 24, "category": "UI 24px", "palette": false}, "mage": {"name": "<PERSON><PERSON>", "total": 1042, "author": {"name": "MageIcons", "url": "https://github.com/Mage-Icons/mage-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Mage-Icons/mage-icons/blob/main/License.txt"}, "samples": ["chart-25", "music-fill", "coin-a-fill", "multiply", "dash-menu", "cancel-fill"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "stash": {"name": "Stash Icons", "total": 982, "author": {"name": "Pingback LLC", "url": "https://github.com/stash-ui/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/stash-ui/icons/blob/master/LICENSE"}, "samples": ["cloud-solid", "clock-duotone", "search-duotone", "check-solid", "heart-solid", "burger-classic"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "lineicons": {"name": "Lineicons", "total": 606, "author": {"name": "Lineicons", "url": "https://github.com/LineiconsHQ/Lineicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/LineiconsHQ/Lineicons/blob/main/LICENSE.md"}, "samples": ["check", "v<PERSON><PERSON><PERSON>", "star-fat", "unsplash", "clipboard", "cart-2"], "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "icon-park-outline": {"name": "IconPark Outline", "total": 2658, "version": "1.4.2", "author": {"name": "ByteDance", "url": "https://github.com/bytedance/IconPark"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/bytedance/IconPark/blob/master/LICENSE"}, "samples": ["add-one", "english-mustache", "basketball-clothes", "sort", "lightning", "pinwheel"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "icon-park-solid": {"name": "IconPark Solid", "total": 1947, "version": "1.4.2", "author": {"name": "ByteDance", "url": "https://github.com/bytedance/IconPark"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/bytedance/IconPark/blob/master/LICENSE"}, "samples": ["add-one", "english-mustache", "basketball-clothes", "sort", "lightning", "pinwheel"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "icon-park-twotone": {"name": "IconPark TwoTone", "total": 1944, "version": "1.4.2", "author": {"name": "ByteDance", "url": "https://github.com/bytedance/IconPark"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/bytedance/IconPark/blob/master/LICENSE"}, "samples": ["add-one", "english-mustache", "basketball-clothes", "sort", "lightning", "pinwheel"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "jam": {"name": "Jam Icons", "total": 940, "author": {"name": "<PERSON>", "url": "https://github.com/michaelampr"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/cyberalien/jam-backup/blob/main/LICENSE"}, "samples": ["chevrons-square-up-right", "luggage-f", "rubber", "capsule-f", "header", "tablet"], "height": 24, "category": "UI 24px", "tags": ["<PERSON>"], "palette": false}, "streamline-cyber": {"name": "Cyber free icons", "total": 500, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["music-note-1", "navigation-up-arrow", "map-direction", "glass-1", "phone-5", "fork-knife"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes"], "palette": false}, "guidance": {"name": "Guidance", "total": 360, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["smoking-area", "playground", "glass", "paper", "escalator", "printer"], "height": 24, "category": "UI 24px", "tags": ["Precise Shapes"], "palette": false}, "carbon": {"name": "Carbon", "total": 2336, "version": "11.61.0", "author": {"name": "IBM", "url": "https://github.com/carbon-design-system/carbon/tree/main/packages/icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0"}, "samples": ["user-certification", "humidity", "edit-off", "flag-filled", "binding-01", "mac-option"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "ion": {"name": "IonIcons", "total": 1357, "version": "8.0.9", "author": {"name": "<PERSON>", "url": "https://github.com/ionic-team/ionicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/ionic-team/ionicons/blob/main/LICENSE"}, "samples": ["code-download-sharp", "contrast-outline", "checkmark-done", "navigate-sharp", "arrow-redo-outline", "bookmark-sharp"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "famicons": {"name": "Famicons", "total": 1342, "author": {"name": "Family", "url": "https://github.com/familyjs/famicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/familyjs/famicons/blob/main/LICENSE"}, "samples": ["backspace-outline", "home-sharp", "bluetooth-outline", "glasses-sharp", "resize", "funnel-sharp"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "ant-design": {"name": "Ant Design Icons", "total": 830, "version": "4.4.2", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ant-design/ant-design-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/ant-design/ant-design-icons/blob/master/LICENSE"}, "samples": ["pushpin-filled", "pie-chart-outlined", "shopping-twotone", "layout-outlined", "dash-outlined", "cloud-twotone"], "height": 16, "category": "UI 16px / 32px", "tags": ["<PERSON>"], "palette": false}, "lsicon": {"name": "Lsicon", "total": 716, "author": {"name": "Wis Design", "url": "https://www.lsicon.com/"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/wisdesignsystem/lsicon/blob/main/LICENSE"}, "samples": ["pointer-outline", "column-filled", "vip-outline", "area-chart-outline", "density-l-filled", "open-new-outline"], "height": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "gravity-ui": {"name": "Gravity UI Icons", "total": 727, "version": "2.13.0", "author": {"name": "YANDEX LLC", "url": "https://github.com/gravity-ui/icons/"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/gravity-ui/icons/blob/main/LICENSE"}, "samples": ["magnifier", "bookmark-fill", "display", "italic", "caret-down", "ban"], "height": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "cil": {"name": "CoreUI Free", "total": 554, "version": "2.0.1", "author": {"name": "creative<PERSON><PERSON><PERSON>", "url": "https://github.com/coreui/coreui-icons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["airplane-mode-off", "badge", "color-border", "bookmark", "stream", "file"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": [], "palette": false}, "ep": {"name": "Element Plus", "total": 293, "version": "2.3.1", "author": {"name": "Element Plus", "url": "https://github.com/element-plus/element-plus-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/element-plus/element-plus-icons/blob/main/packages/svg/package.json"}, "samples": ["home-filled", "partly-cloudy", "avatar", "briefcase", "platform", "flag"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "charm": {"name": "Charm Icons", "total": 261, "version": "0.12.1", "author": {"name": "<PERSON>", "url": "https://github.com/jaynewey/charm-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/jaynewey/charm-icons/blob/main/LICENSE"}, "samples": ["chart-line", "image", "thumb-up", "diamond", "diff", "tick"], "height": 16, "category": "UI 16px / 32px", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "quill": {"name": "Quill Icons", "total": 140, "author": {"name": "<PERSON>", "url": "https://www.figma.com/community/file/1034432054377533052/Quill-Iconset"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/yourtempo/tempo-quill-icons/blob/main/LICENSE"}, "samples": ["collapse", "desktop", "moon", "download", "checkmark", "activity"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "bytesize": {"name": "Bytesize Icons", "total": 101, "version": "1.4.0", "author": {"name": "<PERSON>", "url": "https://github.com/danklammer/bytesize-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/danklammer/bytesize-icons/blob/master/LICENSE.md"}, "samples": ["desktop", "code", "sign-out", "checkmark", "bookmark", "activity"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "bi": {"name": "Bootstrap Icons", "total": 2078, "version": "1.13.1", "author": {"name": "The Bootstrap Authors", "url": "https://github.com/twbs/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/twbs/icons/blob/main/LICENSE.md"}, "samples": ["graph-up", "card-image", "code-slash", "egg-fill", "brilliance", "toggle-on"], "height": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes"], "palette": false}, "streamline-pixel": {"name": "Pixel free icons", "total": 662, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["shopping-shipping-bag-1", "interface-essential-battery", "social-rewards-flag", "interface-essential-synchronize-arrows-square-2", "shopping-shipping-box", "weather-temperature-thermometer"], "height": 32, "displayHeight": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes"], "palette": false}, "streamline-block": {"name": "Streamline Block", "total": 300, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["other-ui-chat", "content-write", "content-bookmark", "nature-lightning", "control-buttons-play", "basic-ui-remove"], "height": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes"], "palette": false}, "rivet-icons": {"name": "Rivet Icons", "total": 210, "author": {"name": "Indiana University", "url": "https://github.com/indiana-university/rivet-icons"}, "license": {"title": "BSD 3-<PERSON><PERSON>", "spdx": "BSD-3-<PERSON><PERSON>", "url": "https://github.com/indiana-university/rivet-icons/blob/develop/LICENSE"}, "samples": ["lightning", "credit-card-solid", "pause", "bookmark-solid", "plus", "flag-solid"], "height": 16, "category": "UI 16px / 32px", "tags": ["Precise Shapes"], "palette": false}, "nimbus": {"name": "Nimbus", "total": 140, "version": "0.3.2", "author": {"name": "Linkedstore S.A.", "url": "https://github.com/cyberalien/nimbus-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/cyberalien/nimbus-icons/blob/main/LICENSE"}, "samples": ["barcode", "mail", "calendar", "mobile", "download", "volume"], "height": 16, "category": "UI 16px / 32px", "palette": false}, "formkit": {"name": "FormKit Icons", "total": 144, "author": {"name": "FormKit, Inc", "url": "https://github.com/formkit/formkit/tree/master/packages/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/formkit/formkit/blob/master/packages/icons/LICENSE"}, "samples": ["checkbox", "reorder", "submit", "check", "radio", "close"], "category": "UI 16px / 32px", "tags": ["Precise Shapes"], "palette": false}, "fluent": {"name": "Fluent UI System Icons", "total": 18049, "version": "1.1.302", "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui-system-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui-system-icons/blob/main/LICENSE"}, "samples": ["zoom-out-24-filled", "drink-coffee-24-regular", "photo-filter-24-regular", "checkmark-24-filled", "oval-24-filled", "cloud-24-filled"], "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "ph": {"name": "Phosphor", "total": 9072, "version": "2.1.1", "author": {"name": "Phosphor Icons", "url": "https://github.com/phosphor-icons/core"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/phosphor-icons/core/blob/main/LICENSE"}, "samples": ["folder-open-duotone", "check-square-offset-thin", "pencil-line-fill", "check-thin", "browser-duotone", "thermometer-simple-fill"], "height": 24, "category": "UI Other / Mixed Grid", "tags": ["<PERSON>"], "palette": false}, "teenyicons": {"name": "Teenyicons", "total": 1200, "version": "0.4.1", "author": {"name": "smhmd", "url": "https://github.com/teenyicons/teenyicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/teenyicons/teenyicons/blob/master/LICENSE"}, "samples": ["face-id-solid", "user-outline", "page-break-outline", "hexagon-outline", "send-left-outline", "frame-solid"], "height": 15, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "clarity": {"name": "Clarity", "total": 1103, "author": {"name": "VMware", "url": "https://github.com/vmware/clarity"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/vmware/clarity-assets/blob/master/LICENSE"}, "samples": ["help-outline-badged", "heart-broken-solid", "shield-outline-alerted", "bookmark-solid", "check-line", "tablet-solid"], "height": 36, "displayHeight": 18, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "streamline-freehand": {"name": "Freehand free icons", "total": 1000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["terminal", "cursor-speed-1", "lock-cancel-slash", "data-transfer-vertical", "discount-percent-thin", "layouts-right"], "height": 24, "category": "UI Other / Mixed Grid", "palette": false}, "ix": {"name": "Siemens Industrial Experience Icons", "total": 1315, "author": {"name": "Siemens AG", "url": "https://github.com/siemens/ix-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/siemens/ix-icons/blob/main/LICENSE.md"}, "samples": ["notification-filled", "home-filled", "panel-ipc", "plant-filled", "play-filled", "folder-filled"], "category": "UI Other / Mixed Grid", "tags": ["<PERSON>"], "palette": false}, "octicon": {"name": "Octicons", "total": 649, "version": "19.15.2", "author": {"name": "GitHub", "url": "https://github.com/primer/octicons/"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/primer/octicons/blob/main/LICENSE"}, "samples": ["alert-24", "bell-slash-24", "hourglass-24", "skip-fill-16", "chevron-up-16", "kebab-horizontal-16"], "category": "UI Other / Mixed Grid", "palette": false}, "memory": {"name": "Memory Icons", "total": 651, "author": {"name": "Pictogrammers", "url": "https://github.com/Pictogrammers/Memory"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Pictogrammers/Memory/blob/main/LICENSE"}, "samples": ["chart-bar", "application", "message", "card-text", "monitor", "poll"], "height": 22, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "system-uicons": {"name": "System UIcons", "total": 430, "author": {"name": "<PERSON>", "url": "https://github.com/CoreyGinnivan/system-uicons"}, "license": {"title": "Unlicense", "spdx": "Unlicense", "url": "https://github.com/CoreyGinnivan/system-uicons/blob/master/LICENSE"}, "samples": ["bell", "message-writing", "write", "check", "pull-left", "frame"], "height": 21, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "radix-icons": {"name": "<PERSON><PERSON><PERSON>", "total": 318, "version": "1.3.2", "author": {"name": "WorkOS", "url": "https://github.com/radix-ui/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/radix-ui/icons/blob/master/LICENSE"}, "samples": ["width", "checkbox", "code", "border-width", "all-sides", "half-2"], "height": 15, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "zondicons": {"name": "Zondicons", "total": 297, "version": "0.1.0", "author": {"name": "<PERSON>", "url": "https://github.com/dukestreetstudio/zondicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/dukestreetstudio/zondicons/blob/master/LICENSE"}, "samples": ["copy", "hand-stop", "mouse", "arrow-thick-down", "send", "checkmark"], "height": 20, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "uiw": {"name": "uiw icons", "total": 214, "version": "2.6.10", "author": {"name": "liwen0526", "url": "https://github.com/uiwjs/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/uiwjs/icons/blob/master/LICENSE"}, "samples": ["cut", "like-o", "download", "caret-down", "windows", "check"], "height": 20, "category": "UI Other / Mixed Grid", "palette": false}, "maki": {"name": "<PERSON><PERSON>", "total": 215, "version": "8.2.0", "author": {"name": "Mapbox", "url": "https://github.com/mapbox/maki"}, "license": {"title": "CC0", "spdx": "CC0-1.0", "url": "https://creativecommons.org/publicdomain/zero/1.0/"}, "samples": ["entrance-alt1", "clothing-store", "grocery", "roadblock", "monument", "shelter"], "height": 15, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "codex": {"name": "CodeX Icons", "total": 78, "author": {"name": "CodeX", "url": "https://github.com/codex-team/icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/codex-team/icons/blob/master/LICENSE"}, "samples": ["align-left", "table-with-headings", "heading", "delimiter", "dot-circle", "plus"], "category": "UI Other / Mixed Grid", "tags": ["<PERSON>", "Uses Stroke"], "palette": false}, "ei": {"name": "Evil Icons", "total": 70, "version": "1.10.1", "author": {"name": "<PERSON> and <PERSON>", "url": "https://github.com/evil-icons/evil-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/evil-icons/evil-icons/blob/master/LICENSE.txt"}, "samples": ["paperclip", "like", "arrow-right", "navicon", "chevron-right", "chart"], "height": 25, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "heroicons": {"name": "HeroIcons", "total": 1288, "version": "2.2.0", "author": {"name": "Refactoring UI Inc", "url": "https://github.com/tailwindlabs/heroicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/tailwindlabs/heroicons/blob/master/LICENSE"}, "samples": ["camera", "building-library", "receipt-refund", "bookmark", "cloud", "folder"], "category": "UI Other / Mixed Grid", "tags": ["<PERSON>"], "palette": false}, "pepicons-pop": {"name": "Pepicons Pop!", "total": 1275, "version": "3.1.1", "author": {"name": "CyCraft", "url": "https://github.com/CyCraft/pepicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/CyCraft/pepicons/blob/dev/LICENSE"}, "samples": ["bookmark", "moon", "pen", "hash", "heart", "times"], "category": "UI Other / Mixed Grid", "palette": false}, "pepicons-print": {"name": "Pepicons Print", "total": 1275, "version": "3.1.1", "author": {"name": "CyCraft", "url": "https://github.com/CyCraft/pepicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/CyCraft/pepicons/blob/dev/LICENSE"}, "samples": ["bookmark", "moon", "pen", "hash", "heart", "times"], "category": "UI Other / Mixed Grid", "palette": false}, "pepicons-pencil": {"name": "Pepicons Pencil", "total": 1275, "version": "3.1.1", "author": {"name": "CyCraft", "url": "https://github.com/CyCraft/pepicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/CyCraft/pepicons/blob/dev/LICENSE"}, "samples": ["bookmark", "moon", "pen", "hash", "heart", "times"], "category": "UI Other / Mixed Grid", "palette": false}, "f7": {"name": "Framework7 Icons", "total": 1253, "version": "5.0.5", "author": {"name": "<PERSON>", "url": "https://github.com/framework7io/framework7-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/framework7io/framework7-icons/blob/master/LICENSE"}, "samples": ["hourglass-bottomhalf-fill", "pencil-outline", "rosette", "placemark-fill", "barcode", "logo-rss"], "height": 28, "category": "UI Other / Mixed Grid", "tags": ["<PERSON>"], "palette": false}, "pajamas": {"name": "Gitlab SVGs", "total": 402, "version": "3.134.0", "author": {"name": "GitLab B.V.", "url": "https://gitlab.com/gitlab-org/gitlab-svgs/-/tree/main"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://gitlab.com/gitlab-org/gitlab-svgs/-/blob/main/LICENSE"}, "samples": ["preferences", "expire", "merge", "trend-up", "template", "symlink"], "category": "UI Other / Mixed Grid", "palette": false}, "garden": {"name": "Garden SVG Icons", "total": 1003, "author": {"name": "Zendesk", "url": "https://github.com/zendeskgarden/svg-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/zendeskgarden/svg-icons/blob/main/LICENSE.md"}, "samples": ["relationshape-sell-26", "bookmark-fill-12", "moon-fill-12", "x-stroke-16", "relationshape-guide-26", "heading-stroke-12"], "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "streamline": {"name": "Streamline", "total": 3000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["sign-hashtag", "open-umbrella-solid", "graph-bar-increase", "camping-tent", "add-circle-solid", "filter-2"], "height": 28, "category": "UI Other / Mixed Grid", "palette": false}, "streamline-flex": {"name": "Flex free icons", "total": 1500, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["shield-2", "number-sign", "heart", "merge-vertical", "thermometer-solid", "music-equalizer"], "height": 28, "category": "UI Other / Mixed Grid", "palette": false}, "fa6-solid": {"name": "Font Awesome Solid", "total": 1402, "version": "6.7.2", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["location-pin", "gem", "folder", "mound", "toggle-on", "table-columns"], "height": 32, "displayHeight": 16, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "fa6-regular": {"name": "Font Awesome Regular", "total": 163, "version": "6.7.2", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["message", "clock", "folder", "face-surprise", "bookmark", "calendar"], "height": 32, "displayHeight": 16, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "picon": {"name": "Pico-icon", "total": 824, "author": {"name": "Picon Contributors", "url": "https://github.com/yne/picon"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://github.com/yne/picon/blob/master/OFL.txt"}, "samples": ["bookmark", "rewind", "folder", "thunderbolt", "message", "east"], "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "ooui": {"name": "OOUI", "total": 364, "version": "0.51.7", "author": {"name": "OOUI Team", "url": "https://github.com/wikimedia/oojs-ui"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/wikimedia/oojs-ui/blob/master/LICENSE-MIT"}, "samples": ["search", "share", "restore", "menu", "bookmark", "check"], "height": 20, "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "oui": {"name": "OpenSearch UI", "total": 402, "version": "2.0.0", "author": {"name": "OpenSearch Contributors", "url": "https://github.com/opensearch-project/oui"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/opensearch-project/oui/blob/main/LICENSE.txt"}, "samples": ["word-wrap-disabled", "annotation", "token-rank-feature", "shard", "apps", "token-histogram"], "category": "UI Other / Mixed Grid", "palette": false}, "nrk": {"name": "NRK Core Icons", "total": 230, "author": {"name": "Norsk rikskringkasting", "url": "https://github.com/nrkno/core-icons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["lyn", "edit", "mening", "media-airplay", "category-active", "heart-active"], "category": "UI Other / Mixed Grid", "tags": ["<PERSON>"], "palette": false}, "dinkie-icons": {"name": "Dinkie Icons", "total": 1198, "author": {"name": "atelierAnchor", "url": "https://github.com/atelier-anchor/dinkie-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/atelier-anchor/dinkie-icons/blob/main/LICENSE"}, "samples": ["lock-small-filled", "u1faab-small", "battery-small", "monospaced", "mobile-phone", "menu-small"], "category": "UI Other / Mixed Grid", "palette": false}, "qlementine-icons": {"name": "<PERSON><PERSON><PERSON>", "total": 684, "author": {"name": "<PERSON>", "url": "https://github.com/oclero/qlementine-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/oclero/qlementine-icons/blob/master/LICENSE"}, "samples": ["windows-16", "check-tick-small-16", "speaker-0-16", "play-16", "triangle-filled-16", "folder-filled-16"], "category": "UI Other / Mixed Grid", "palette": false}, "streamline-ultimate-color": {"name": "Ultimate color icons", "total": 998, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["touch-up", "like", "download-bottom", "power-button", "move-expand-vertical", "keyboard-asterisk-3"], "height": 24, "category": "UI Multicolor", "palette": true}, "streamline-plump-color": {"name": "Plump color icons", "total": 1000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["tune-adjust-volume", "pacman-flat", "web", "sad-face", "location-pin-flat", "smiley-indiferent-flat"], "height": 24, "category": "UI Multicolor", "tags": ["Uses Stroke"], "palette": true}, "streamline-freehand-color": {"name": "Freehand color icons", "total": 1000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["discount-percent-thin", "cursor-speed-1", "data-transfer-diagonal", "terminal", "delete-disable-block-1", "power-button"], "height": 24, "category": "UI Multicolor", "palette": true}, "streamline-kameleon-color": {"name": "Kameleon color icons", "total": 400, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["pointer", "filter-duo", "text-file", "oven-glove-duo", "prism-2", "bank-duo"], "height": 24, "category": "UI Multicolor", "palette": true}, "streamline-stickies-color": {"name": "Stickies color icons", "total": 200, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["mail", "love", "validation-1", "help", "graph-pie", "cursor"], "category": "UI Multicolor", "palette": true}, "fluent-color": {"name": "Fluent UI System Color Icons", "total": 867, "version": "1.1.302", "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui-system-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui-system-icons/blob/main/LICENSE"}, "samples": ["mic-20", "alert-32", "wrench-24", "shifts-32", "add-circle-16", "shield-24"], "category": "UI Multicolor", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "streamline-color": {"name": "Streamline color", "total": 2000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["graph", "pie-chart-flat", "filter-2", "asterisk-1", "flash-2-flat", "bracket"], "height": 28, "category": "UI Multicolor", "palette": true}, "streamline-flex-color": {"name": "Flex color icons", "total": 1000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["fahrenheit", "tablet-capsule-flat", "shuffle", "music-equalizer", "thermometer-flat", "merge-vertical"], "height": 28, "category": "UI Multicolor", "palette": true}, "streamline-sharp-color": {"name": "Sharp color icons", "total": 1000, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["file-bookmark-flat", "double-bookmark-flat", "battery-medium-3-flat", "bluetooth", "new-folder-flat", "check"], "height": 24, "category": "UI Multicolor", "tags": ["Uses Stroke"], "palette": true}, "streamline-cyber-color": {"name": "Cyber color icons", "total": 500, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["shopping-basket-3", "command-2", "music-note-1", "loop-diamond", "cursor-scroll-vertical", "map-target-2"], "height": 24, "category": "UI Multicolor", "tags": ["Precise Shapes"], "palette": true}, "icon-park": {"name": "IconPark", "total": 2658, "version": "1.4.2", "author": {"name": "ByteDance", "url": "https://github.com/bytedance/IconPark"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/bytedance/IconPark/blob/master/LICENSE"}, "samples": ["add-one", "english-mustache", "basketball-clothes", "sort", "lightning", "pinwheel"], "height": 24, "category": "UI Multicolor", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": true}, "marketeq": {"name": "Marketeq", "total": 590, "author": {"name": "Marketeq"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["mute", "desk-6", "fishing-hook", "delta", "infinite-2", "backward-2"], "height": 25, "category": "UI Multicolor", "tags": ["<PERSON>", "Uses Stroke"], "palette": true}, "vscode-icons": {"name": "VSCode Icons", "total": 1387, "version": "12.13.0", "author": {"name": "<PERSON>", "url": "https://github.com/vscode-icons/vscode-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/vscode-icons/vscode-icons/blob/master/LICENSE"}, "samples": ["file-type-actionscript2", "file-type-json", "file-type-manifest", "default-file", "file-type-diff", "default-folder"], "height": 32, "displayHeight": 16, "category": "Programming", "tags": ["<PERSON>"], "palette": true}, "codicon": {"name": "Codicons", "total": 483, "version": "0.0.37", "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/vscode-codicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/microsoft/vscode-codicons/blob/main/LICENSE"}, "samples": ["account", "bell-dot", "new-file", "split-vertical", "debug-pause", "color-mode"], "category": "Programming", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "material-icon-theme": {"name": "Material Icon Theme", "total": 1056, "author": {"name": "Material Extensions", "url": "https://github.com/material-extensions/vscode-material-icon-theme"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/material-extensions/vscode-material-icon-theme/blob/main/LICENSE"}, "samples": ["zip", "email", "folder-upload", "stackblitz", "tree", "vscode"], "category": "Programming", "palette": true}, "file-icons": {"name": "File Icons", "total": 930, "author": {"name": "<PERSON>", "url": "https://github.com/file-icons/icons"}, "license": {"title": "ISC", "spdx": "ISC", "url": "https://github.com/file-icons/icons/blob/master/LICENSE.md"}, "samples": ["adobe", "chartjs", "dom", "yaml", "font", "npm"], "height": 16, "category": "Programming", "tags": [], "palette": false}, "devicon": {"name": "Devicon", "total": 933, "author": {"name": "konpa", "url": "https://github.com/devicons/devicon/tree/master"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/devicons/devicon/blob/master/LICENSE"}, "samples": ["windows8", "tensorflow", "logstash", "stackblitz", "fsharp", "vite"], "height": 32, "displayHeight": 16, "category": "Programming", "palette": true}, "devicon-plain": {"name": "Devicon Plain", "total": 682, "author": {"name": "konpa", "url": "https://github.com/devicons/devicon/tree/master"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/devicons/devicon/blob/master/LICENSE"}, "samples": ["kotlin", "bulma", "logstash", "flutter", "v<PERSON><PERSON><PERSON>", "fsharp"], "height": 32, "displayHeight": 16, "category": "Programming", "palette": false}, "catppuccin": {"name": "Cat<PERSON>uccin Icons", "total": 560, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/catppuccin/vscode-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/catppuccin/vscode-icons/blob/main/LICENSE"}, "samples": ["folder", "nuxt", "vscode", "amber", "stackblitz", "vercel"], "height": 16, "category": "Programming", "palette": true}, "skill-icons": {"name": "Skill Icons", "total": 397, "author": {"name": "tandpfun", "url": "https://github.com/tandpfun/skill-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/tandpfun/skill-icons/blob/main/LICENSE"}, "samples": ["markdown-light", "vuejs-dark", "html", "<PERSON><PERSON><PERSON>", "vuejs-light", "gamemakerstudio"], "height": 24, "category": "Programming", "palette": true}, "unjs": {"name": "UnJS Logos", "total": 63, "author": {"name": "UnJS", "url": "https://github.com/unjs"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0"}, "samples": ["nitro", "jiti", "unstorage", "h3", "defu", "unbuild"], "height": 32, "displayHeight": 16, "category": "Programming", "palette": true}, "simple-icons": {"name": "Simple Icons", "total": 3291, "version": "15.0.0", "author": {"name": "Simple Icons Collaborators", "url": "https://github.com/simple-icons/simple-icons"}, "license": {"title": "CC0 1.0", "spdx": "CC0-1.0", "url": "https://github.com/simple-icons/simple-icons/blob/develop/LICENSE.md"}, "samples": ["wise", "framer", "vuetify", "unsplash", "stackblitz", "<PERSON><PERSON><PERSON><PERSON>"], "height": 24, "category": "Logos", "palette": false}, "logos": {"name": "SVG Logos", "total": 1838, "author": {"name": "<PERSON>", "url": "https://github.com/gilbarbara/logos"}, "license": {"title": "CC0", "spdx": "CC0-1.0", "url": "https://raw.githubusercontent.com/gilbarbara/logos/master/LICENSE.txt"}, "samples": ["npm-icon", "uikit", "patreon", "serverless", "vue", "modernizr"], "category": "Logos", "tags": [], "palette": true}, "streamline-logos": {"name": "Logos free icons", "total": 1362, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["microsoft-windows-logo-2", "unspash-logo-solid", "patreon-logo", "macos-osx-logo-solid", "bing-logo-2-solid", "macos-osx-logo"], "height": 24, "category": "Logos", "palette": false}, "cib": {"name": "CoreUI Brands", "total": 830, "version": "2.0.1", "author": {"name": "creative<PERSON><PERSON><PERSON>", "url": "https://github.com/coreui/coreui-icons"}, "license": {"title": "CC0 1.0", "spdx": "CC0-1.0", "url": "https://creativecommons.org/publicdomain/zero/1.0/"}, "samples": ["cc-amazon-pay", "hotjar", "open-id", "unsplash", "framer", "kotlin"], "height": 32, "displayHeight": 16, "category": "Logos", "tags": [], "palette": false}, "fa6-brands": {"name": "Font Awesome Brands", "total": 495, "version": "6.7.2", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["strava", "css3", "y-combinator", "unsplash", "v<PERSON><PERSON><PERSON>", "google-drive"], "height": 32, "displayHeight": 16, "category": "Logos", "palette": false}, "bxl": {"name": "BoxIcons Logo", "total": 155, "version": "2.1.4", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/atisawd/boxicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["patreon", "adobe", "v<PERSON><PERSON><PERSON>", "unsplash", "twitch", "microsoft"], "height": 24, "category": "Logos", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "nonicons": {"name": "Nonicons", "total": 69, "version": "0.0.18", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/yamatsum/nonicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/yamatsum/nonicons/blob/master/LICENSE"}, "samples": ["kotlin-16", "vue-16", "npm-16", "next-16", "go-16", "ionic-16"], "height": 16, "category": "Logos", "palette": false}, "arcticons": {"name": "Arcticons", "total": 12946, "author": {"name": "Donnn<PERSON>", "url": "https://github.com/Arcticons-Team/Arcticons"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["paperlaunch", "gravadordevoz", "appstract", "clipious", "productivity", "daserste"], "height": 48, "displayHeight": 24, "category": "Logos", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "cbi": {"name": "Custom Brand Icons", "total": 1442, "author": {"name": "Emanuele & rchiileea", "url": "https://github.com/elax46/custom-brand-icons"}, "license": {"title": "CC BY-NC-SA 4.0", "spdx": "CC-BY-NC-SA-4.0", "url": "https://github.com/elax46/custom-brand-icons/blob/main/LICENSE"}, "samples": ["<PERSON><PERSON><PERSON><PERSON>", "espn", "roomscomputer", "netapp", "prosieben", "roomsstaircase"], "category": "Logos", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "brandico": {"name": "Brandico", "total": 45, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/fontello/brandico.font"}, "license": {"title": "CC BY SA", "spdx": "CC-BY-SA-3.0", "url": "https://creativecommons.org/licenses/by-sa/3.0/"}, "samples": ["vimeo", "twitter-bird", "yandex", "bandcamp", "facebook", "win8"], "category": "Logos", "tags": [], "palette": false}, "entypo-social": {"name": "Entypo+ Social", "total": 76, "author": {"name": "<PERSON>", "url": "https://github.com/chancancode/entypo-plus"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["linkedin-with-circle", "twitter", "youtube", "google-drive", "medium", "basecamp"], "height": 20, "category": "Logos", "tags": [], "palette": false}, "token": {"name": "Web3 Icons", "total": 1728, "version": "4.0.13", "author": {"name": "0xa3k5", "url": "https://github.com/0xa3k5/web3icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/0xa3k5/web3icons/blob/main/LICENCE"}, "samples": ["bit", "dog", "eth", "mtd", "rune", "vite"], "height": 24, "category": "Logos", "palette": false}, "token-branded": {"name": "Web3 Icons Branded", "total": 1981, "version": "4.0.13", "author": {"name": "0xa3k5", "url": "https://github.com/0xa3k5/web3icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/0xa3k5/web3icons/blob/main/LICENCE"}, "samples": ["bit", "dog", "eth", "mtd", "rune", "vite"], "height": 24, "category": "Logos", "palette": true}, "cryptocurrency": {"name": "Cryptocurrency Icons", "total": 483, "version": "0.18.1", "author": {"name": "<PERSON>", "url": "https://github.com/atomiclabs/cryptocurrency-icons"}, "license": {"title": "CC0 1.0", "spdx": "CC0-1.0", "url": "https://creativecommons.org/publicdomain/zero/1.0/"}, "samples": ["btc", "ltc", "eth", "storm", "waves", "vib"], "height": 32, "displayHeight": 16, "category": "Logos", "tags": [], "palette": false}, "cryptocurrency-color": {"name": "Cryptocurrency Color Icons", "total": 483, "version": "0.18.1", "author": {"name": "<PERSON>", "url": "https://github.com/atomiclabs/cryptocurrency-icons"}, "license": {"title": "CC0 1.0", "spdx": "CC0-1.0", "url": "https://creativecommons.org/publicdomain/zero/1.0/"}, "samples": ["btc", "ltc", "eth", "storm", "waves", "vib"], "height": 32, "displayHeight": 16, "category": "Logos", "tags": [], "palette": true}, "openmoji": {"name": "OpenMoji", "total": 4182, "author": {"name": "OpenMoji", "url": "https://github.com/hfg-gmuend/openmoji"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["bicycle", "bow-and-arrow", "full-moon-face", "plus", "solar-cell", "battery"], "height": 36, "displayHeight": 18, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": true}, "twemoji": {"name": "Twitter Emoji", "total": 3668, "author": {"name": "Twitter", "url": "https://github.com/twitter/twemoji"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["anguished-face", "duck", "bell", "spoon", "clipboard", "wrapped-gift"], "height": 36, "displayHeight": 18, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes"], "palette": true}, "noto": {"name": "<PERSON><PERSON>", "total": 3562, "author": {"name": "Google Inc", "url": "https://github.com/googlefonts/noto-emoji"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/googlefonts/noto-emoji/blob/main/svg/LICENSE"}, "samples": ["beaming-face-with-smiling-eyes", "computer-mouse", "chart-increasing", "dove", "page-facing-up", "red-heart"], "height": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "fluent-emoji": {"name": "Fluent <PERSON><PERSON>ji", "total": 3126, "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui-emoji"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE"}, "samples": ["avocado", "ticket", "straight-ruler", "diamond-suit", "egg", "yin-yang"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": true}, "fluent-emoji-flat": {"name": "Fluent Emoji Flat", "total": 3145, "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui-emoji"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE"}, "samples": ["avocado", "ticket", "straight-ruler", "diamond-suit", "egg", "yin-yang"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "fluent-emoji-high-contrast": {"name": "Fluent Emoji High Contrast", "total": 1595, "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui-emoji"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE"}, "samples": ["avocado", "ticket", "straight-ruler", "diamond-suit", "egg", "yin-yang"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "noto-v1": {"name": "<PERSON><PERSON> (v1)", "total": 2162, "author": {"name": "Google Inc", "url": "https://github.com/googlefonts/noto-emoji"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/googlefonts/noto-emoji/blob/main/svg/LICENSE"}, "samples": ["face-with-open-mouth", "no-entry", "scissors", "card-index-dividers", "key", "magnifying-glass-tilted-left"], "height": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "emojione": {"name": "Emoji One (Colored)", "total": 1834, "version": "2.3.0", "author": {"name": "Emoji One", "url": "https://github.com/EmojiTwo/emojitwo"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["anxious-face-with-sweat", "cloud-with-snow", "studio-microphone", "antenna-bars", "eight-spoked-asterisk", "no-entry"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "emojione-monotone": {"name": "Emoji One (Monotone)", "total": 1403, "version": "2.2.7", "author": {"name": "Emoji One", "url": "https://github.com/EmojiTwo/emojitwo"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["face-with-tongue", "envelope", "frog-face", "star", "droplet", "gem-stone"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "emojione-v1": {"name": "Emoji One (v1)", "total": 1262, "version": "1.5.2", "author": {"name": "Emoji One", "url": "https://github.com/joypixels/emojione-legacy"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["face-savoring-food", "panda-face", "artist-palette", "lightning-mood", "droplet", "folder"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": [], "palette": true}, "fxemoji": {"name": "Firefox OS Emoji", "total": 1034, "version": "0.0.2", "author": {"name": "Mozilla", "url": "https://github.com/mozilla/fxemoji"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://mozilla.github.io/fxemoji/LICENSE.md"}, "samples": ["foxweary", "loveletter", "openlock", "blackdiamondsuit", "speaker", "screen"], "height": 32, "displayHeight": 16, "category": "<PERSON><PERSON><PERSON>", "tags": [], "palette": true}, "streamline-emojis": {"name": "Streamline Emojis", "total": 787, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["crescent-moon", "fire", "kissing-face-with-smiling-eyes", "paperclip", "cloud-1", "balloon"], "height": 24, "category": "<PERSON><PERSON><PERSON>", "palette": true}, "circle-flags": {"name": "Circle Flags", "total": 632, "version": "1.0.0", "author": {"name": "HatScripts", "url": "https://github.com/HatScripts/circle-flags"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/HatScripts/circle-flags/blob/gh-pages/LICENSE"}, "samples": ["ee", "klingon", "jp", "pl", "fr", "de"], "height": 32, "displayHeight": 16, "category": "Flags / Maps", "palette": true}, "flag": {"name": "Flag Icons", "total": 542, "version": "7.5.0", "author": {"name": "Panay<PERSON><PERSON>", "url": "https://github.com/lipis/flag-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/lipis/flag-icons/blob/main/LICENSE"}, "samples": ["fr-1x1", "de-1x1", "bh-1x1", "kr-1x1", "ee-1x1", "bg-1x1"], "category": "Flags / Maps", "tags": ["Uses Stroke"], "palette": true}, "flagpack": {"name": "Flagpack", "total": 255, "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Yummygum/flagpack-core"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/Yummygum/flagpack-core/blob/main/LICENSE"}, "samples": ["ci", "gb-ukm", "wf", "de", "ie", "th"], "height": 24, "category": "Flags / Maps", "tags": [], "palette": true}, "cif": {"name": "CoreUI Flags", "total": 199, "version": "2.0.1", "author": {"name": "creative<PERSON><PERSON><PERSON>", "url": "https://github.com/coreui/coreui-icons"}, "license": {"title": "CC0 1.0", "spdx": "CC0-1.0", "url": "https://creativecommons.org/publicdomain/zero/1.0/"}, "samples": ["ee", "ca", "sk", "pl", "fr", "de"], "category": "Flags / Maps", "tags": [], "palette": true}, "gis": {"name": "Font-GIS", "total": 367, "version": "1.0.6", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/viglino/font-gis"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/Viglino/font-gis/blob/main/LICENSE-CC-BY.md"}, "samples": ["layer-o", "poi-o", "bbox", "flag-start", "location-arrow-o", "polygon"], "category": "Flags / Maps", "palette": false}, "map": {"name": "Map Icons", "total": 167, "version": "3.0.2", "author": {"name": "<PERSON>", "url": "https://github.com/scottdejonge/map-icons"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["restaurant", "real-estate-agency", "wheelchair", "storage", "park", "store"], "height": 25, "category": "Flags / Maps", "tags": ["Precise Shapes"], "palette": false}, "geo": {"name": "GeoGlyphs", "total": 30, "version": "0.0.10", "author": {"name": "<PERSON>", "url": "https://github.com/cugos/geoglyphs"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/cugos/geoglyphs/blob/main/LICENSE.md"}, "samples": ["turf-center", "turf-erased", "turf-point-on-line", "turf-destination", "turf-envelope", "turf-along"], "category": "Flags / Maps", "tags": ["<PERSON>"], "palette": false}, "game-icons": {"name": "Game Icons", "total": 4123, "author": {"name": "GameIcons", "url": "https://github.com/game-icons/icons"}, "license": {"title": "CC BY 3.0", "spdx": "CC-BY-3.0", "url": "https://github.com/game-icons/icons/blob/master/license.txt"}, "samples": ["diamond-trophy", "thrown-spear", "rank-3", "stairs", "table", "soda-can"], "category": "Thematic", "tags": ["<PERSON>"], "palette": false}, "fad": {"name": "FontAudio", "total": 155, "author": {"name": "@fefanto", "url": "https://github.com/fefanto/fontaudio"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["shuffle", "headphones", "rew", "cutter", "mono", "punch-in"], "height": 16, "category": "Thematic", "tags": ["<PERSON>"], "palette": false}, "academicons": {"name": "Academicons", "total": 158, "version": "1.9.4", "author": {"name": "<PERSON>", "url": "https://github.com/jpswalsh/academicons"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["conversation", "crossref-square", "stackoverflow", "acclaim", "hypothesis", "inspire"], "height": 32, "displayHeight": 16, "category": "Thematic", "tags": [], "palette": false}, "wi": {"name": "Weather Icons", "total": 219, "version": "2.0.12", "author": {"name": "<PERSON>", "url": "https://github.com/erikflowers/weather-icons"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["day-hail", "barometer", "day-windy", "moon-waning-crescent-2", "direction-down-right", "lightning"], "height": 30, "category": "Thematic", "tags": ["<PERSON>"], "palette": false}, "meteocons": {"name": "Meteocons", "total": 447, "version": "3.0.0", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/basmilius/weather-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/basmilius/weather-icons/blob/dev/LICENSE"}, "samples": ["hurricane-fill", "sunrise-fill", "windsock", "moon-first-quarter", "code-red", "raindrop-fill"], "height": 32, "displayHeight": 16, "category": "Thematic", "tags": ["Uses Stroke", "Contains Animations"], "palette": true}, "healthicons": {"name": "Health Icons", "total": 2000, "version": "2.0.0", "author": {"name": "Resolve to Save Lives", "url": "https://github.com/resolvetosavelives/healthicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/resolvetosavelives/healthicons/blob/main/LICENSE"}, "samples": ["cold-chain", "emergency-post", "asthma-outline", "exercise", "health-alt", "heart"], "height": 24, "category": "Thematic", "tags": ["Precise Shapes"], "palette": false}, "medical-icon": {"name": "Medical Icons", "total": 144, "version": "1.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/samcome/webfont-medical-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/samcome/webfont-medical-icons/blob/master/LICENSE"}, "samples": ["i-care-staff-area", "i-nursery", "immunizations", "i-chapel", "stairs", "health-services"], "category": "Thematic", "palette": false}, "covid": {"name": "<PERSON><PERSON>", "total": 142, "author": {"name": "Streamline", "url": "https://github.com/webalys-hq/streamline-vectors"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["social-distancing-correct-2", "personal-hygiene-clean-toothpaste", "vaccine-protection-medicine-pill", "social-distancing-not-allowed-space-man", "graph-cured-increasing", "social-distancing-attention"], "height": 24, "category": "Thematic", "tags": ["Uses Stroke"], "palette": false}, "la": {"name": "Line Awesome", "total": 1544, "version": "1.2.1", "author": {"name": "Icons8", "url": "https://github.com/icons8/line-awesome"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}, "samples": ["archive-solid", "female-solid", "check-circle", "bars", "signal", "columns"], "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "eva": {"name": "Eva Icons", "total": 490, "version": "1.1.3", "author": {"name": "Akveo", "url": "https://github.com/akveo/eva-icons/"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/akveo/eva-icons/blob/master/LICENSE.txt"}, "samples": ["droplet-off-outline", "flash-fill", "printer-outline", "layout-fill", "radio-button-off-outline", "person-fill"], "height": 24, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "dashicons": {"name": "Dashicons", "total": 342, "version": "0.9.0", "author": {"name": "WordPress", "url": "https://github.com/WordPress/dashicons"}, "license": {"title": "GPL", "spdx": "GPL-2.0-or-later", "url": "https://github.com/WordPress/dashicons/blob/master/gpl.txt"}, "samples": ["shortcode", "businessperson", "editor-expand", "sort", "category", "columns"], "height": 20, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "flat-color-icons": {"name": "Flat Color Icons", "total": 329, "version": "1.0.2", "author": {"name": "Icons8", "url": "https://github.com/icons8/flat-Color-icons"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["edit-image", "donate", "planner", "expand", "music", "checkmark"], "height": 48, "displayHeight": 24, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": true}, "entypo": {"name": "Entypo+", "total": 321, "author": {"name": "<PERSON>", "url": "https://github.com/chancancode/entypo-plus"}, "license": {"title": "CC BY-SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["bell", "image", "erase", "select-arrows", "bookmark", "swap"], "height": 20, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "foundation": {"name": "Foundation", "total": 283, "version": "3.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/zurb/foundation-icon-fonts"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["graph-trend", "indent-more", "lock", "contrast", "page", "mobile"], "category": "Archive / Unmaintained", "tags": ["<PERSON>"], "palette": false}, "raphael": {"name": "<PERSON>", "total": 266, "author": {"name": "<PERSON>", "url": "https://github.com/d<PERSON><PERSON><PERSON>novskiy/raphael"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["home", "cloud", "parent", "inbox", "volume0", "check"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["<PERSON>"], "palette": false}, "icons8": {"name": "Icons8 Windows 10 Icons", "total": 234, "version": "1.0.0", "author": {"name": "Icons8", "url": "https://github.com/icons8/windows-10-icons"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["checked", "create-new", "group", "columns", "angle-left", "monitor"], "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "iwwa": {"name": "Innowatio Font", "total": 105, "version": "1.1.3", "author": {"name": "Innowatio", "url": "https://github.com/innowatio/iwwa-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}, "samples": ["tag", "settings", "connection-o", "flag", "option", "menu"], "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "gala": {"name": "Gala Icons", "total": 51, "author": {"name": "<PERSON>", "url": "https://github.com/cyberalien/gala-icons"}, "license": {"title": "GPL", "spdx": "GPL-3.0", "url": "https://github.com/cyberalien/gala-icons/blob/main/LICENSE"}, "samples": ["brochure", "remove", "chart", "issue", "multi", "video"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "heroicons-outline": {"name": "HeroIcons v1 Outline", "total": 230, "version": "1.0.6", "author": {"name": "Refactoring UI Inc", "url": "https://github.com/tailwindlabs/heroicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/tailwindlabs/heroicons/blob/master/LICENSE"}, "samples": ["color-swatch", "library", "receipt-refund", "bookmark", "cloud", "folder"], "height": 24, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false}, "heroicons-solid": {"name": "HeroIcons v1 Solid", "total": 230, "version": "1.0.6", "author": {"name": "Refactoring UI Inc", "url": "https://github.com/tailwindlabs/heroicons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/tailwindlabs/heroicons/blob/master/LICENSE"}, "samples": ["color-swatch", "library", "receipt-refund", "bookmark", "cloud", "folder"], "height": 20, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "<PERSON>"], "palette": false}, "fa-solid": {"name": "Font Awesome 5 Solid", "total": 1001, "version": "5.15.4", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["search-plus", "paste", "comment-dots", "bookmark", "egg", "volume-off"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": [], "palette": false}, "fa-regular": {"name": "Font Awesome 5 Regular", "total": 151, "version": "5.15.4", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["bell", "comment", "hand-point-left", "bookmark", "arrow-alt-circle-down", "file"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": [], "palette": false}, "fa-brands": {"name": "Font Awesome 5 Brands", "total": 457, "version": "5.15.4", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["amazon", "cc-visa", "v<PERSON><PERSON><PERSON>", "chrome", "strava", "microsoft"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": [], "palette": false}, "fa": {"name": "Font Awesome 4", "total": 678, "version": "4.7.0", "author": {"name": "<PERSON>", "url": "https://github.com/FortAwesome/Font-Awesome/tree/fa-4"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["wrench", "bell-o", "user-o", "area-chart", "play", "css3"], "category": "Archive / Unmaintained", "tags": [], "palette": false}, "fluent-mdl2": {"name": "Fluent UI MDL2", "total": 1735, "author": {"name": "Microsoft Corporation", "url": "https://github.com/microsoft/fluentui/tree/master/packages/react-icons-mdl2"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/microsoft/fluentui/blob/master/packages/react-icons-mdl2/LICENSE"}, "samples": ["flow", "home", "switch", "caret-solid-16", "accept", "wind-direction"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "fontisto": {"name": "Fontisto", "total": 615, "version": "3.0.4", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/kenangundogan/fontisto"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/kenangundogan/fontisto/blob/master/LICENSE"}, "samples": ["prescription", "heartbeat-alt", "rain", "navigate", "filter", "home"], "height": 24, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "icomoon-free": {"name": "IcoMoon Free", "total": 491, "author": {"name": "Keyamoon", "url": "https://github.com/Keyamoon/IcoMoon-Free"}, "license": {"title": "GPL", "spdx": "GPL-3.0-or-later", "url": "https://www.gnu.org/licenses/gpl.html"}, "samples": ["bubbles3", "forward", "volume-medium", "play3", "folder", "bookmark"], "height": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "subway": {"name": "Subway Icon Set", "total": 306, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/ma<PERSON><PERSON><PERSON><PERSON>/subway"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["call-2", "power-batton", "admin", "backward", "subtraction-1", "file"], "height": 16, "category": "Archive / Unmaintained", "palette": false}, "oi": {"name": "Open Iconic", "total": 223, "version": "1.1.1", "author": {"name": "Iconic", "url": "https://github.com/iconic/open-iconic"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/iconic/open-iconic/blob/master/ICON-LICENSE"}, "samples": ["bug", "bullhorn", "chat", "media-play", "eject", "location"], "height": 8, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "wpf": {"name": "Icons8 Windows 8 Icons", "total": 200, "author": {"name": "Icons8", "url": "https://github.com/icons8/WPF-UI-Framework"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["check-file", "add-image", "geo-fence", "clapperboard", "message", "building"], "height": 26, "category": "Archive / Unmaintained", "palette": false}, "simple-line-icons": {"name": "Simple line icons", "total": 189, "version": "2.5.5", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/thesabbir/simple-line-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/thesabbir/simple-line-icons/blob/master/LICENSE.md"}, "samples": ["bubbles", "camrecorder", "cloud-upload", "folder", "arrow-left", "docs"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "et": {"name": "Elegant", "total": 100, "version": "1.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/pprince/etlinefont-bower"}, "license": {"title": "GPL 3.0", "spdx": "GPL-3.0-or-later", "url": "https://www.gnu.org/licenses/gpl.html"}, "samples": ["profile-female", "ribbon", "layers", "tablet", "scope", "cloud"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "el": {"name": "Elusive Icons", "total": 304, "version": "2.0.0", "author": {"name": "Team Redux", "url": "https://github.com/dovy/elusive-icons"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["headphones", "cog", "user", "play", "bookmark", "delicious"], "category": "Archive / Unmaintained", "tags": [], "palette": false}, "vaadin": {"name": "<PERSON><PERSON><PERSON>", "total": 636, "version": "4.3.2", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vaadin/web-components"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0"}, "samples": ["area-select", "file-picture", "plus-circle-o", "angle-up", "eject", "enter-arrow"], "height": 32, "displayHeight": 16, "category": "Archive / Unmaintained", "tags": ["Precise Shapes"], "palette": false}, "grommet-icons": {"name": "Grommet Icons", "total": 634, "version": "4.12.1", "author": {"name": "Grommet", "url": "https://github.com/grommet/grommet-icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}, "samples": ["user-expert", "action", "home", "more-vertical", "waypoint", "terminal"], "height": 24, "category": "Archive / Unmaintained", "tags": ["Precise Shapes", "Uses Stroke"], "palette": false}, "whh": {"name": "WebHostingHub Glyphs", "total": 2125, "author": {"name": "WebHostingHub"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["addtags", "brightness", "circlecallincoming"], "palette": false, "hidden": true}, "si-glyph": {"name": "SmartIcons Glyph", "total": 799, "version": "0.0.2", "author": {"name": "SmartIcons"}, "license": {"title": "CC BY SA 4.0", "spdx": "CC-BY-SA-4.0", "url": "https://creativecommons.org/licenses/by-sa/4.0/"}, "samples": ["circle-load-left", "basket-arrow-right", "slide-show"], "palette": false, "hidden": true}, "zmdi": {"name": "Material Design Iconic Font", "total": 777, "author": {"name": "MDI Community", "url": "https://github.com/zavoloklom/material-design-iconic-font"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1"}, "samples": ["alarm-snooze", "cloud-off", "library"], "palette": false, "hidden": true}, "ls": {"name": "Ligature Symbols", "total": 348, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kudakurage/LigatureSymbols"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["bad", "search", "bag"], "tags": [], "palette": false, "hidden": true}, "bpmn": {"name": "BPMN", "total": 112, "version": "0.10.0", "author": {"name": "Camunda Services GmbH", "url": "https://github.com/bpmn-io/bpmn-font"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://github.com/bpmn-io/bpmn-font/blob/master/LICENSE"}, "samples": ["intermediate-event-catch-non-interrupting-escalation", "user", "lane-insert-above"], "displayHeight": 24, "tags": ["<PERSON>"], "palette": false, "hidden": true}, "flat-ui": {"name": "Flat UI Icons", "total": 100, "author": {"name": "Designmodo, Inc.", "url": "https://github.com/designmodo/Flat-UI"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/designmodo/Flat-UI/blob/master/LICENSE"}, "samples": ["map", "graph", "imac"], "tags": [], "palette": true, "hidden": true}, "vs": {"name": "<PERSON><PERSON><PERSON>", "total": 159, "version": "5.11.2", "author": {"name": "TableCheck", "url": "https://github.com/kkvesper/vesper-icons"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["edit-page", "kakao-square", "person"], "palette": false, "hidden": true}, "topcoat": {"name": "TopCoat Icons", "total": 89, "version": "0.2.0", "author": {"name": "TopCoat", "url": "https://github.com/topcoat/icons"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/topcoat/icons/blob/master/LICENSE"}, "samples": ["wifi", "feedback", "pencil"], "palette": false, "hidden": true}, "il": {"name": "Icalicons", "total": 84, "version": "0.0.1", "author": {"name": "Icalia Labs"}, "license": {"title": "MIT", "spdx": "MIT"}, "samples": ["calendar", "users", "conversation"], "tags": [], "palette": false, "hidden": true}, "websymbol": {"name": "Web Symbols Liga", "total": 85, "author": {"name": "Just Be Nice studio"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL"}, "samples": ["clock", "resize-full-circle", "tag"], "palette": false, "hidden": true}, "fontelico": {"name": "Fontelico", "total": 34, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/fontello/fontelico.font"}, "license": {"title": "CC BY SA", "spdx": "CC-BY-SA-3.0", "url": "https://creativecommons.org/licenses/by-sa/3.0/"}, "samples": ["spin5", "emo-sunglasses", "crown-plus"], "displayHeight": 20, "tags": [], "palette": false, "hidden": true}, "ps": {"name": "PrestaShop Icons", "total": 479, "author": {"name": "PrestaShop", "url": "https://github.com/PrestaShop/prestashop-icon-font"}, "license": {"title": "CC BY-NC 4.0", "spdx": "CC-BY-NC-4.0", "url": "https://creativecommons.org/licenses/by-nc/4.0/"}, "samples": ["bell", "girl", "home"], "palette": false, "hidden": true}, "feather": {"name": "Feather Icons", "total": 286, "author": {"name": "<PERSON>", "url": "https://github.com/feathericons/feather"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/feathericons/feather/blob/master/LICENSE"}, "samples": ["check-circle", "award", "home"], "height": 24, "tags": ["Precise Shapes", "<PERSON>", "Uses Stroke"], "palette": false, "hidden": true}, "mono-icons": {"name": "Mono Icons", "total": 180, "version": "1.3.1", "author": {"name": "Mono", "url": "https://github.com/mono-company/mono-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/mono-company/mono-icons/blob/master/LICENSE.md"}, "samples": ["user", "log-in", "play"], "height": 24, "palette": false, "hidden": true}, "pepicons": {"name": "Pepicons", "total": 428, "version": "1.2.7", "author": {"name": "CyCraft", "url": "https://github.com/CyCraft/pepicons"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://github.com/CyCraft/pepicons/blob/dev/LICENSE"}, "samples": ["bookmark-print", "moon", "pen-print"], "displayHeight": 20, "palette": false, "hidden": true}}