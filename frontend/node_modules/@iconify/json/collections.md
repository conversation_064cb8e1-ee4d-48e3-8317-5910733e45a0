# List of icon sets


## Material Symbols
* Number of icons: 14195
* Author: Google
* URL: https://github.com/google/material-design-icons
* License: Apache 2.0
* License URL: https://github.com/google/material-design-icons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `material-symbols`
* File: [json/material-symbols.json](json/material-symbols.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Material Symbols Light
* Number of icons: 14263
* Author: Google
* URL: https://github.com/google/material-design-icons
* License: Apache 2.0
* License URL: https://github.com/google/material-design-icons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `material-symbols-light`
* File: [json/material-symbols-light.json](json/material-symbols-light.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Google Material Icons
* Number of icons: 10955
* Author: Material Design Authors
* URL: https://github.com/material-icons/material-icons
* License: Apache 2.0
* License URL: https://github.com/material-icons/material-icons/blob/master/LICENSE
* Version: 1.0.32
* Palette: Colorless
* Icon set prefix: `ic`
* File: [json/ic.json](json/ic.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Material Design Icons
* Number of icons: 7447
* Author: Pictogrammers
* URL: https://github.com/Templarian/MaterialDesign
* License: Apache 2.0
* License URL: https://github.com/Templarian/MaterialDesign/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `mdi`
* File: [json/mdi.json](json/mdi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Material Design Light
* Number of icons: 284
* Author: Pictogrammers
* URL: https://github.com/Templarian/MaterialDesignLight
* License: Open Font License
* License URL: https://github.com/Templarian/MaterialDesignLight/blob/master/LICENSE.md
* Palette: Colorless
* Icon set prefix: `mdi-light`
* File: [json/mdi-light.json](json/mdi-light.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Material Line Icons
* Number of icons: 1091
* Author: Vjacheslav Trushkin
* URL: https://github.com/cyberalien/line-md
* License: MIT
* License URL: https://github.com/cyberalien/line-md/blob/master/license.txt
* Version: 2.0.8
* Palette: Colorless
* Icon set prefix: `line-md`
* File: [json/line-md.json](json/line-md.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Solar
* Number of icons: 7401
* Author: 480 Design
* URL: https://www.figma.com/community/file/1166831539721848736
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `solar`
* File: [json/solar.json](json/solar.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Tabler Icons
* Number of icons: 5944
* Author: Paweł Kuna
* URL: https://github.com/tabler/tabler-icons
* License: MIT
* License URL: https://github.com/tabler/tabler-icons/blob/master/LICENSE
* Version: 3.34.0
* Palette: Colorless
* Icon set prefix: `tabler`
* File: [json/tabler.json](json/tabler.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Huge Icons
* Number of icons: 4523
* Author: Hugeicons
* URL: https://icon-sets.iconify.design/icon-sets/hugeicons/
* License: MIT
* Palette: Colorless
* Icon set prefix: `hugeicons`
* File: [json/hugeicons.json](json/hugeicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## MingCute Icon
* Number of icons: 3098
* Author: MingCute Design
* URL: https://github.com/Richard9394/MingCute
* License: Apache 2.0
* License URL: https://github.com/Richard9394/MingCute/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `mingcute`
* File: [json/mingcute.json](json/mingcute.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Remix Icon
* Number of icons: 3058
* Author: Remix Design
* URL: https://github.com/Remix-Design/RemixIcon
* License: Apache 2.0
* License URL: https://github.com/Remix-Design/RemixIcon/blob/master/License
* Version: 4.6.0
* Palette: Colorless
* Icon set prefix: `ri`
* File: [json/ri.json](json/ri.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Myna UI Icons
* Number of icons: 2468
* Author: Praveen Juge
* URL: https://github.com/praveenjuge/mynaui-icons
* License: MIT
* License URL: https://github.com/praveenjuge/mynaui-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `mynaui`
* File: [json/mynaui.json](json/mynaui.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IconaMoon
* Number of icons: 1781
* Author: Dariush Habibpour
* URL: https://github.com/dariushhpg1/IconaMoon
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `iconamoon`
* File: [json/iconamoon.json](json/iconamoon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Iconoir
* Number of icons: 1671
* Author: Luca Burgio
* URL: https://github.com/iconoir-icons/iconoir
* License: MIT
* License URL: https://github.com/iconoir-icons/iconoir/blob/main/LICENSE
* Version: 7.11.0
* Palette: Colorless
* Icon set prefix: `iconoir`
* File: [json/iconoir.json](json/iconoir.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Lucide
* Number of icons: 1599
* Author: Lucide Contributors
* URL: https://github.com/lucide-icons/lucide
* License: ISC
* License URL: https://github.com/lucide-icons/lucide/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `lucide`
* File: [json/lucide.json](json/lucide.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Lucide Lab
* Number of icons: 373
* Author: Lucide Contributors
* URL: https://github.com/lucide-icons/lucide-lab
* License: ISC
* License URL: https://github.com/lucide-icons/lucide-lab/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `lucide-lab`
* File: [json/lucide-lab.json](json/lucide-lab.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Unicons
* Number of icons: 1215
* Author: Iconscout
* URL: https://github.com/Iconscout/unicons
* License: Apache 2.0
* License URL: https://github.com/Iconscout/unicons/blob/master/LICENSE
* Version: 4.2.0
* Palette: Colorless
* Icon set prefix: `uil`
* File: [json/uil.json](json/uil.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## TDesign Icons
* Number of icons: 2124
* Author: TDesign
* URL: https://github.com/Tencent/tdesign-icons
* License: MIT
* License URL: https://github.com/Tencent/tdesign-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `tdesign`
* File: [json/tdesign.json](json/tdesign.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Sargam Icons
* Number of icons: 924
* Author: Abhimanyu Rana
* URL: https://github.com/planetabhi/sargam-icons
* License: MIT
* License URL: https://github.com/planetabhi/sargam-icons/blob/main/LICENSE.txt
* Palette: Colorless
* Icon set prefix: `si`
* File: [json/si.json](json/si.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## BoxIcons
* Number of icons: 814
* Author: Atisa
* URL: https://github.com/atisawd/boxicons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.1.4
* Palette: Colorless
* Icon set prefix: `bx`
* File: [json/bx.json](json/bx.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## BoxIcons Solid
* Number of icons: 665
* Author: Atisa
* URL: https://github.com/atisawd/boxicons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.1.4
* Palette: Colorless
* Icon set prefix: `bxs`
* File: [json/bxs.json](json/bxs.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Majesticons
* Number of icons: 760
* Author: Gerrit Halfmann
* URL: https://github.com/halfmage/majesticons
* License: MIT
* License URL: https://github.com/halfmage/majesticons/blob/main/LICENSE
* Version: 2.1.2
* Palette: Colorless
* Icon set prefix: `majesticons`
* File: [json/majesticons.json](json/majesticons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## css.gg
* Number of icons: 704
* Author: Astrit
* URL: https://github.com/astrit/css.gg
* License: MIT
* License URL: https://github.com/astrit/css.gg/blob/master/LICENSE
* Version: 2.1.4
* Palette: Colorless
* Icon set prefix: `gg`
* File: [json/gg.json](json/gg.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flowbite Icons
* Number of icons: 751
* Author: Themesberg
* URL: https://github.com/themesberg/flowbite-icons
* License: MIT
* License URL: https://github.com/themesberg/flowbite-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `flowbite`
* File: [json/flowbite.json](json/flowbite.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Basil
* Number of icons: 493
* Author: Craftwork
* URL: https://www.figma.com/community/file/931906394678748246
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `basil`
* File: [json/basil.json](json/basil.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pixelarticons
* Number of icons: 486
* Author: Gerrit Halfmann
* URL: https://github.com/halfmage/pixelarticons
* License: MIT
* License URL: https://github.com/halfmage/pixelarticons/blob/master/LICENSE
* Version: 1.8.1
* Palette: Colorless
* Icon set prefix: `pixelarticons`
* File: [json/pixelarticons.json](json/pixelarticons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pixel Icon
* Number of icons: 450
* Author: HackerNoon
* URL: https://github.com/hackernoon/pixel-icon-library
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `pixel`
* File: [json/pixel.json](json/pixel.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Akar Icons
* Number of icons: 454
* Author: Arturo Wibawa
* URL: https://github.com/artcoholic/akar-icons
* License: MIT
* License URL: https://github.com/artcoholic/akar-icons/blob/master/LICENSE
* Version: 1.9.31
* Palette: Colorless
* Icon set prefix: `akar-icons`
* File: [json/akar-icons.json](json/akar-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## coolicons
* Number of icons: 442
* Author: Kryston Schwarze
* URL: https://github.com/krystonschwarze/coolicons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `ci`
* File: [json/ci.json](json/ci.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## ProIcons
* Number of icons: 518
* Author: ProCode
* URL: https://github.com/ProCode-Software/proicons
* License: MIT
* License URL: https://github.com/ProCode-Software/proicons/blob/main/LICENSE
* Version: 4.12.2
* Palette: Colorless
* Icon set prefix: `proicons`
* File: [json/proicons.json](json/proicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Typicons
* Number of icons: 336
* Author: Stephen Hutchings
* URL: https://github.com/stephenhutchings/typicons.font
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Version: 2.1.2
* Palette: Colorless
* Icon set prefix: `typcn`
* File: [json/typcn.json](json/typcn.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Meteor Icons
* Number of icons: 321
* Author: zkreations
* URL: https://github.com/zkreations/icons
* License: MIT
* License URL: https://github.com/zkreations/icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `meteor-icons`
* File: [json/meteor-icons.json](json/meteor-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Prime Icons
* Number of icons: 313
* Author: PrimeTek
* URL: https://github.com/primefaces/primeicons
* License: MIT
* License URL: https://github.com/primefaces/primeicons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `prime`
* File: [json/prime.json](json/prime.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Circum Icons
* Number of icons: 288
* Author: Klarr Agency
* URL: https://github.com/Klarr-Agency/Circum-Icons
* License: Mozilla Public License 2.0
* License URL: https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE
* Version: 1.0.0
* Palette: Colorless
* Icon set prefix: `circum`
* File: [json/circum.json](json/circum.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Feather Icon
* Number of icons: 255
* Author: Megumi Hano
* URL: https://github.com/feathericon/feathericon
* License: MIT
* License URL: https://github.com/feathericon/feathericon/blob/master/LICENSE
* Version: 1.0.2
* Palette: Colorless
* Icon set prefix: `fe`
* File: [json/fe.json](json/fe.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## EOS Icons
* Number of icons: 253
* Author: SUSE UX/UI team
* URL: https://gitlab.com/SUSE-UIUX/eos-icons
* License: MIT
* License URL: https://gitlab.com/SUSE-UIUX/eos-icons/-/blob/master/LICENSE
* Version: 5.4.0
* Palette: Colorless
* Icon set prefix: `eos-icons`
* File: [json/eos-icons.json](json/eos-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Bitcoin Icons
* Number of icons: 250
* Author: Bitcoin Design Community
* URL: https://github.com/BitcoinDesign/Bitcoin-Icons
* License: MIT
* License URL: https://github.com/BitcoinDesign/Bitcoin-Icons/blob/main/LICENSE-MIT
* Version: 0.1.10
* Palette: Colorless
* Icon set prefix: `bitcoin-icons`
* File: [json/bitcoin-icons.json](json/bitcoin-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Humbleicons
* Number of icons: 253
* Author: Jiří Zralý
* URL: https://github.com/zraly/humbleicons
* License: MIT
* License URL: https://github.com/zraly/humbleicons/blob/master/license
* Version: 1.16.0
* Palette: Colorless
* Icon set prefix: `humbleicons`
* File: [json/humbleicons.json](json/humbleicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Unicons Monochrome
* Number of icons: 298
* Author: Iconscout
* URL: https://github.com/Iconscout/unicons
* License: Apache 2.0
* License URL: https://github.com/Iconscout/unicons/blob/master/LICENSE
* Version: 4.2.0
* Palette: Colorless
* Icon set prefix: `uim`
* File: [json/uim.json](json/uim.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Unicons Thin Line
* Number of icons: 216
* Author: Iconscout
* URL: https://github.com/Iconscout/unicons
* License: Apache 2.0
* License URL: https://github.com/Iconscout/unicons/blob/master/LICENSE
* Version: 4.2.0
* Palette: Colorless
* Icon set prefix: `uit`
* File: [json/uit.json](json/uit.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Unicons Solid
* Number of icons: 190
* Author: Iconscout
* URL: https://github.com/Iconscout/unicons
* License: Apache 2.0
* License URL: https://github.com/Iconscout/unicons/blob/master/LICENSE
* Version: 4.2.0
* Palette: Colorless
* Icon set prefix: `uis`
* File: [json/uis.json](json/uis.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Gridicons
* Number of icons: 207
* Author: Automattic
* URL: https://github.com/Automattic/gridicons
* License: GPL 2.0
* License URL: https://github.com/Automattic/gridicons/blob/trunk/LICENSE.md
* Version: 3.4.2
* Palette: Colorless
* Icon set prefix: `gridicons`
* File: [json/gridicons.json](json/gridicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Mono Icons
* Number of icons: 180
* Author: Mono
* URL: https://github.com/mono-company/mono-icons
* License: MIT
* License URL: https://github.com/mono-company/mono-icons/blob/master/LICENSE.md
* Version: 1.3.1
* Palette: Colorless
* Icon set prefix: `mi`
* File: [json/mi.json](json/mi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Cuida Icons
* Number of icons: 180
* Author: Sysvale
* URL: https://github.com/Sysvale/cuida-icons
* License: Apache 2.0
* License URL: https://github.com/Sysvale/cuida-icons/blob/main/LICENSE
* Version: 1.17.0
* Palette: Colorless
* Icon set prefix: `cuida`
* File: [json/cuida.json](json/cuida.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## WeUI Icon
* Number of icons: 162
* Author: WeUI
* URL: https://github.com/weui/weui-icon
* License: MIT
* Palette: Colorless
* Icon set prefix: `weui`
* File: [json/weui.json](json/weui.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Duoicons
* Number of icons: 91
* Author: fernandcf
* URL: https://github.com/fazdiu/duo-icons
* License: MIT
* License URL: https://github.com/fazdiu/duo-icons/blob/master/LICENSE
* Version: 1.1.4
* Palette: Colorless
* Icon set prefix: `duo-icons`
* File: [json/duo-icons.json](json/duo-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## SVG Spinners
* Number of icons: 46
* Author: Utkarsh Verma
* URL: https://github.com/n3r4zzurr0/svg-spinners
* License: MIT
* License URL: https://github.com/n3r4zzurr0/svg-spinners/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `svg-spinners`
* File: [json/svg-spinners.json](json/svg-spinners.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Lets Icons
* Number of icons: 1528
* Author: Leonid Tsvetkov
* URL: https://www.figma.com/community/file/886554014393250663
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `lets-icons`
* File: [json/lets-icons.json](json/lets-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Ultimate free icons
* Number of icons: 1999
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-ultimate`
* File: [json/streamline-ultimate.json](json/streamline-ultimate.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Plump free icons
* Number of icons: 1499
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-plump`
* File: [json/streamline-plump.json](json/streamline-plump.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Sharp free icons
* Number of icons: 1500
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-sharp`
* File: [json/streamline-sharp.json](json/streamline-sharp.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Mage Icons
* Number of icons: 1042
* Author: MageIcons
* URL: https://github.com/Mage-Icons/mage-icons
* License: Apache 2.0
* License URL: https://github.com/Mage-Icons/mage-icons/blob/main/License.txt
* Palette: Colorless
* Icon set prefix: `mage`
* File: [json/mage.json](json/mage.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Stash Icons
* Number of icons: 982
* Author: Pingback LLC
* URL: https://github.com/stash-ui/icons
* License: MIT
* License URL: https://github.com/stash-ui/icons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `stash`
* File: [json/stash.json](json/stash.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Lineicons
* Number of icons: 606
* Author: Lineicons
* URL: https://github.com/LineiconsHQ/Lineicons
* License: MIT
* License URL: https://github.com/LineiconsHQ/Lineicons/blob/main/LICENSE.md
* Palette: Colorless
* Icon set prefix: `lineicons`
* File: [json/lineicons.json](json/lineicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IconPark Outline
* Number of icons: 2658
* Author: ByteDance
* URL: https://github.com/bytedance/IconPark
* License: Apache 2.0
* License URL: https://github.com/bytedance/IconPark/blob/master/LICENSE
* Version: 1.4.2
* Palette: Colorless
* Icon set prefix: `icon-park-outline`
* File: [json/icon-park-outline.json](json/icon-park-outline.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IconPark Solid
* Number of icons: 1947
* Author: ByteDance
* URL: https://github.com/bytedance/IconPark
* License: Apache 2.0
* License URL: https://github.com/bytedance/IconPark/blob/master/LICENSE
* Version: 1.4.2
* Palette: Colorless
* Icon set prefix: `icon-park-solid`
* File: [json/icon-park-solid.json](json/icon-park-solid.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IconPark TwoTone
* Number of icons: 1944
* Author: ByteDance
* URL: https://github.com/bytedance/IconPark
* License: Apache 2.0
* License URL: https://github.com/bytedance/IconPark/blob/master/LICENSE
* Version: 1.4.2
* Palette: Colorless
* Icon set prefix: `icon-park-twotone`
* File: [json/icon-park-twotone.json](json/icon-park-twotone.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Jam Icons
* Number of icons: 940
* Author: Michael Amprimo
* URL: https://github.com/michaelampr
* License: MIT
* License URL: https://github.com/cyberalien/jam-backup/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `jam`
* File: [json/jam.json](json/jam.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Cyber free icons
* Number of icons: 500
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-cyber`
* File: [json/streamline-cyber.json](json/streamline-cyber.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Guidance
* Number of icons: 360
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `guidance`
* File: [json/guidance.json](json/guidance.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Carbon
* Number of icons: 2336
* Author: IBM
* URL: https://github.com/carbon-design-system/carbon/tree/main/packages/icons
* License: Apache 2.0
* Version: 11.61.0
* Palette: Colorless
* Icon set prefix: `carbon`
* File: [json/carbon.json](json/carbon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IonIcons
* Number of icons: 1357
* Author: Ben Sperry
* URL: https://github.com/ionic-team/ionicons
* License: MIT
* License URL: https://github.com/ionic-team/ionicons/blob/main/LICENSE
* Version: 8.0.9
* Palette: Colorless
* Icon set prefix: `ion`
* File: [json/ion.json](json/ion.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Famicons
* Number of icons: 1342
* Author: Family
* URL: https://github.com/familyjs/famicons
* License: MIT
* License URL: https://github.com/familyjs/famicons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `famicons`
* File: [json/famicons.json](json/famicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Ant Design Icons
* Number of icons: 830
* Author: HeskeyBaozi
* URL: https://github.com/ant-design/ant-design-icons
* License: MIT
* License URL: https://github.com/ant-design/ant-design-icons/blob/master/LICENSE
* Version: 4.4.2
* Palette: Colorless
* Icon set prefix: `ant-design`
* File: [json/ant-design.json](json/ant-design.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Lsicon
* Number of icons: 716
* Author: Wis Design
* URL: https://www.lsicon.com/
* License: MIT
* License URL: https://github.com/wisdesignsystem/lsicon/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `lsicon`
* File: [json/lsicon.json](json/lsicon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Gravity UI Icons
* Number of icons: 727
* Author: YANDEX LLC
* URL: https://github.com/gravity-ui/icons/
* License: MIT
* License URL: https://github.com/gravity-ui/icons/blob/main/LICENSE
* Version: 2.13.0
* Palette: Colorless
* Icon set prefix: `gravity-ui`
* File: [json/gravity-ui.json](json/gravity-ui.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## CoreUI Free
* Number of icons: 554
* Author: creativeLabs Łukasz Holeczek
* URL: https://github.com/coreui/coreui-icons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.0.1
* Palette: Colorless
* Icon set prefix: `cil`
* File: [json/cil.json](json/cil.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Element Plus
* Number of icons: 293
* Author: Element Plus
* URL: https://github.com/element-plus/element-plus-icons
* License: MIT
* License URL: https://github.com/element-plus/element-plus-icons/blob/main/packages/svg/package.json
* Version: 2.3.1
* Palette: Colorless
* Icon set prefix: `ep`
* File: [json/ep.json](json/ep.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Charm Icons
* Number of icons: 261
* Author: Jay Newey
* URL: https://github.com/jaynewey/charm-icons
* License: MIT
* License URL: https://github.com/jaynewey/charm-icons/blob/main/LICENSE
* Version: 0.12.1
* Palette: Colorless
* Icon set prefix: `charm`
* File: [json/charm.json](json/charm.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Quill Icons
* Number of icons: 140
* Author: Casper Lourens
* URL: https://www.figma.com/community/file/1034432054377533052/Quill-Iconset
* License: MIT
* License URL: https://github.com/yourtempo/tempo-quill-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `quill`
* File: [json/quill.json](json/quill.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Bytesize Icons
* Number of icons: 101
* Author: Dan Klammer
* URL: https://github.com/danklammer/bytesize-icons
* License: MIT
* License URL: https://github.com/danklammer/bytesize-icons/blob/master/LICENSE.md
* Version: 1.4.0
* Palette: Colorless
* Icon set prefix: `bytesize`
* File: [json/bytesize.json](json/bytesize.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Bootstrap Icons
* Number of icons: 2078
* Author: The Bootstrap Authors
* URL: https://github.com/twbs/icons
* License: MIT
* License URL: https://github.com/twbs/icons/blob/main/LICENSE.md
* Version: 1.13.1
* Palette: Colorless
* Icon set prefix: `bi`
* File: [json/bi.json](json/bi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pixel free icons
* Number of icons: 662
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-pixel`
* File: [json/streamline-pixel.json](json/streamline-pixel.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Streamline Block
* Number of icons: 300
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-block`
* File: [json/streamline-block.json](json/streamline-block.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Rivet Icons
* Number of icons: 210
* Author: Indiana University
* URL: https://github.com/indiana-university/rivet-icons
* License: BSD 3-Clause
* License URL: https://github.com/indiana-university/rivet-icons/blob/develop/LICENSE
* Palette: Colorless
* Icon set prefix: `rivet-icons`
* File: [json/rivet-icons.json](json/rivet-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Nimbus
* Number of icons: 140
* Author: Linkedstore S.A.
* URL: https://github.com/cyberalien/nimbus-icons
* License: MIT
* License URL: https://github.com/cyberalien/nimbus-icons/blob/main/LICENSE
* Version: 0.3.2
* Palette: Colorless
* Icon set prefix: `nimbus`
* File: [json/nimbus.json](json/nimbus.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## FormKit Icons
* Number of icons: 144
* Author: FormKit, Inc
* URL: https://github.com/formkit/formkit/tree/master/packages/icons
* License: MIT
* License URL: https://github.com/formkit/formkit/blob/master/packages/icons/LICENSE
* Palette: Colorless
* Icon set prefix: `formkit`
* File: [json/formkit.json](json/formkit.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent UI System Icons
* Number of icons: 18049
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui-system-icons
* License: MIT
* License URL: https://github.com/microsoft/fluentui-system-icons/blob/main/LICENSE
* Version: 1.1.302
* Palette: Colorless
* Icon set prefix: `fluent`
* File: [json/fluent.json](json/fluent.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Phosphor
* Number of icons: 9072
* Author: Phosphor Icons
* URL: https://github.com/phosphor-icons/core
* License: MIT
* License URL: https://github.com/phosphor-icons/core/blob/main/LICENSE
* Version: 2.1.1
* Palette: Colorless
* Icon set prefix: `ph`
* File: [json/ph.json](json/ph.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Teenyicons
* Number of icons: 1200
* Author: smhmd
* URL: https://github.com/teenyicons/teenyicons
* License: MIT
* License URL: https://github.com/teenyicons/teenyicons/blob/master/LICENSE
* Version: 0.4.1
* Palette: Colorless
* Icon set prefix: `teenyicons`
* File: [json/teenyicons.json](json/teenyicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Clarity
* Number of icons: 1103
* Author: VMware
* URL: https://github.com/vmware/clarity
* License: MIT
* License URL: https://github.com/vmware/clarity-assets/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `clarity`
* File: [json/clarity.json](json/clarity.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Freehand free icons
* Number of icons: 1000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-freehand`
* File: [json/streamline-freehand.json](json/streamline-freehand.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Siemens Industrial Experience Icons
* Number of icons: 1315
* Author: Siemens AG
* URL: https://github.com/siemens/ix-icons
* License: MIT
* License URL: https://github.com/siemens/ix-icons/blob/main/LICENSE.md
* Palette: Colorless
* Icon set prefix: `ix`
* File: [json/ix.json](json/ix.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Octicons
* Number of icons: 649
* Author: GitHub
* URL: https://github.com/primer/octicons/
* License: MIT
* License URL: https://github.com/primer/octicons/blob/main/LICENSE
* Version: 19.15.2
* Palette: Colorless
* Icon set prefix: `octicon`
* File: [json/octicon.json](json/octicon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Memory Icons
* Number of icons: 651
* Author: Pictogrammers
* URL: https://github.com/Pictogrammers/Memory
* License: Apache 2.0
* License URL: https://github.com/Pictogrammers/Memory/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `memory`
* File: [json/memory.json](json/memory.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## System UIcons
* Number of icons: 430
* Author: Corey Ginnivan
* URL: https://github.com/CoreyGinnivan/system-uicons
* License: Unlicense
* License URL: https://github.com/CoreyGinnivan/system-uicons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `system-uicons`
* File: [json/system-uicons.json](json/system-uicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Radix Icons
* Number of icons: 318
* Author: WorkOS
* URL: https://github.com/radix-ui/icons
* License: MIT
* License URL: https://github.com/radix-ui/icons/blob/master/LICENSE
* Version: 1.3.2
* Palette: Colorless
* Icon set prefix: `radix-icons`
* File: [json/radix-icons.json](json/radix-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Zondicons
* Number of icons: 297
* Author: Steve Schoger
* URL: https://github.com/dukestreetstudio/zondicons
* License: MIT
* License URL: https://github.com/dukestreetstudio/zondicons/blob/master/LICENSE
* Version: 0.1.0
* Palette: Colorless
* Icon set prefix: `zondicons`
* File: [json/zondicons.json](json/zondicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## uiw icons
* Number of icons: 214
* Author: liwen0526
* URL: https://github.com/uiwjs/icons
* License: MIT
* License URL: https://github.com/uiwjs/icons/blob/master/LICENSE
* Version: 2.6.10
* Palette: Colorless
* Icon set prefix: `uiw`
* File: [json/uiw.json](json/uiw.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Maki
* Number of icons: 215
* Author: Mapbox
* URL: https://github.com/mapbox/maki
* License: CC0
* License URL: https://creativecommons.org/publicdomain/zero/1.0/
* Version: 8.2.0
* Palette: Colorless
* Icon set prefix: `maki`
* File: [json/maki.json](json/maki.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## CodeX Icons
* Number of icons: 78
* Author: CodeX
* URL: https://github.com/codex-team/icons
* License: MIT
* License URL: https://github.com/codex-team/icons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `codex`
* File: [json/codex.json](json/codex.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Evil Icons
* Number of icons: 70
* Author: Alexander Madyankin and Roman Shamin
* URL: https://github.com/evil-icons/evil-icons
* License: MIT
* License URL: https://github.com/evil-icons/evil-icons/blob/master/LICENSE.txt
* Version: 1.10.1
* Palette: Colorless
* Icon set prefix: `ei`
* File: [json/ei.json](json/ei.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## HeroIcons
* Number of icons: 1288
* Author: Refactoring UI Inc
* URL: https://github.com/tailwindlabs/heroicons
* License: MIT
* License URL: https://github.com/tailwindlabs/heroicons/blob/master/LICENSE
* Version: 2.2.0
* Palette: Colorless
* Icon set prefix: `heroicons`
* File: [json/heroicons.json](json/heroicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pepicons Pop!
* Number of icons: 1275
* Author: CyCraft
* URL: https://github.com/CyCraft/pepicons
* License: CC BY 4.0
* License URL: https://github.com/CyCraft/pepicons/blob/dev/LICENSE
* Version: 3.1.1
* Palette: Colorless
* Icon set prefix: `pepicons-pop`
* File: [json/pepicons-pop.json](json/pepicons-pop.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pepicons Print
* Number of icons: 1275
* Author: CyCraft
* URL: https://github.com/CyCraft/pepicons
* License: CC BY 4.0
* License URL: https://github.com/CyCraft/pepicons/blob/dev/LICENSE
* Version: 3.1.1
* Palette: Colorless
* Icon set prefix: `pepicons-print`
* File: [json/pepicons-print.json](json/pepicons-print.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pepicons Pencil
* Number of icons: 1275
* Author: CyCraft
* URL: https://github.com/CyCraft/pepicons
* License: CC BY 4.0
* License URL: https://github.com/CyCraft/pepicons/blob/dev/LICENSE
* Version: 3.1.1
* Palette: Colorless
* Icon set prefix: `pepicons-pencil`
* File: [json/pepicons-pencil.json](json/pepicons-pencil.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Framework7 Icons
* Number of icons: 1253
* Author: Vladimir Kharlampidi
* URL: https://github.com/framework7io/framework7-icons
* License: MIT
* License URL: https://github.com/framework7io/framework7-icons/blob/master/LICENSE
* Version: 5.0.5
* Palette: Colorless
* Icon set prefix: `f7`
* File: [json/f7.json](json/f7.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Gitlab SVGs
* Number of icons: 402
* Author: GitLab B.V.
* URL: https://gitlab.com/gitlab-org/gitlab-svgs/-/tree/main
* License: MIT
* License URL: https://gitlab.com/gitlab-org/gitlab-svgs/-/blob/main/LICENSE
* Version: 3.134.0
* Palette: Colorless
* Icon set prefix: `pajamas`
* File: [json/pajamas.json](json/pajamas.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Garden SVG Icons
* Number of icons: 1003
* Author: Zendesk
* URL: https://github.com/zendeskgarden/svg-icons
* License: Apache 2.0
* License URL: https://github.com/zendeskgarden/svg-icons/blob/main/LICENSE.md
* Palette: Colorless
* Icon set prefix: `garden`
* File: [json/garden.json](json/garden.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Streamline
* Number of icons: 3000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline`
* File: [json/streamline.json](json/streamline.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flex free icons
* Number of icons: 1500
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-flex`
* File: [json/streamline-flex.json](json/streamline-flex.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome Solid
* Number of icons: 1402
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 6.7.2
* Palette: Colorless
* Icon set prefix: `fa6-solid`
* File: [json/fa6-solid.json](json/fa6-solid.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome Regular
* Number of icons: 163
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 6.7.2
* Palette: Colorless
* Icon set prefix: `fa6-regular`
* File: [json/fa6-regular.json](json/fa6-regular.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Pico-icon
* Number of icons: 824
* Author: Picon Contributors
* URL: https://github.com/yne/picon
* License: Open Font License
* License URL: https://github.com/yne/picon/blob/master/OFL.txt
* Palette: Colorless
* Icon set prefix: `picon`
* File: [json/picon.json](json/picon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## OOUI
* Number of icons: 364
* Author: OOUI Team
* URL: https://github.com/wikimedia/oojs-ui
* License: MIT
* License URL: https://github.com/wikimedia/oojs-ui/blob/master/LICENSE-MIT
* Version: 0.51.7
* Palette: Colorless
* Icon set prefix: `ooui`
* File: [json/ooui.json](json/ooui.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## OpenSearch UI
* Number of icons: 402
* Author: OpenSearch Contributors
* URL: https://github.com/opensearch-project/oui
* License: Apache 2.0
* License URL: https://github.com/opensearch-project/oui/blob/main/LICENSE.txt
* Version: 2.0.0
* Palette: Colorless
* Icon set prefix: `oui`
* File: [json/oui.json](json/oui.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## NRK Core Icons
* Number of icons: 230
* Author: Norsk rikskringkasting
* URL: https://github.com/nrkno/core-icons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `nrk`
* File: [json/nrk.json](json/nrk.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Dinkie Icons
* Number of icons: 1198
* Author: atelierAnchor
* URL: https://github.com/atelier-anchor/dinkie-icons
* License: MIT
* License URL: https://github.com/atelier-anchor/dinkie-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `dinkie-icons`
* File: [json/dinkie-icons.json](json/dinkie-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Qlementine Icons
* Number of icons: 684
* Author: Olivier Cléro
* URL: https://github.com/oclero/qlementine-icons
* License: MIT
* License URL: https://github.com/oclero/qlementine-icons/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `qlementine-icons`
* File: [json/qlementine-icons.json](json/qlementine-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Ultimate color icons
* Number of icons: 998
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-ultimate-color`
* File: [json/streamline-ultimate-color.json](json/streamline-ultimate-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Plump color icons
* Number of icons: 1000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-plump-color`
* File: [json/streamline-plump-color.json](json/streamline-plump-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Freehand color icons
* Number of icons: 1000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-freehand-color`
* File: [json/streamline-freehand-color.json](json/streamline-freehand-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Kameleon color icons
* Number of icons: 400
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-kameleon-color`
* File: [json/streamline-kameleon-color.json](json/streamline-kameleon-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Stickies color icons
* Number of icons: 200
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-stickies-color`
* File: [json/streamline-stickies-color.json](json/streamline-stickies-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent UI System Color Icons
* Number of icons: 867
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui-system-icons
* License: MIT
* License URL: https://github.com/microsoft/fluentui-system-icons/blob/main/LICENSE
* Version: 1.1.302
* Palette: Colorful
* Icon set prefix: `fluent-color`
* File: [json/fluent-color.json](json/fluent-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Streamline color
* Number of icons: 2000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-color`
* File: [json/streamline-color.json](json/streamline-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flex color icons
* Number of icons: 1000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-flex-color`
* File: [json/streamline-flex-color.json](json/streamline-flex-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Sharp color icons
* Number of icons: 1000
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-sharp-color`
* File: [json/streamline-sharp-color.json](json/streamline-sharp-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Cyber color icons
* Number of icons: 500
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-cyber-color`
* File: [json/streamline-cyber-color.json](json/streamline-cyber-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IconPark
* Number of icons: 2658
* Author: ByteDance
* URL: https://github.com/bytedance/IconPark
* License: Apache 2.0
* License URL: https://github.com/bytedance/IconPark/blob/master/LICENSE
* Version: 1.4.2
* Palette: Colorful
* Icon set prefix: `icon-park`
* File: [json/icon-park.json](json/icon-park.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Marketeq
* Number of icons: 590
* Author: Marketeq
* License: MIT
* Palette: Colorful
* Icon set prefix: `marketeq`
* File: [json/marketeq.json](json/marketeq.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## VSCode Icons
* Number of icons: 1387
* Author: Roberto Huertas
* URL: https://github.com/vscode-icons/vscode-icons
* License: MIT
* License URL: https://github.com/vscode-icons/vscode-icons/blob/master/LICENSE
* Version: 12.13.0
* Palette: Colorful
* Icon set prefix: `vscode-icons`
* File: [json/vscode-icons.json](json/vscode-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Codicons
* Number of icons: 483
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/vscode-codicons
* License: CC BY 4.0
* License URL: https://github.com/microsoft/vscode-codicons/blob/main/LICENSE
* Version: 0.0.37
* Palette: Colorless
* Icon set prefix: `codicon`
* File: [json/codicon.json](json/codicon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Material Icon Theme
* Number of icons: 1056
* Author: Material Extensions
* URL: https://github.com/material-extensions/vscode-material-icon-theme
* License: MIT
* License URL: https://github.com/material-extensions/vscode-material-icon-theme/blob/main/LICENSE
* Palette: Colorful
* Icon set prefix: `material-icon-theme`
* File: [json/material-icon-theme.json](json/material-icon-theme.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## File Icons
* Number of icons: 930
* Author: John Gardner
* URL: https://github.com/file-icons/icons
* License: ISC
* License URL: https://github.com/file-icons/icons/blob/master/LICENSE.md
* Palette: Colorless
* Icon set prefix: `file-icons`
* File: [json/file-icons.json](json/file-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Devicon
* Number of icons: 933
* Author: konpa
* URL: https://github.com/devicons/devicon/tree/master
* License: MIT
* License URL: https://github.com/devicons/devicon/blob/master/LICENSE
* Palette: Colorful
* Icon set prefix: `devicon`
* File: [json/devicon.json](json/devicon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Devicon Plain
* Number of icons: 682
* Author: konpa
* URL: https://github.com/devicons/devicon/tree/master
* License: MIT
* License URL: https://github.com/devicons/devicon/blob/master/LICENSE
* Palette: Colorless
* Icon set prefix: `devicon-plain`
* File: [json/devicon-plain.json](json/devicon-plain.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Catppuccin Icons
* Number of icons: 560
* Author: Catppuccin
* URL: https://github.com/catppuccin/vscode-icons
* License: MIT
* License URL: https://github.com/catppuccin/vscode-icons/blob/main/LICENSE
* Palette: Colorful
* Icon set prefix: `catppuccin`
* File: [json/catppuccin.json](json/catppuccin.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Skill Icons
* Number of icons: 397
* Author: tandpfun
* URL: https://github.com/tandpfun/skill-icons
* License: MIT
* License URL: https://github.com/tandpfun/skill-icons/blob/main/LICENSE
* Palette: Colorful
* Icon set prefix: `skill-icons`
* File: [json/skill-icons.json](json/skill-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## UnJS Logos
* Number of icons: 63
* Author: UnJS
* URL: https://github.com/unjs
* License: Apache 2.0
* Palette: Colorful
* Icon set prefix: `unjs`
* File: [json/unjs.json](json/unjs.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Simple Icons
* Number of icons: 3291
* Author: Simple Icons Collaborators
* URL: https://github.com/simple-icons/simple-icons
* License: CC0 1.0
* License URL: https://github.com/simple-icons/simple-icons/blob/develop/LICENSE.md
* Version: 15.0.0
* Palette: Colorless
* Icon set prefix: `simple-icons`
* File: [json/simple-icons.json](json/simple-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## SVG Logos
* Number of icons: 1838
* Author: Gil Barbara
* URL: https://github.com/gilbarbara/logos
* License: CC0
* License URL: https://raw.githubusercontent.com/gilbarbara/logos/master/LICENSE.txt
* Palette: Colorful
* Icon set prefix: `logos`
* File: [json/logos.json](json/logos.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Logos free icons
* Number of icons: 1362
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `streamline-logos`
* File: [json/streamline-logos.json](json/streamline-logos.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## CoreUI Brands
* Number of icons: 830
* Author: creativeLabs Łukasz Holeczek
* URL: https://github.com/coreui/coreui-icons
* License: CC0 1.0
* License URL: https://creativecommons.org/publicdomain/zero/1.0/
* Version: 2.0.1
* Palette: Colorless
* Icon set prefix: `cib`
* File: [json/cib.json](json/cib.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome Brands
* Number of icons: 495
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 6.7.2
* Palette: Colorless
* Icon set prefix: `fa6-brands`
* File: [json/fa6-brands.json](json/fa6-brands.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## BoxIcons Logo
* Number of icons: 155
* Author: Atisa
* URL: https://github.com/atisawd/boxicons
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.1.4
* Palette: Colorless
* Icon set prefix: `bxl`
* File: [json/bxl.json](json/bxl.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Nonicons
* Number of icons: 69
* Author: yamatsum
* URL: https://github.com/yamatsum/nonicons
* License: MIT
* License URL: https://github.com/yamatsum/nonicons/blob/master/LICENSE
* Version: 0.0.18
* Palette: Colorless
* Icon set prefix: `nonicons`
* File: [json/nonicons.json](json/nonicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Arcticons
* Number of icons: 12946
* Author: Donnnno
* URL: https://github.com/Arcticons-Team/Arcticons
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Palette: Colorless
* Icon set prefix: `arcticons`
* File: [json/arcticons.json](json/arcticons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Custom Brand Icons
* Number of icons: 1442
* Author: Emanuele & rchiileea
* URL: https://github.com/elax46/custom-brand-icons
* License: CC BY-NC-SA 4.0
* License URL: https://github.com/elax46/custom-brand-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `cbi`
* File: [json/cbi.json](json/cbi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Brandico
* Number of icons: 45
* Author: Fontello
* URL: https://github.com/fontello/brandico.font
* License: CC BY SA
* License URL: https://creativecommons.org/licenses/by-sa/3.0/
* Palette: Colorless
* Icon set prefix: `brandico`
* File: [json/brandico.json](json/brandico.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Entypo+ Social
* Number of icons: 76
* Author: Daniel Bruce
* URL: https://github.com/chancancode/entypo-plus
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Palette: Colorless
* Icon set prefix: `entypo-social`
* File: [json/entypo-social.json](json/entypo-social.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Web3 Icons
* Number of icons: 1728
* Author: 0xa3k5
* URL: https://github.com/0xa3k5/web3icons
* License: MIT
* License URL: https://github.com/0xa3k5/web3icons/blob/main/LICENCE
* Version: 4.0.13
* Palette: Colorless
* Icon set prefix: `token`
* File: [json/token.json](json/token.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Web3 Icons Branded
* Number of icons: 1981
* Author: 0xa3k5
* URL: https://github.com/0xa3k5/web3icons
* License: MIT
* License URL: https://github.com/0xa3k5/web3icons/blob/main/LICENCE
* Version: 4.0.13
* Palette: Colorful
* Icon set prefix: `token-branded`
* File: [json/token-branded.json](json/token-branded.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Cryptocurrency Icons
* Number of icons: 483
* Author: Christopher Downer
* URL: https://github.com/atomiclabs/cryptocurrency-icons
* License: CC0 1.0
* License URL: https://creativecommons.org/publicdomain/zero/1.0/
* Version: 0.18.1
* Palette: Colorless
* Icon set prefix: `cryptocurrency`
* File: [json/cryptocurrency.json](json/cryptocurrency.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Cryptocurrency Color Icons
* Number of icons: 483
* Author: Christopher Downer
* URL: https://github.com/atomiclabs/cryptocurrency-icons
* License: CC0 1.0
* License URL: https://creativecommons.org/publicdomain/zero/1.0/
* Version: 0.18.1
* Palette: Colorful
* Icon set prefix: `cryptocurrency-color`
* File: [json/cryptocurrency-color.json](json/cryptocurrency-color.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## OpenMoji
* Number of icons: 4182
* Author: OpenMoji
* URL: https://github.com/hfg-gmuend/openmoji
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Palette: Colorful
* Icon set prefix: `openmoji`
* File: [json/openmoji.json](json/openmoji.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Twitter Emoji
* Number of icons: 3668
* Author: Twitter
* URL: https://github.com/twitter/twemoji
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `twemoji`
* File: [json/twemoji.json](json/twemoji.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Noto Emoji
* Number of icons: 3562
* Author: Google Inc
* URL: https://github.com/googlefonts/noto-emoji
* License: Apache 2.0
* License URL: https://github.com/googlefonts/noto-emoji/blob/main/svg/LICENSE
* Palette: Colorful
* Icon set prefix: `noto`
* File: [json/noto.json](json/noto.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent Emoji
* Number of icons: 3126
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui-emoji
* License: MIT
* License URL: https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE
* Palette: Colorful
* Icon set prefix: `fluent-emoji`
* File: [json/fluent-emoji.json](json/fluent-emoji.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent Emoji Flat
* Number of icons: 3145
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui-emoji
* License: MIT
* License URL: https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE
* Palette: Colorful
* Icon set prefix: `fluent-emoji-flat`
* File: [json/fluent-emoji-flat.json](json/fluent-emoji-flat.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent Emoji High Contrast
* Number of icons: 1595
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui-emoji
* License: MIT
* License URL: https://github.com/microsoft/fluentui-emoji/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `fluent-emoji-high-contrast`
* File: [json/fluent-emoji-high-contrast.json](json/fluent-emoji-high-contrast.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Noto Emoji (v1)
* Number of icons: 2162
* Author: Google Inc
* URL: https://github.com/googlefonts/noto-emoji
* License: Apache 2.0
* License URL: https://github.com/googlefonts/noto-emoji/blob/main/svg/LICENSE
* Palette: Colorful
* Icon set prefix: `noto-v1`
* File: [json/noto-v1.json](json/noto-v1.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Emoji One (Colored)
* Number of icons: 1834
* Author: Emoji One
* URL: https://github.com/EmojiTwo/emojitwo
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.3.0
* Palette: Colorful
* Icon set prefix: `emojione`
* File: [json/emojione.json](json/emojione.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Emoji One (Monotone)
* Number of icons: 1403
* Author: Emoji One
* URL: https://github.com/EmojiTwo/emojitwo
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 2.2.7
* Palette: Colorless
* Icon set prefix: `emojione-monotone`
* File: [json/emojione-monotone.json](json/emojione-monotone.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Emoji One (v1)
* Number of icons: 1262
* Author: Emoji One
* URL: https://github.com/joypixels/emojione-legacy
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Version: 1.5.2
* Palette: Colorful
* Icon set prefix: `emojione-v1`
* File: [json/emojione-v1.json](json/emojione-v1.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Firefox OS Emoji
* Number of icons: 1034
* Author: Mozilla
* URL: https://github.com/mozilla/fxemoji
* License: Apache 2.0
* License URL: https://mozilla.github.io/fxemoji/LICENSE.md
* Version: 0.0.2
* Palette: Colorful
* Icon set prefix: `fxemoji`
* File: [json/fxemoji.json](json/fxemoji.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Streamline Emojis
* Number of icons: 787
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorful
* Icon set prefix: `streamline-emojis`
* File: [json/streamline-emojis.json](json/streamline-emojis.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Circle Flags
* Number of icons: 632
* Author: HatScripts
* URL: https://github.com/HatScripts/circle-flags
* License: MIT
* License URL: https://github.com/HatScripts/circle-flags/blob/gh-pages/LICENSE
* Version: 1.0.0
* Palette: Colorful
* Icon set prefix: `circle-flags`
* File: [json/circle-flags.json](json/circle-flags.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flag Icons
* Number of icons: 542
* Author: Panayiotis Lipiridis
* URL: https://github.com/lipis/flag-icons
* License: MIT
* License URL: https://github.com/lipis/flag-icons/blob/main/LICENSE
* Version: 7.5.0
* Palette: Colorful
* Icon set prefix: `flag`
* File: [json/flag.json](json/flag.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flagpack
* Number of icons: 255
* Author: Yummygum
* URL: https://github.com/Yummygum/flagpack-core
* License: MIT
* License URL: https://github.com/Yummygum/flagpack-core/blob/main/LICENSE
* Version: 2.0.0
* Palette: Colorful
* Icon set prefix: `flagpack`
* File: [json/flagpack.json](json/flagpack.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## CoreUI Flags
* Number of icons: 199
* Author: creativeLabs Łukasz Holeczek
* URL: https://github.com/coreui/coreui-icons
* License: CC0 1.0
* License URL: https://creativecommons.org/publicdomain/zero/1.0/
* Version: 2.0.1
* Palette: Colorful
* Icon set prefix: `cif`
* File: [json/cif.json](json/cif.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font-GIS
* Number of icons: 367
* Author: Jean-Marc Viglino
* URL: https://github.com/viglino/font-gis
* License: CC BY 4.0
* License URL: https://github.com/Viglino/font-gis/blob/main/LICENSE-CC-BY.md
* Version: 1.0.6
* Palette: Colorless
* Icon set prefix: `gis`
* File: [json/gis.json](json/gis.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Map Icons
* Number of icons: 167
* Author: Scott de Jonge
* URL: https://github.com/scottdejonge/map-icons
* License: Open Font License
* License URL: https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL
* Version: 3.0.2
* Palette: Colorless
* Icon set prefix: `map`
* File: [json/map.json](json/map.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## GeoGlyphs
* Number of icons: 30
* Author: Sam Matthews
* URL: https://github.com/cugos/geoglyphs
* License: MIT
* License URL: https://github.com/cugos/geoglyphs/blob/main/LICENSE.md
* Version: 0.0.10
* Palette: Colorless
* Icon set prefix: `geo`
* File: [json/geo.json](json/geo.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Game Icons
* Number of icons: 4123
* Author: GameIcons
* URL: https://github.com/game-icons/icons
* License: CC BY 3.0
* License URL: https://github.com/game-icons/icons/blob/master/license.txt
* Palette: Colorless
* Icon set prefix: `game-icons`
* File: [json/game-icons.json](json/game-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## FontAudio
* Number of icons: 155
* Author: @fefanto
* URL: https://github.com/fefanto/fontaudio
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `fad`
* File: [json/fad.json](json/fad.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Academicons
* Number of icons: 158
* Author: James Walsh
* URL: https://github.com/jpswalsh/academicons
* License: Open Font License
* License URL: https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL
* Version: 1.9.4
* Palette: Colorless
* Icon set prefix: `academicons`
* File: [json/academicons.json](json/academicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Weather Icons
* Number of icons: 219
* Author: Erik Flowers
* URL: https://github.com/erikflowers/weather-icons
* License: Open Font License
* License URL: https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL
* Version: 2.0.12
* Palette: Colorless
* Icon set prefix: `wi`
* File: [json/wi.json](json/wi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Meteocons
* Number of icons: 447
* Author: Bas Milius
* URL: https://github.com/basmilius/weather-icons
* License: MIT
* License URL: https://github.com/basmilius/weather-icons/blob/dev/LICENSE
* Version: 3.0.0
* Palette: Colorful
* Icon set prefix: `meteocons`
* File: [json/meteocons.json](json/meteocons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Health Icons
* Number of icons: 2000
* Author: Resolve to Save Lives
* URL: https://github.com/resolvetosavelives/healthicons
* License: MIT
* License URL: https://github.com/resolvetosavelives/healthicons/blob/main/LICENSE
* Version: 2.0.0
* Palette: Colorless
* Icon set prefix: `healthicons`
* File: [json/healthicons.json](json/healthicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Medical Icons
* Number of icons: 144
* Author: Samuel Frémondière
* URL: https://github.com/samcome/webfont-medical-icons
* License: MIT
* License URL: https://github.com/samcome/webfont-medical-icons/blob/master/LICENSE
* Version: 1.0.0
* Palette: Colorless
* Icon set prefix: `medical-icon`
* File: [json/medical-icon.json](json/medical-icon.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Covid Icons
* Number of icons: 142
* Author: Streamline
* URL: https://github.com/webalys-hq/streamline-vectors
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `covid`
* File: [json/covid.json](json/covid.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Line Awesome
* Number of icons: 1544
* Author: Icons8
* URL: https://github.com/icons8/line-awesome
* License: Apache 2.0
* License URL: https://www.apache.org/licenses/LICENSE-2.0
* Version: 1.2.1
* Palette: Colorless
* Icon set prefix: `la`
* File: [json/la.json](json/la.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Eva Icons
* Number of icons: 490
* Author: Akveo
* URL: https://github.com/akveo/eva-icons/
* License: MIT
* License URL: https://github.com/akveo/eva-icons/blob/master/LICENSE.txt
* Version: 1.1.3
* Palette: Colorless
* Icon set prefix: `eva`
* File: [json/eva.json](json/eva.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Dashicons
* Number of icons: 342
* Author: WordPress
* URL: https://github.com/WordPress/dashicons
* License: GPL
* License URL: https://github.com/WordPress/dashicons/blob/master/gpl.txt
* Version: 0.9.0
* Palette: Colorless
* Icon set prefix: `dashicons`
* File: [json/dashicons.json](json/dashicons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Flat Color Icons
* Number of icons: 329
* Author: Icons8
* URL: https://github.com/icons8/flat-Color-icons
* License: MIT
* Version: 1.0.2
* Palette: Colorful
* Icon set prefix: `flat-color-icons`
* File: [json/flat-color-icons.json](json/flat-color-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Entypo+
* Number of icons: 321
* Author: Daniel Bruce
* URL: https://github.com/chancancode/entypo-plus
* License: CC BY-SA 4.0
* License URL: https://creativecommons.org/licenses/by-sa/4.0/
* Palette: Colorless
* Icon set prefix: `entypo`
* File: [json/entypo.json](json/entypo.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Foundation
* Number of icons: 283
* Author: Zurb
* URL: https://github.com/zurb/foundation-icon-fonts
* License: MIT
* Version: 3.0.0
* Palette: Colorless
* Icon set prefix: `foundation`
* File: [json/foundation.json](json/foundation.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Raphael
* Number of icons: 266
* Author: Dmitry Baranovskiy
* URL: https://github.com/dmitrybaranovskiy/raphael
* License: MIT
* Palette: Colorless
* Icon set prefix: `raphael`
* File: [json/raphael.json](json/raphael.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Icons8 Windows 10 Icons
* Number of icons: 234
* Author: Icons8
* URL: https://github.com/icons8/windows-10-icons
* License: MIT
* Version: 1.0.0
* Palette: Colorless
* Icon set prefix: `icons8`
* File: [json/icons8.json](json/icons8.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Innowatio Font
* Number of icons: 105
* Author: Innowatio
* URL: https://github.com/innowatio/iwwa-icons
* License: Apache 2.0
* License URL: https://www.apache.org/licenses/LICENSE-2.0
* Version: 1.1.3
* Palette: Colorless
* Icon set prefix: `iwwa`
* File: [json/iwwa.json](json/iwwa.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Gala Icons
* Number of icons: 51
* Author: Jake Wells
* URL: https://github.com/cyberalien/gala-icons
* License: GPL
* License URL: https://github.com/cyberalien/gala-icons/blob/main/LICENSE
* Palette: Colorless
* Icon set prefix: `gala`
* File: [json/gala.json](json/gala.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## HeroIcons v1 Outline
* Number of icons: 230
* Author: Refactoring UI Inc
* URL: https://github.com/tailwindlabs/heroicons
* License: MIT
* License URL: https://github.com/tailwindlabs/heroicons/blob/master/LICENSE
* Version: 1.0.6
* Palette: Colorless
* Icon set prefix: `heroicons-outline`
* File: [json/heroicons-outline.json](json/heroicons-outline.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## HeroIcons v1 Solid
* Number of icons: 230
* Author: Refactoring UI Inc
* URL: https://github.com/tailwindlabs/heroicons
* License: MIT
* License URL: https://github.com/tailwindlabs/heroicons/blob/master/LICENSE
* Version: 1.0.6
* Palette: Colorless
* Icon set prefix: `heroicons-solid`
* File: [json/heroicons-solid.json](json/heroicons-solid.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome 5 Solid
* Number of icons: 1001
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 5.15.4
* Palette: Colorless
* Icon set prefix: `fa-solid`
* File: [json/fa-solid.json](json/fa-solid.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome 5 Regular
* Number of icons: 151
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 5.15.4
* Palette: Colorless
* Icon set prefix: `fa-regular`
* File: [json/fa-regular.json](json/fa-regular.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome 5 Brands
* Number of icons: 457
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Version: 5.15.4
* Palette: Colorless
* Icon set prefix: `fa-brands`
* File: [json/fa-brands.json](json/fa-brands.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Font Awesome 4
* Number of icons: 678
* Author: Dave Gandy
* URL: https://github.com/FortAwesome/Font-Awesome/tree/fa-4
* License: Open Font License
* License URL: https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL
* Version: 4.7.0
* Palette: Colorless
* Icon set prefix: `fa`
* File: [json/fa.json](json/fa.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fluent UI MDL2
* Number of icons: 1735
* Author: Microsoft Corporation
* URL: https://github.com/microsoft/fluentui/tree/master/packages/react-icons-mdl2
* License: MIT
* License URL: https://github.com/microsoft/fluentui/blob/master/packages/react-icons-mdl2/LICENSE
* Palette: Colorless
* Icon set prefix: `fluent-mdl2`
* File: [json/fluent-mdl2.json](json/fluent-mdl2.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Fontisto
* Number of icons: 615
* Author: Kenan Gündoğan
* URL: https://github.com/kenangundogan/fontisto
* License: MIT
* License URL: https://github.com/kenangundogan/fontisto/blob/master/LICENSE
* Version: 3.0.4
* Palette: Colorless
* Icon set prefix: `fontisto`
* File: [json/fontisto.json](json/fontisto.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## IcoMoon Free
* Number of icons: 491
* Author: Keyamoon
* URL: https://github.com/Keyamoon/IcoMoon-Free
* License: GPL
* License URL: https://www.gnu.org/licenses/gpl.html
* Palette: Colorless
* Icon set prefix: `icomoon-free`
* File: [json/icomoon-free.json](json/icomoon-free.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Subway Icon Set
* Number of icons: 306
* Author: Mariusz Ostrowski
* URL: https://github.com/mariuszostrowski/subway
* License: CC BY 4.0
* License URL: https://creativecommons.org/licenses/by/4.0/
* Palette: Colorless
* Icon set prefix: `subway`
* File: [json/subway.json](json/subway.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Open Iconic
* Number of icons: 223
* Author: Iconic
* URL: https://github.com/iconic/open-iconic
* License: MIT
* License URL: https://github.com/iconic/open-iconic/blob/master/ICON-LICENSE
* Version: 1.1.1
* Palette: Colorless
* Icon set prefix: `oi`
* File: [json/oi.json](json/oi.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Icons8 Windows 8 Icons
* Number of icons: 200
* Author: Icons8
* URL: https://github.com/icons8/WPF-UI-Framework
* License: MIT
* Palette: Colorless
* Icon set prefix: `wpf`
* File: [json/wpf.json](json/wpf.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Simple line icons
* Number of icons: 189
* Author: Sabbir Ahmed
* URL: https://github.com/thesabbir/simple-line-icons
* License: MIT
* License URL: https://github.com/thesabbir/simple-line-icons/blob/master/LICENSE.md
* Version: 2.5.5
* Palette: Colorless
* Icon set prefix: `simple-line-icons`
* File: [json/simple-line-icons.json](json/simple-line-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Elegant
* Number of icons: 100
* Author: Kenny Sing
* URL: https://github.com/pprince/etlinefont-bower
* License: GPL 3.0
* License URL: https://www.gnu.org/licenses/gpl.html
* Version: 1.0.1
* Palette: Colorless
* Icon set prefix: `et`
* File: [json/et.json](json/et.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Elusive Icons
* Number of icons: 304
* Author: Team Redux
* URL: https://github.com/dovy/elusive-icons
* License: Open Font License
* License URL: https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL
* Version: 2.0.0
* Palette: Colorless
* Icon set prefix: `el`
* File: [json/el.json](json/el.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Vaadin Icons
* Number of icons: 636
* Author: Vaadin
* URL: https://github.com/vaadin/web-components
* License: Apache 2.0
* Version: 4.3.2
* Palette: Colorless
* Icon set prefix: `vaadin`
* File: [json/vaadin.json](json/vaadin.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))

## Grommet Icons
* Number of icons: 634
* Author: Grommet
* URL: https://github.com/grommet/grommet-icons
* License: Apache 2.0
* License URL: https://www.apache.org/licenses/LICENSE-2.0
* Version: 4.12.1
* Palette: Colorless
* Icon set prefix: `grommet-icons`
* File: [json/grommet-icons.json](json/grommet-icons.json) ([in IconifyJSON format](https://docs.iconify.design/types/iconify-json.html))