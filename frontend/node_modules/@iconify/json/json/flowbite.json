{"prefix": "flowbite", "info": {"name": "Flowbite Icons", "total": 751, "author": {"name": "Themesberg", "url": "https://github.com/themesberg/flowbite-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/themesberg/flowbite-icons/blob/main/LICENSE"}, "samples": ["user-outline", "vue-solid", "list-outline", "bars-from-left-outline", "letter-underline-outline", "table-column-solid"], "height": 24, "category": "UI 24px", "tags": ["Uses Stroke"], "palette": false}, "lastModified": **********, "icons": {"add-column-after-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5v14m8-7h-2m0 0h-2m2 0v2m0-2v-2M3 11h6m-6 4h6m11 4H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1\"/>"}, "add-column-before-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 5v14m-8-7h2m0 0h2m-2 0v2m0-2v-2m12 1h-6m6 4h-6M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "address-book-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 6H5m2 3H5m2 3H5m2 3H5m2 3H5m11-1a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2M7 3h11a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1m8 7a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "address-book-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 2a2 2 0 0 0-2 2v1a1 1 0 0 0 0 2v1a1 1 0 0 0 0 2v1a1 1 0 1 0 0 2v1a1 1 0 1 0 0 2v1a1 1 0 1 0 0 2v1a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm3 8a3 3 0 1 1 6 0a3 3 0 0 1-6 0m-1 7a3 3 0 0 1 3-3h2a3 3 0 0 1 3 3a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "adjustments-horizontal-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M20 6H10m0 0a2 2 0 1 0-4 0m4 0a2 2 0 1 1-4 0m0 0H4m16 6h-2m0 0a2 2 0 1 0-4 0m4 0a2 2 0 1 1-4 0m0 0H4m16 6H10m0 0a2 2 0 1 0-4 0m4 0a2 2 0 1 1-4 0m0 0H4\"/>"}, "adjustments-horizontal-solid": {"body": "<path fill=\"currentColor\" d=\"M10.83 5a3.001 3.001 0 0 0-5.66 0H4a1 1 0 1 0 0 2h1.17a3.001 3.001 0 0 0 5.66 0H20a1 1 0 1 0 0-2zM4 11h9.17a3.001 3.001 0 0 1 5.66 0H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 1 1 0-2m1.17 6H4a1 1 0 1 0 0 2h1.17a3.001 3.001 0 0 0 5.66 0H20a1 1 0 1 0 0-2h-9.17a3.001 3.001 0 0 0-5.66 0\"/>"}, "adjustments-vertical-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 4v10m0 0a2 2 0 1 0 0 4m0-4a2 2 0 1 1 0 4m0 0v2m6-16v2m0 0a2 2 0 1 0 0 4m0-4a2 2 0 1 1 0 4m0 0v10m6-16v10m0 0a2 2 0 1 0 0 4m0-4a2 2 0 1 1 0 4m0 0v2\"/>"}, "adjustments-vertical-solid": {"body": "<path fill=\"currentColor\" d=\"M5 13.17a3.001 3.001 0 0 0 0 5.66V20a1 1 0 1 0 2 0v-1.17a3.001 3.001 0 0 0 0-5.66V4a1 1 0 0 0-2 0zM11 20v-9.17a3.001 3.001 0 0 1 0-5.66V4a1 1 0 1 1 2 0v1.17a3.001 3.001 0 0 1 0 5.66V20a1 1 0 1 1-2 0m6-1.17V20a1 1 0 1 0 2 0v-1.17a3.001 3.001 0 0 0 0-5.66V4a1 1 0 1 0-2 0v9.17a3.001 3.001 0 0 0 0 5.66\"/>"}, "align-center-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 6h8M6 10h12M8 14h8M6 18h12\"/>"}, "align-center-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 6c0-.6.4-1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m-2 4c0-.6.4-1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m2 4c0-.6.4-1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m-2 4c0-.6.4-1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>", "hidden": true}, "align-justify-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 6H6m12 4H6m12 4H6m12 4H6\"/>"}, "align-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 6h8m-8 4h12M6 14h8m-8 4h12\"/>"}, "align-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 6h-8m8 4H6m12 4h-8m8 4H6\"/>"}, "angle-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m19 9l-7 7l-7-7\"/>"}, "angle-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m15 19l-7-7l7-7\"/>"}, "angle-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m9 5l7 7l-7 7\"/>"}, "angle-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m5 15l7-7l7 7\"/>"}, "annotation-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7.556 8.5h8m-8 3.5H12m7.111-7H4.89a.9.9 0 0 0-.629.256a.87.87 0 0 0-.26.619v9.25c0 .232.094.455.26.619A.9.9 0 0 0 4.89 16H9l3 4l3-4h4.111a.9.9 0 0 0 .629-.256a.87.87 0 0 0 .26-.619v-9.25a.87.87 0 0 0-.26-.619a.9.9 0 0 0-.63-.256Z\"/>"}, "annotation-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3.559 4.544c.355-.35.834-.544 1.33-.544H19.11c.496 0 .975.194 1.33.544s.559.829.559 1.331v9.25c0 .502-.203.981-.559 1.331c-.355.35-.834.544-1.33.544H15.5l-2.7 3.6a1 1 0 0 1-1.6 0L8.5 17H4.889c-.496 0-.975-.194-1.33-.544A1.87 1.87 0 0 1 3 15.125v-9.25c0-.502.203-.981.559-1.331M7.556 7.5a1 1 0 1 0 0 2h8a1 1 0 0 0 0-2zm0 3.5a1 1 0 1 0 0 2H12a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "api-key-outline": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M6.943 11h-.852l.96-2.91h1.08L9.09 11h-.852l-.637-2.108H7.58zm-.159-1.148h1.602v.591H6.784zM9.371 11V8.09h1.256a1.2 1.2 0 0 1 .567.129a.93.93 0 0 1 .377.36q.135.23.135.54q0 .314-.139.542a.9.9 0 0 1-.388.352a1.3 1.3 0 0 1-.58.123h-.75v-.613h.59a.5.5 0 0 0 .237-.049a.35.35 0 0 0 .152-.14a.4.4 0 0 0 .054-.215a.4.4 0 0 0-.054-.213a.34.34 0 0 0-.152-.136a.5.5 0 0 0-.237-.048h-.278V11zm3.415-2.91V11h-.79V8.09z\"/><path stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.318 2a1 1 0 0 0-1 1v.729l-.18.073l-.553-.538a1 1 0 0 0-1.396 0L3.31 5.096a1 1 0 0 0 0 1.432l.529.515q-.03.067-.058.137H3a1 1 0 0 0-1 1v2.59a1 1 0 0 0 1 1h.78l.058.136l-.529.515a1 1 0 0 0 0 1.433l1.88 1.83a1 1 0 0 0 1.396 0l.552-.537q.09.038.181.073v.73a1 1 0 0 0 1 1h2.66a1 1 0 0 0 1-1v-.73q.196-.076.388-.165l.543.261v.434a1 1 0 0 0 1 1H14v.063a1 1 0 0 0 1 1h.09v.062a1 1 0 0 0 1 1h.685l.495.482a3 3 0 0 0 4.187 0l.24-.234a1 1 0 0 0-.016-1.449l-6.216-5.784l.05-.12h.78a1 1 0 0 0 1-1V8.18a1 1 0 0 0-1-1h-.78l-.057-.137l.528-.515a1 1 0 0 0 0-1.432l-1.88-1.832a1 1 0 0 0-1.396 0l-.552.538q-.09-.038-.18-.073V3a1 1 0 0 0-1-1z\"/></g>"}, "apple-full-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10.042c-1.39 0-1.393-1.125-4.206-1.037c-1.731.054-3.156 2.23-2.712 5.308C5.359 16.238 6.986 21 8.992 21c2.005 0 2.073-.85 3.008-.85s.855.85 3.008.85s3.633-4.762 3.91-6.687c.444-3.078-.981-5.254-2.712-5.308c-2.813-.088-2.816 1.037-4.206 1.037m2.36-4.213c-.878.749-1.861.72-2.722.72c0-.626.079-1.94.86-2.77c.782-.828 2.128-.766 2.644-.766c0 .595.096 2.068-.782 2.816\"/>"}, "apple-full-solid": {"body": "<path fill=\"currentColor\" d=\"M15.052 2.013a7 7 0 0 0-1.306.088c-.605.107-1.377.359-1.975.993c-.557.59-.83 1.315-.97 1.922a7 7 0 0 0-.163 1.533v1h1.133c.817.004 2.103.009 3.237-.959c.703-.598.958-1.432 1.06-2.068c.087-.544.08-1.082.075-1.378l-.001-.13v-1h-1zM12 9.042c-.293 0-.397-.057-.726-.236c-.103-.055-.228-.123-.387-.205c-.68-.35-1.552-.645-3.125-.596c-1.31.041-2.377.882-3.015 2.036c-.64 1.16-.902 2.7-.655 4.415c.154 1.07.662 2.84 1.396 4.358c.368.76.818 1.512 1.35 2.09c.517.56 1.243 1.096 2.154 1.096c1.178 0 1.862-.259 2.39-.55c.166-.092.276-.156.352-.2c.116-.068.154-.09.195-.097c.02-.004.04-.004.07-.004h.058c.02.004.038.015.095.052c.064.04.176.113.393.238c.532.31 1.226.561 2.463.561c.93 0 1.671-.514 2.21-1.09c.546-.584.994-1.343 1.353-2.106c.719-1.526 1.185-3.294 1.337-4.348c.247-1.714-.014-3.255-.655-4.415c-.638-1.154-1.706-1.995-3.015-2.036c-1.573-.049-2.445.247-3.125.596c-.16.082-.284.15-.387.205c-.329.18-.433.236-.726.236\"/>"}, "apple-solid": {"body": "<path fill=\"currentColor\" d=\"M17.537 12.625a4.42 4.42 0 0 0 2.684 4.047a11 11 0 0 1-1.384 2.845c-.834 1.218-1.7 2.432-3.062 2.457c-1.34.025-1.77-.794-3.3-.794c-1.531 0-2.01.769-3.275.82c-1.316.049-2.317-1.318-3.158-2.532c-1.72-2.484-3.032-7.017-1.27-10.077A4.9 4.9 0 0 1 8.91 6.884c1.292-.025 2.51.869 3.3.869c.789 0 2.27-1.075 3.828-.917a4.67 4.67 0 0 1 3.66 1.984a4.52 4.52 0 0 0-2.16 3.805m-2.52-7.432A4.4 4.4 0 0 0 16.06 2a4.48 4.48 0 0 0-2.945 1.516a4.18 4.18 0 0 0-1.061 3.093a3.7 3.7 0 0 0 2.967-1.416Z\"/>"}, "archive-arrow-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 11v5m0 0l2-2m-2 2l-2-2M3 6v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1m2 2v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V8z\"/>"}, "archive-arrow-down-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 1 0 0 4h16a2 2 0 1 0 0-4zm0 6h16v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm10.707 5.707a1 1 0 0 0-1.414-1.414l-.293.293V12a1 1 0 1 0-2 0v2.586l-.293-.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0z\" clip-rule=\"evenodd\"/>"}, "archive-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 12v1h4v-1m4 7H6a1 1 0 0 1-1-1V9h14v9a1 1 0 0 1-1 1ZM4 5h16a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z\"/>"}, "archive-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2zM9 13v-1h6v1a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/><path d=\"M2 6a2 2 0 0 1 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2\"/></g>"}, "arrow-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19V5m0 14l-4-4m4 4l4-4\"/>"}, "arrow-down-to-bracket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 15v2a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-2m-8 1V4m0 12l-4-4m4 4l4-4\"/>"}, "arrow-down-to-bracket-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-linejoin=\"round\" d=\"M4 15v2a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-2\"/><path d=\"M12 15.5V4\"/><path stroke-linejoin=\"round\" d=\"m8 12l4 4l4-4\"/></g>", "hidden": true}, "arrow-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12h14M5 12l4-4m-4 4l4 4\"/>"}, "arrow-left-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M6 12h13\"/><path stroke-linejoin=\"round\" d=\"m9 8l-4 4l4 4\"/></g>", "hidden": true}, "arrow-left-to-bracket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12H4m12 0l-4 4m4-4l-4-4m3-4h2a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-2\"/>"}, "arrow-left-to-bracket-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M15.5 12H4\"/><path stroke-linejoin=\"round\" d=\"M15 4h2a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-2\"/><path stroke-linejoin=\"round\" d=\"m12 16l4-4l-4-4\"/></g>", "hidden": true}, "arrow-right-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.153 19L21 12l-4.847-7H3l4.848 7L3 19z\"/>"}, "arrow-right-alt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43z\" clip-rule=\"evenodd\"/>"}, "arrow-right-arrow-left-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M4 16h13m3-8H7\"/><path stroke-linejoin=\"round\" d=\"m8 12l-4 4l4 4m8-8l4-4l-4-4\"/></g>", "hidden": true}, "arrow-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 12H5m14 0l-4 4m4-4l-4-4\"/>"}, "arrow-right-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M18 12H5\"/><path stroke-linejoin=\"round\" d=\"m15 16l4-4l-4-4\"/></g>", "hidden": true}, "arrow-right-to-bracket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 12H8m12 0l-4 4m4-4l-4-4M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2\"/>"}, "arrow-right-to-bracket-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M19.5 12H8\"/><path stroke-linejoin=\"round\" d=\"M9 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h2m7-4l4-4l-4-4\"/></g>", "hidden": true}, "arrow-sort-letters-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4v16M7 4l3 3M7 4L4 7m9-3h6l-6 6h6m-6.5 10l3.5-7l3.5 7M14 18h4\"/>"}, "arrow-sort-letters-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M7 5v15\"/><path stroke-linejoin=\"round\" d=\"M10 7L7 4L4 7m9-3h6l-6 6h6m-6.5 10l3.5-7l3.5 7M14 18h4\"/></g>", "hidden": true}, "arrow-up-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 20V7m0 13l-4-4m4 4l4-4m4-12v13m0-13l4 4m-4-4l-4 4\"/>"}, "arrow-up-down-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M8 20V7m8-3v13\"/><path stroke-linejoin=\"round\" d=\"m4 16l4 4l4-4m8-8l-4-4l-4 4\"/></g>", "hidden": true}, "arrow-up-from-bracket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 15v2a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-2M12 4v12m0-12l4 4m-4-4L8 8\"/>"}, "arrow-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v13m0-13l4 4m-4-4l-4 4\"/>"}, "arrow-up-right-down-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 4h4m0 0v4m0-4l-5 5M8 20H4m0 0v-4m0 4l5-5\"/>"}, "arrow-up-right-down-left-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 4h4v4m-.5-3.5L15 9M8 20H4v-4m.5 3.5L9 15\"/>", "hidden": true}, "arrow-up-right-from-square-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121l7.778-7.778\"/>"}, "arrow-up-right-from-square-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M11.403 5H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-6.403a3 3 0 0 1-1.743-1.612l-3.025 3.025A3 3 0 1 1 9.99 9.768l3.025-3.025A3 3 0 0 1 11.403 5\"/><path d=\"M13.232 4a1 1 0 0 1 1-1H20a1 1 0 0 1 1 1v5.768a1 1 0 1 1-2 0V6.414l-6.182 6.182a1 1 0 0 1-1.414-1.414L17.586 5h-3.354a1 1 0 0 1-1-1\"/></g>"}, "arrow-up-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M12 18V5\"/><path stroke-linejoin=\"round\" d=\"m8 15l4 4l4-4\"/></g>", "hidden": true}, "arrows-repeat-count-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m16 4l3 3H5v3m3 10l-3-3h14v-3m-9-2.5l2-1.5v4\"/>"}, "arrows-repeat-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m16 10l3-3m0 0l-3-3m3 3H5v3m3 4l-3 3m0 0l3 3m-3-3h14v-3\"/>"}, "atom-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8.737 8.737a21.5 21.5 0 0 1 3.308-2.724m0 0c3.063-2.026 5.99-2.641 7.331-1.3c1.827 1.828.026 6.591-4.023 10.64s-8.812 5.85-10.64 4.023c-1.33-1.33-.736-4.218 1.249-7.253m6.083-6.11c-3.063-2.026-5.99-2.641-7.331-1.3c-1.827 1.828-.026 6.591 4.023 10.64m3.308-9.34a21.5 21.5 0 0 1 3.308 2.724m2.775 3.386c1.985 3.035 2.579 5.923 1.248 7.253c-1.336 1.337-4.245.732-7.295-1.275M14 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/>"}, "atom-solid": {"body": "<g fill=\"none\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.2 6L8 8a1 1 0 0 0 1.4 1.4A21 21 0 0 1 12 7.2a21 21 0 0 1 2.6 2.2A1 1 0 0 0 16.1 8l-2.2-2l2.6-1c1.2-.1 1.8 0 2.2.4c.4.5.6 1.6 0 3.4c-.7 1.8-2.1 3.9-4 5.8c-2 2-4 3.4-5.9 4c-1.8.7-3 .5-3.4 0c-.3-.3-.5-1-.3-2a9 9 0 0 1 1-2.7L8 16a1 1 0 0 0 1.3-1.5c-1.9-1.9-3.3-4-4-5.8c-.6-1.8-.4-3 0-3.4c.4-.3 1-.5 2.2-.3c.7.1 1.6.5 2.6 1ZM12 4.9c1.5-.8 2.9-1.4 4.2-1.7C17.6 3 19 3 20 4.1c1.3 1.3 1.2 3.5.4 5.5a15 15 0 0 1-1.2 2.4c.8 1.5 1.4 3 1.7 4.2c.2 1.4 0 2.9-1 3.9s-2.4 1.1-3.8.9c-1.3-.3-2.7-.9-4.2-1.7l-2.4 1.2c-2 .8-4.2 1-5.6-.4c-1-1-1.1-2.5-.9-3.9A12 12 0 0 1 4.7 12a15 15 0 0 1-1.2-2.4c-.8-2-1-4.2.4-5.6C5 3 6.5 3 8 3.1c1.2.3 2.6.9 4 1.7ZM14 18a9 9 0 0 0 2.7 1c1 .2 1.7 0 2-.3c.4-.4.6-1 .4-2.1a9 9 0 0 0-1-2.7A23.4 23.4 0 0 1 14 18\" clip-rule=\"evenodd\"/><path fill=\"currentColor\" d=\"M14 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/><path stroke=\"currentColor\" d=\"M10.2 6L8 8a1 1 0 0 0 1.4 1.4A21 21 0 0 1 12 7.2a21 21 0 0 1 2.6 2.2A1 1 0 0 0 16.1 8l-2.2-2l2.6-1c1.2-.1 1.8 0 2.2.4c.4.5.6 1.6 0 3.4c-.7 1.8-2.1 3.9-4 5.8c-2 2-4 3.4-5.9 4c-1.8.7-3 .5-3.4 0c-.3-.3-.5-1-.3-2a9 9 0 0 1 1-2.7L8 16a1 1 0 0 0 1.3-1.5c-1.9-1.9-3.3-4-4-5.8c-.6-1.8-.4-3 0-3.4c.4-.3 1-.5 2.2-.3c.7.1 1.6.5 2.6 1ZM12 4.9c1.5-.8 2.9-1.4 4.2-1.7C17.6 3 19 3 20 4.1c1.3 1.3 1.2 3.5.4 5.5a15 15 0 0 1-1.2 2.4c.8 1.5 1.4 3 1.7 4.2c.2 1.4 0 2.9-1 3.9s-2.4 1.1-3.8.9c-1.3-.3-2.7-.9-4.2-1.7l-2.4 1.2c-2 .8-4.2 1-5.6-.4c-1-1-1.1-2.5-.9-3.9A12 12 0 0 1 4.7 12a15 15 0 0 1-1.2-2.4c-.8-2-1-4.2.4-5.6C5 3 6.5 3 8 3.1c1.2.3 2.6.9 4 1.7ZM14 18a9 9 0 0 0 2.7 1c1 .2 1.7 0 2-.3c.4-.4.6-1 .4-2.1a9 9 0 0 0-1-2.7A23.4 23.4 0 0 1 14 18Z\" clip-rule=\"evenodd\"/><path stroke=\"currentColor\" d=\"M14 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/></g>", "hidden": true}, "award-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.171 12.906l-2.153 6.411l2.672-.89l1.568 2.34l1.825-5.183m5.73-2.678l2.154 6.411l-2.673-.89l-1.568 2.34l-1.825-5.183M9.165 4.3c.58.068 1.153-.17 1.515-.628a1.68 1.68 0 0 1 2.64 0a1.68 1.68 0 0 0 1.515.628a1.68 1.68 0 0 1 1.866 1.866c-.068.58.17 1.154.628 1.516a1.68 1.68 0 0 1 0 2.639a1.68 1.68 0 0 0-.628 1.515a1.68 1.68 0 0 1-1.866 1.866a1.68 1.68 0 0 0-1.516.628a1.68 1.68 0 0 1-2.639 0a1.68 1.68 0 0 0-1.515-.628a1.68 1.68 0 0 1-1.867-1.866a1.68 1.68 0 0 0-.627-1.515a1.68 1.68 0 0 1 0-2.64c.458-.361.696-.935.627-1.515A1.68 1.68 0 0 1 9.165 4.3M14 9a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "award-solid": {"body": "<g fill=\"currentColor\"><path d=\"M11 9a1 1 0 1 1 2 0a1 1 0 0 1-2 0\"/><path fill-rule=\"evenodd\" d=\"M9.896 3.051a2.68 2.68 0 0 1 4.208 0c.147.186.38.282.615.255a2.68 2.68 0 0 1 2.976 2.975a.68.68 0 0 0 .254.615a2.68 2.68 0 0 1 0 4.208a.68.68 0 0 0-.254.615a2.68 2.68 0 0 1-2.976 2.976a.68.68 0 0 0-.615.254a2.682 2.682 0 0 1-4.208 0a.68.68 0 0 0-.614-.255a2.68 2.68 0 0 1-2.976-2.975a.68.68 0 0 0-.255-.615a2.68 2.68 0 0 1 0-4.208a.68.68 0 0 0 .255-.615a2.68 2.68 0 0 1 2.976-2.975a.68.68 0 0 0 .614-.255M12 6a3 3 0 1 0 0 6a3 3 0 0 0 0-6\" clip-rule=\"evenodd\"/><path d=\"M5.395 15.055L4.07 19a1 1 0 0 0 1.264 1.267l1.95-.65l1.144 1.707A1 1 0 0 0 10.2 21.1l1.12-3.18a4.64 4.64 0 0 1-2.515-1.208a4.67 4.67 0 0 1-3.411-1.656Zm7.269 2.867l1.12 3.177a1 1 0 0 0 1.773.224l1.144-1.707l1.95.65A1 1 0 0 0 19.915 19l-1.32-3.93a4.67 4.67 0 0 1-3.4 1.642a4.64 4.64 0 0 1-2.53 1.21Z\"/></g>"}, "aws-solid": {"body": "<g fill=\"currentColor\"><path d=\"M7.709 10.176q-.009.29.077.567q.084.237.21.458q.045.065.05.143a.24.24 0 0 1-.127.192l-.414.266a.3.3 0 0 1-.168.055a.27.27 0 0 1-.197-.09a1.6 1.6 0 0 1-.238-.293a6 6 0 0 1-.196-.376c-.23.28-.523.505-.856.658s-.698.23-1.066.224a1.82 1.82 0 0 1-1.305-.458a1.6 1.6 0 0 1-.477-1.223c-.008-.248.04-.494.142-.72c.102-.228.255-.43.448-.592a2.45 2.45 0 0 1 1.606-.492q.352 0 .701.048c.246.034.491.082.75.137v-.465a1.38 1.38 0 0 0-.308-1.018a1.52 1.52 0 0 0-1.066-.293a3.2 3.2 0 0 0-.702.082q-.359.083-.7.218l-.232.082h-.099c-.098 0-.14-.068-.14-.198v-.314a.4.4 0 0 1 .042-.219a.55.55 0 0 1 .19-.136q.394-.186.82-.287a4 4 0 0 1 1.017-.123a2.4 2.4 0 0 1 1.712.519c.196.21.345.458.438.727a2 2 0 0 1 .102.837zm-2.652.97q.361 0 .702-.116c.24-.08.453-.221.617-.41a.9.9 0 0 0 .224-.403q.069-.273.064-.553v-.287a5 5 0 0 0-.625-.09h-.617a1.52 1.52 0 0 0-.968.254a.87.87 0 0 0-.323.738a.87.87 0 0 0 .239.683a.95.95 0 0 0 .687.184m5.254.683a.4.4 0 0 1-.253-.061a.5.5 0 0 1-.14-.253l-1.53-4.919a1 1 0 0 1-.055-.26q-.002-.155.154-.157h.645a.4.4 0 0 1 .253.062c.068.07.114.158.133.253l1.101 4.249l1.031-4.242a.4.4 0 0 1 .127-.253a.42.42 0 0 1 .26-.062h.525a.44.44 0 0 1 .267.062a.5.5 0 0 1 .126.253l1.017 4.242l1.13-4.263a.5.5 0 0 1 .132-.253a.4.4 0 0 1 .253-.061h.61a.15.15 0 0 1 .119.041a.14.14 0 0 1 .042.116a1 1 0 0 1 0 .102s0 .09-.042.158L14.631 11.5a.5.5 0 0 1-.133.253a.43.43 0 0 1-.253.061h-.56a.38.38 0 0 1-.26-.068a.43.43 0 0 1-.126-.253l-1.017-4.098l-1.01 4.098a.43.43 0 0 1-.127.253a.4.4 0 0 1-.26.068zm8.416.164a4.5 4.5 0 0 1-1.01-.11a3 3 0 0 1-.75-.252a.5.5 0 0 1-.204-.178a.44.44 0 0 1-.042-.184v-.321q0-.197.148-.198a.4.4 0 0 1 .126 0l.161.068q.338.147.702.219q.384.082.778.082c.332.02.663-.054.954-.212a.7.7 0 0 0 .251-.257a.67.67 0 0 0 .086-.344a.6.6 0 0 0-.176-.444a1.6 1.6 0 0 0-.66-.335l-.946-.294c-.41-.1-.776-.325-1.045-.642a1.5 1.5 0 0 1-.33-.929a1.34 1.34 0 0 1 .176-.683c.117-.206.277-.385.47-.526c.21-.151.448-.262.701-.328c.269-.076.548-.112.828-.11q.217-.015.435 0l.427.069l.372.102l.274.11q.11.063.196.157q.06.098.056.212v.3q0 .205-.147.205a.7.7 0 0 1-.253-.082a3.1 3.1 0 0 0-1.262-.246a1.8 1.8 0 0 0-.87.178a.64.64 0 0 0-.236.245a.6.6 0 0 0-.073.329a.6.6 0 0 0 .197.45c.207.162.446.28.701.349l.926.287a2.03 2.03 0 0 1 1.01.615c.201.256.307.572.302.895a1.6 1.6 0 0 1-.169.737c-.112.22-.276.412-.477.56a2.1 2.1 0 0 1-.701.363a3.3 3.3 0 0 1-.947.15zm-16.77 2.011c.089-.111.253-.141.403-.05a19.7 19.7 0 0 0 9.711 2.543a19.5 19.5 0 0 0 7.414-1.493c.221-.092.442-.025.55.127a.34.34 0 0 1 .057.273a.46.46 0 0 1-.193.267l-.003.002a14.4 14.4 0 0 1-8.05 2.427a14.68 14.68 0 0 1-9.828-3.693a.33.33 0 0 1-.116-.203a.26.26 0 0 1 .055-.2\"/><path d=\"M21.985 13.236a.6.6 0 0 0-.226-.144a2 2 0 0 0-.35-.104a4.6 4.6 0 0 0-.997-.088c-.754.011-1.65.183-2.334.675c-.112.08-.195.195-.175.315c.022.136.155.192.286.192l.024-.003c.375-.047.965-.12 1.5-.134c.27-.008.523 0 .723.034q.151.024.251.066a.3.3 0 0 1 .132.09q.034.039.05.144q.013.106-.002.26c-.02.206-.077.457-.153.724c-.117.415-.276.857-.401 1.208l-.097.272a.32.32 0 0 0-.02.19a.21.21 0 0 0 .11.144c.***************.355-.084c.644-.55 1.05-1.394 1.26-2.135c.105-.37.163-.72.176-1q.01-.212-.014-.368a.5.5 0 0 0-.098-.254\"/></g>"}, "backward-step-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 6v12m8-12v12l-8-6z\"/>"}, "backward-step-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7 6a1 1 0 0 1 2 0v4l6.4-4.8A1 1 0 0 1 17 6v12a1 1 0 0 1-1.6.8L9 14v4a1 1 0 1 1-2 0z\" clip-rule=\"evenodd\"/>"}, "bacon-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" stroke-width=\"2\" d=\"M7.426 18.244c1.317-2.202 2.533-1.524 3.85-3.726s.1-2.88 1.417-5.083c1.317-2.202 2.534-1.524 3.85-3.727m-5.696 14.444c1.317-2.203 2.534-1.524 3.85-3.727s.1-2.88 1.418-5.083c1.317-2.202 2.533-1.524 3.85-3.726L13.122 3.8c-1.317 2.203-2.533 1.524-3.85 3.727s-.1 2.88-1.417 5.083c-1.317 2.202-2.534 1.524-3.85 3.726z\"/>"}, "bacon-solid": {"body": "<path fill=\"currentColor\" d=\"m16.133 4.335l-2.524-1.407a1 1 0 0 0-1.345.36c-.53.887-.995 1.166-1.571 1.513l-.011.007c-.686.412-1.485.896-2.268 2.207c-.786 1.313-.823 2.232-.85 3.02v.018c-.023.644-.041 1.164-.568 2.044c-.53.887-.995 1.167-1.571 1.514l-.011.006c-.685.412-1.484.897-2.268 2.207a1 1 0 0 0 .372 1.387l2.621 1.461c.142-.413.306-.737.428-.94c.784-1.311 1.583-1.796 2.268-2.208l.011-.006c.577-.347 1.042-.627 1.572-1.513c.526-.88.544-1.4.567-2.045v-.019c.028-.787.065-1.706.85-3.02c.784-1.31 1.583-1.794 2.268-2.206l.01-.007c.575-.345 1.038-.624 1.565-1.5c.124-.222.296-.546.454-.873M7.93 19.67l2.43 1.354a1 1 0 0 0 1.344-.36c.53-.887.995-1.166 1.572-1.513l.01-.007c.686-.412 1.485-.896 2.269-2.207c.785-1.313.822-2.232.85-3.02V13.9c.023-.644.04-1.164.567-2.044c.53-.887.995-1.167 1.572-1.514l.01-.006c.686-.412 1.485-.897 2.268-2.207a1 1 0 0 0-.37-1.387L17.881 5.31a20 20 0 0 1-.48.912c-.783 1.31-1.583 1.794-2.268 2.207l-.01.006c-.577.347-1.042.627-1.572 1.513c-.527.88-.545 1.4-.567 2.045v.018c-.028.787-.065 1.706-.85 3.02c-.784 1.31-1.583 1.795-2.269 2.207l-.01.006c-.577.347-1.042.627-1.572 1.513c-.102.17-.257.495-.353.914\"/>"}, "badge-check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8.032 12l1.984 1.984l4.96-4.96m4.55 5.272l.893-.893a1.984 1.984 0 0 0 0-2.806l-.893-.893a1.98 1.98 0 0 1-.581-1.403V7.04a1.984 1.984 0 0 0-1.984-1.984h-1.262a1.98 1.98 0 0 1-1.403-.581l-.893-.893a1.984 1.984 0 0 0-2.806 0l-.893.893a1.98 1.98 0 0 1-1.403.581H7.04A1.984 1.984 0 0 0 5.055 7.04v1.262c0 .527-.209 1.031-.581 1.403l-.893.893a1.984 1.984 0 0 0 0 2.806l.893.893c.372.372.581.876.581 1.403v1.262a1.984 1.984 0 0 0 1.984 1.984h1.262c.527 0 1.031.209 1.403.581l.893.893a1.984 1.984 0 0 0 2.806 0l.893-.893a2 2 0 0 1 1.403-.581h1.262a1.984 1.984 0 0 0 1.984-1.984V15.7c0-.527.209-1.031.581-1.403Z\"/>"}, "badge-check-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2c-.791 0-1.55.314-2.11.874l-.893.893a1 1 0 0 1-.696.288H7.04A2.984 2.984 0 0 0 4.055 7.04v1.262a1 1 0 0 1-.288.696l-.893.893a2.984 2.984 0 0 0 0 4.22l.893.893a1 1 0 0 1 .288.696v1.262a2.984 2.984 0 0 0 2.984 2.984h1.262c.261 0 .512.104.696.288l.893.893a2.984 2.984 0 0 0 4.22 0l.893-.893a1 1 0 0 1 .696-.288h1.262a2.984 2.984 0 0 0 2.984-2.984V15.7c0-.261.104-.512.288-.696l.893-.893a2.984 2.984 0 0 0 0-4.22l-.893-.893a1 1 0 0 1-.288-.696V7.04a2.984 2.984 0 0 0-2.984-2.984h-1.262a1 1 0 0 1-.696-.288l-.893-.893A2.98 2.98 0 0 0 12 2m3.683 7.73a1 1 0 1 0-1.414-1.413l-4.253 4.253l-1.277-1.277a1 1 0 0 0-1.415 1.414l1.985 1.984a1 1 0 0 0 1.414 0l4.96-4.96Z\" clip-rule=\"evenodd\"/>"}, "ban-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m6 6l12 12m3-6a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\"/>"}, "barcode-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\"><path stroke-width=\"2\" d=\"M2.992 4.983v13.934m6.97-13.934v13.934m5.976-13.934v13.934m2.987-13.934v13.934\"/><path d=\"M5.48 4.483v14.934M7.47 4.483v14.934M21.413 4.483v14.934M13.446 4.483v14.934\"/></g>"}, "bars-from-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 7h14M5 12h14M5 17h10\"/>"}, "bars-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 7h14M5 12h14M5 17h14\"/>"}, "battery-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M2.988 7.971a1 1 0 0 1 1-1h12.937a1 1 0 0 1 1 1v7.959a1 1 0 0 1-1 1H3.988a1 1 0 0 1-1-1zm17.925 4.97v-1.983a1 1 0 0 0-1-1h-.988a1 1 0 0 0-1 1v1.984a1 1 0 0 0 1 1h.988a1 1 0 0 0 1-1Z\"/><path d=\"M5.975 9.959h8.963v3.983H5.975z\"/></g>"}, "battery-solid": {"body": "<g fill=\"currentColor\"><path d=\"M7 13v-2h7v2z\"/><path fill-rule=\"evenodd\" d=\"M2 8a2 2 0 0 1 2-2h13a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm4 1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1z\" clip-rule=\"evenodd\"/><path d=\"M22 14v-4a1 1 0 0 0-1-1h-1v6h1a1 1 0 0 0 1-1\"/></g>"}, "bed-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 17v2M12 5.5V10m-6 7v2m15-2v-4a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3v4zm-2-7V8a3 3 0 0 0-3-3H8a3 3 0 0 0-3 3v2z\"/>"}, "bed-solid": {"body": "<path fill=\"currentColor\" d=\"M2.535 11A4 4 0 0 0 2 13v4a1 1 0 0 0 1 1h2v1a1 1 0 1 0 2 0v-1h10v1a1 1 0 1 0 2 0v-1h2a1 1 0 0 0 1-1v-4c0-.729-.195-1.412-.535-2zM20 9V8a4 4 0 0 0-4-4h-3v5zm-9-5H8a4 4 0 0 0-4 4v1h7z\"/>"}, "beer-mug-empty-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.125 6H20v3.86a4 4 0 0 1-1.781 3.328l-1.58 1.046M4.189 17h12.625M9 7v7m3-7v7M4.941 4.938l-.875 14A1 1 0 0 0 5.064 20h10.872a1 1 0 0 0 .998-1.062l-.875-14A1 1 0 0 0 15.06 4H5.939a1 1 0 0 0-.998.938\"/>"}, "beer-mug-empty-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M3.943 4.875A2 2 0 0 1 5.94 3h9.12a2 2 0 0 1 1.997 1.875l.008.125H20a1 1 0 0 1 1 1v3.86a5 5 0 0 1-2.226 4.16l-1.1.733l.078 1.247H3.248zm13.587 7.57l.134-.09A3 3 0 0 0 19 9.86V7h-1.81zM10 7a1 1 0 0 0-2 0v7a1 1 0 1 0 2 0zm3 0a1 1 0 1 0-2 0v7a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/><path d=\"m3.123 18l-.055.875A2 2 0 0 0 5.065 21h10.87a2 2 0 0 0 1.997-2.125L17.877 18z\"/></g>"}, "bell-active-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5.365V3m0 2.365a5.34 5.34 0 0 1 5.133 5.368v1.8c0 2.386 1.867 2.982 1.867 4.175c0 .593 0 1.193-.538 1.193H5.538c-.538 0-.538-.6-.538-1.193c0-1.193 1.867-1.789 1.867-4.175v-1.8A5.34 5.34 0 0 1 12 5.365m-8.134 5.368a8.46 8.46 0 0 1 2.252-5.714m14.016 5.714a8.46 8.46 0 0 0-2.252-5.714M8.54 17.901a3.48 3.48 0 0 0 6.92 0z\"/>"}, "bell-active-alt-solid": {"body": "<path fill=\"currentColor\" d=\"M17.133 12.632v-1.8a5.41 5.41 0 0 0-4.154-5.262A1 1 0 0 0 13 5.464V3.1a1 1 0 0 0-2 0v2.364a1 1 0 0 0 .021.106a5.406 5.406 0 0 0-4.154 5.262v1.8C6.867 15.018 5 15.614 5 16.807C5 17.4 5 18 5.538 18h12.924C19 18 19 17.4 19 16.807c0-1.193-1.867-1.789-1.867-4.175m-13.267-.8a1 1 0 0 1-1-1a9.42 9.42 0 0 1 2.517-6.391A1.001 1.001 0 1 1 6.854 5.8a7.43 7.43 0 0 0-1.988 5.037a1 1 0 0 1-1 .995m16.268 0a1 1 0 0 1-1-1A7.43 7.43 0 0 0 17.146 5.8a1 1 0 0 1 1.471-1.354a9.42 9.42 0 0 1 2.517 6.391a1 1 0 0 1-1 .995M8.823 19a3.453 3.453 0 0 0 6.354 0z\"/>"}, "bell-active-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m10.827 5.465l-.435-2.324m.435 2.324a5.34 5.34 0 0 1 6.033 4.333l.331 1.769c.44 2.345 2.383 2.588 2.6 3.761c.11.586.22 1.171-.31 1.271l-12.7 2.377c-.529.099-.639-.488-.749-1.074C5.813 16.73 7.538 15.8 7.1 13.455c-.219-1.169.218 1.162-.33-1.769a5.34 5.34 0 0 1 4.058-6.221Zm-7.046 4.41c.143-1.877.822-3.461 2.086-4.856m2.646 13.633a3.472 3.472 0 0 0 6.728-.777l.09-.5z\"/>"}, "bell-active-solid": {"body": "<g fill=\"currentColor\"><path d=\"M11.209 3.816a1 1 0 0 0-1.966.368l.325 1.74a5.34 5.34 0 0 0-2.8 5.762l.276 1.473l.055.296c.258 1.374-.228 2.262-.63 2.998c-.285.52-.527.964-.437 1.449c.11.586.22 1.173.75 1.074l12.7-2.377c.528-.1.418-.685.308-1.27c-.103-.564-.636-1.123-1.195-1.711c-.606-.636-1.243-1.306-1.404-2.051c-.233-1.085-.275-1.387-.303-1.587c-.009-.063-.016-.117-.028-.182a5.34 5.34 0 0 0-5.353-4.39z\"/><path fill-rule=\"evenodd\" d=\"M6.539 4.278a1 1 0 0 1 .07 1.412c-1.115 1.23-1.705 2.605-1.83 4.26a1 1 0 0 1-1.995-.15c.16-2.099.929-3.893 2.342-5.453a1 1 0 0 1 1.413-.069\" clip-rule=\"evenodd\"/><path d=\"M8.95 19.7c.7.8 1.7 1.3 2.8 1.3c1.6 0 2.9-1.1 3.3-2.5z\"/></g>"}, "bell-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5.365V3m0 2.365a5.34 5.34 0 0 1 5.133 5.368v1.8c0 2.386 1.867 2.982 1.867 4.175c0 .593 0 1.292-.538 1.292H5.538C5 18 5 17.301 5 16.708c0-1.193 1.867-1.789 1.867-4.175v-1.8A5.34 5.34 0 0 1 12 5.365M8.733 18c.094.852.306 1.54.944 2.112a3.48 3.48 0 0 0 4.646 0c.638-.572 1.236-1.26 1.33-2.112z\"/>"}, "bell-ring-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5.464V3.099m0 2.365a5.34 5.34 0 0 1 5.133 5.368v1.8c0 2.386 1.867 2.982 1.867 4.175C19 17.4 19 18 18.462 18H5.538C5 18 5 17.4 5 16.807c0-1.193 1.867-1.789 1.867-4.175v-1.8A5.34 5.34 0 0 1 12 5.464M6 5L5 4M4 9H3m15-4l1-1m1 5h1M8.54 18a3.48 3.48 0 0 0 6.92 0z\"/>"}, "bell-ring-solid": {"body": "<path fill=\"currentColor\" d=\"M17.133 12.632v-1.8a5.406 5.406 0 0 0-4.154-5.262A1 1 0 0 0 13 5.464V3.1a1 1 0 0 0-2 0v2.364a1 1 0 0 0 .021.106a5.406 5.406 0 0 0-4.154 5.262v1.8C6.867 15.018 5 15.614 5 16.807C5 17.4 5 18 5.538 18h12.924C19 18 19 17.4 19 16.807c0-1.193-1.867-1.789-1.867-4.175M6 6a1 1 0 0 1-.707-.293l-1-1a1 1 0 0 1 1.414-1.414l1 1A1 1 0 0 1 6 6m-2 4H3a1 1 0 0 1 0-2h1a1 1 0 1 1 0 2m14-4a1 1 0 0 1-.707-1.707l1-1a1 1 0 1 1 1.414 1.414l-1 1A1 1 0 0 1 18 6m3 4h-1a1 1 0 1 1 0-2h1a1 1 0 1 1 0 2M8.823 19a3.453 3.453 0 0 0 6.354 0z\"/>"}, "bell-solid": {"body": "<path fill=\"currentColor\" d=\"M17.133 12.632v-1.8a5.406 5.406 0 0 0-4.154-5.262A1 1 0 0 0 13 5.464V3.1a1 1 0 0 0-2 0v2.364a1 1 0 0 0 .021.106a5.406 5.406 0 0 0-4.154 5.262v1.8C6.867 15.018 5 15.614 5 16.807C5 17.4 5 18 5.538 18h12.924C19 18 19 17.4 19 16.807c0-1.193-1.867-1.789-1.867-4.175M8.823 19a3.453 3.453 0 0 0 6.354 0z\"/>"}, "bitcoin-solid": {"body": "<g fill=\"currentColor\"><path d=\"M10.737 14.588c.895.236 2.853.754 3.164-.497c.318-1.278-1.58-1.704-2.505-1.912l-.27-.062l-.602 2.415zm.845-3.53c.747.2 2.375.634 2.658-.502c.29-1.162-1.293-1.512-2.066-1.683l-.226-.052l-.546 2.19z\"/><path fill-rule=\"evenodd\" d=\"M9.58 21.7c5.357 1.336 10.783-1.924 12.119-7.28c1.335-5.358-1.925-10.785-7.282-12.12C9.06.963 3.634 4.223 2.299 9.581C.963 14.94 4.224 20.365 9.58 21.701m4.629-13.647c1.385.477 2.398 1.192 2.2 2.523c-.145.974-.685 1.445-1.402 1.611c.984.513 1.485 1.299 1.008 2.661c-.592 1.692-1.999 1.835-3.87 1.481l-.454 1.82l-1.097-.274l.448-1.795a41 41 0 0 1-.874-.227l-.45 1.804l-1.096-.274l.454-1.823l-.304-.079l-.478-.123l-1.428-.356l.545-1.256s.808.215.797.2c.31.076.449-.127.503-.262l.718-2.876l.115.029a1 1 0 0 0-.114-.037l.512-2.053c.014-.233-.067-.528-.511-.638c.017-.012-.797-.199-.797-.199l.292-1.171l1.513.377l-.001.006q.342.084.7.165l.45-1.802l1.097.273l-.44 1.767c.293.067.59.135.878.207l.437-1.755l1.098.273z\" clip-rule=\"evenodd\"/></g>"}, "blender-phone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20.283 8h-4.285m3.85 3h-3.85m4.061-6H11v11h8.27l1.715-9.847A.983.983 0 0 0 20.059 5M6.581 13.23h-.838A14 14 0 0 1 5.622 11q-.03-1.119.12-2.23h1.04c.252 0 .496-.088.683-.245a.93.93 0 0 0 .329-.61l.2-1.872a.9.9 0 0 0-.045-.39a.9.9 0 0 0-.212-.34a1 1 0 0 0-.341-.231A1.1 1.1 0 0 0 6.983 5h-2.06a1.27 1.27 0 0 0-.699.204a1.14 1.14 0 0 0-.442.543A15.1 15.1 0 0 0 3.007 11a15.7 15.7 0 0 0 .795 5.229c.165.462 1.342.771 1.864.771h1.116c.142 0 .283-.028.413-.082q.197-.081.341-.23a.9.9 0 0 0 .212-.34a.9.9 0 0 0 .046-.391l-.201-1.873a.93.93 0 0 0-.33-.609a1.06 1.06 0 0 0-.682-.245M10 18v1h10v-1a2 2 0 0 0-2-2h-6a2 2 0 0 0-2 2\"/>"}, "blender-phone-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 4a1 1 0 0 0-1 1v10h10.459l.522-3H16a1 1 0 1 1 0-2h5.33l.174-1H16a1 1 0 1 1 0-2h5.852l.117-.67v-.003A1.983 1.983 0 0 0 20.06 4zM9 18c0-.35.06-.687.17-1h11.66c.11.313.17.65.17 1v1a1 1 0 0 1-1 1H10a1 1 0 0 1-1-1zm-6.991-7a17.8 17.8 0 0 0 .953 6.1c.198.54 1.61.9 2.237.9h1.34c.17 0 .339-.032.495-.095a1.2 1.2 0 0 0 .41-.27c.114-.114.2-.25.254-.396a1 1 0 0 0 .055-.456l-.242-2.185a1.07 1.07 0 0 0-.395-.71a1.3 1.3 0 0 0-.819-.286H5.291Q5.11 12.306 5.146 11q-.036-1.307.145-2.602H6.54c.302 0 .594-.102.818-.286a1.07 1.07 0 0 0 .396-.71l.24-2.185a1 1 0 0 0-.054-.456a1.1 1.1 0 0 0-.254-.397a1.2 1.2 0 0 0-.41-.269A1.3 1.3 0 0 0 6.78 4H4.307c-.3-.001-.592.082-.838.238a1.34 1.34 0 0 0-.531.634A17.1 17.1 0 0 0 2.008 11Z\" clip-rule=\"evenodd\"/>"}, "bone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" stroke-width=\"2\" d=\"M16.888 7.112c.179-.775-.12-1.669-.715-2.265c-1.014-1.013-2.564-1.013-3.577 0s-1.013 2.563 0 3.576l-4.173 4.173c-1.013-1.013-2.563-1.013-3.576 0s-1.013 2.563 0 3.577c.596.596 1.49.894 2.265.715c-.179.775.12 1.669.715 2.265c1.014 1.013 2.564 1.013 3.577 0s1.013-2.563 0-3.576l4.173-4.173c1.013 1.013 2.563 1.013 3.576 0s1.013-2.563 0-3.577c-.596-.596-1.49-.894-2.265-.715\"/>"}, "bone-solid": {"body": "<path fill=\"currentColor\" d=\"M4.14 11.889c-1.404 1.404-1.404 3.587 0 4.99a3.66 3.66 0 0 0 1.966 1.015A3.67 3.67 0 0 0 7.12 19.86c1.404 1.404 3.587 1.404 4.991 0c1.157-1.157 1.36-2.843.61-4.187l2.953-2.952c1.343.75 3.03.547 4.186-.61c1.404-1.404 1.404-3.587 0-4.99a3.67 3.67 0 0 0-1.966-1.016A3.67 3.67 0 0 0 16.88 4.14c-1.404-1.404-3.587-1.404-4.991 0c-1.157 1.157-1.36 2.843-.61 4.187l-2.952 2.952c-1.344-.75-3.03-.547-4.187.61\"/>"}, "book-open-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6.03v13m0-13c-2.819-.831-4.715-1.076-8.029-1.023A.99.99 0 0 0 3 6v11c0 .563.466 1.014 1.03 1.007c3.122-.043 5.018.212 7.97 1.023m0-13c2.819-.831 4.715-1.076 8.029-1.023A.99.99 0 0 1 21 6v11c0 .563-.466 1.014-1.03 1.007c-3.122-.043-5.018.212-7.97 1.023\"/>"}, "book-open-reader-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12.143 11v9m0-9c-2.506-.71-3.191-1.395-6.137-1.35a.864.864 0 0 0-.863.85v7.288c0 .483.414.869.916.862c2.775-.036 3.46.656 6.084 1.35m0-9c2.505-.71 3.107-1.395 6.052-1.35c.48.008.948.388.948.85v7.288c0 .483-.499.869-1 .862c-2.775-.036-3.376.656-6 1.35m2-14a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/>"}, "book-open-reader-solid": {"body": "<path fill=\"currentColor\" d=\"M9 6a3 3 0 1 1 6 0a3 3 0 0 1-6 0m2 3.63l-.372-.131c-1.34-.475-2.493-.884-4.78-.849C4.848 8.666 4 9.464 4 10.5v7.288c0 1.088.92 1.875 1.929 1.862c1.283-.017 2.046.132 2.797.359c.292.088.577.186.903.298l.003.001l.379.13c.299.102.624.209.989.319zm2-.004v11.13a35 35 0 0 0 1.455-.484c.295-.103.557-.195.784-.265c.726-.225 1.466-.374 2.748-.357c.502.007.98-.18 1.34-.479c.362-.3.673-.78.673-1.383V10.5c0-.581-.292-1.05-.638-1.35a2.04 2.04 0 0 0-1.294-.5c-2.267-.035-3.386.37-4.685.839z\"/>"}, "book-open-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11 4.717c-2.286-.58-4.16-.756-7.045-.71A1.99 1.99 0 0 0 2 6v11c0 1.133.934 2.022 2.044 2.007c2.759-.038 4.5.16 6.956.791zm2 15.081c2.456-.631 4.198-.829 6.956-.791A2.013 2.013 0 0 0 22 16.999V6a1.99 1.99 0 0 0-1.955-1.993c-2.885-.046-4.76.13-7.045.71z\" clip-rule=\"evenodd\"/>"}, "book-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 19V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v13H7a2 2 0 0 0-2 2m0 0a2 2 0 0 0 2 2h12M9 3v14m7 0v4\"/>"}, "book-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 2a2 2 0 0 0-2 2v15a3 3 0 0 0 3 3h12a1 1 0 1 0 0-2h-2v-2h2a1 1 0 0 0 1-1V4a2 2 0 0 0-2-2h-8v16h5v2H7a1 1 0 1 1 0-2h1V2z\" clip-rule=\"evenodd\"/>"}, "bookmark-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m17 21l-5-4l-5 4V3.889a.92.92 0 0 1 .244-.629a.8.8 0 0 1 .59-.26h8.333a.8.8 0 0 1 .589.26a.92.92 0 0 1 .244.63z\"/>"}, "bookmark-solid": {"body": "<path fill=\"currentColor\" d=\"M7.833 2c-.507 0-.98.216-1.318.576A1.92 1.92 0 0 0 6 3.89V21a1 1 0 0 0 1.625.78L12 18.28l4.375 3.5A1 1 0 0 0 18 21V3.889c0-.481-.178-.954-.515-1.313A1.8 1.8 0 0 0 16.167 2z\"/>"}, "booth-curtain-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 5v14m14 0V8h2M3 8h6m0-2v8.586c0 .89 1.077 1.337 1.707.707l.586-.586a1 1 0 0 1 1.414 0l.586.586a1 1 0 0 0 1.414 0l.586-.586a1 1 0 0 1 1.414 0l.586.586c.63.63 1.707.184 1.707-.707V6a1 1 0 0 0-1-1h-8a1 1 0 0 0-1 1Z\"/>"}, "booth-curtain-solid": {"body": "<path fill=\"currentColor\" d=\"M4 5v2H3a1 1 0 0 0 0 2h1v10a1 1 0 1 0 2 0V9h2v5.586c0 1.782 2.154 2.674 3.414 1.414l.586-.586l.586.586a2 2 0 0 0 2.828 0l.586-.586l.586.586c.411.411.918.593 1.414.59V19a1 1 0 1 0 2 0V9h1a1 1 0 1 0 0-2h-1V6a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2v1H6V5a1 1 0 0 0-2 0\"/>"}, "bowl-food-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.011 13H20c-.367 2.555-2.32 4.683-4.977 5.616V20H8.977v-1.384C6.32 17.683 4.367 15.556 4 13zm0 0a2 2 0 0 0 .675-3.88M18.01 13H18m0-4c.24 0 .472.043.686.12m0 0c.836-1.033.753-2.67-.28-3.506a2.41 2.41 0 0 0-3.387.356c-.293-1.502-1.748-2.154-3.25-1.86a2.77 2.77 0 0 0-2.19 3.25c-.599-1.187-1.96-2.096-3.424-1.359C4.967 6.6 4.45 8.192 5.049 9.38m0 0C4.371 9.665 4 10.336 4 11.117C4 12.157 4.843 13 5.883 13m-.834-3.62c.287-.12 1.096-.4 2.045.342m5.275-.891a2 2 0 0 1 2.65.988\"/>"}, "bowl-food-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M21 11a3 3 0 0 0-.984-2.221c.503-1.339.218-2.972-.981-3.942a3.41 3.41 0 0 0-3.627-.421a3.3 3.3 0 0 0-1.09-.932c-.823-.439-1.803-.54-2.741-.356a3.77 3.77 0 0 0-2.68 2.074a4 4 0 0 0-.278-.147c-.86-.407-1.891-.462-2.914.053c-1.406.708-2.13 2.353-1.844 3.85q-.242.217-.428.499C3.133 9.917 3 10.448 3 11a1 1 0 0 0 .995 1h16.01A1 1 0 0 0 21 11m-6.89-.765a1 1 0 0 0-1.325-.494a1 1 0 0 1-.831-1.82a3 3 0 0 1 3.975 1.483a1 1 0 0 1-1.82.83\" clip-rule=\"evenodd\"/><path d=\"M20.36 15.224c.282-.595-.196-1.224-.854-1.224H4.494c-.658 0-1.136.63-.853 1.224c.853 1.795 2.417 3.232 4.336 4.072V20a1 1 0 0 0 1 1h6.046a1 1 0 0 0 1-1v-.704c1.918-.84 3.483-2.277 4.336-4.072\"/></g>"}, "bowl-rice-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 10h.01M12 10h.01M7 10h.01M14 8h.01M10 8h.01M6.5 13C5.12 13 4 11.56 4 9.786c0-1.513.45-3.74 2.967-3.453c0-2.593 4.394-3.026 5.169-1.1c.5-.644 1.546-1.207 2.54-1.097c1.26.139 2.05 1.034 2.21 2.197c1.768 0 3.335 1.317 3.114 3.453C20 11.56 18.88 13 17.5 13m-8.523 5.616V20h6.046v-1.384c2.657-.933 4.61-3.06 4.977-5.616H4c.367 2.555 2.32 4.683 4.977 5.616\"/>"}, "bowl-rice-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12.26 3.794a3.3 3.3 0 0 0-.917-.507c-.702-.255-1.499-.305-2.24-.186c-1.159.186-2.517.882-2.975 2.228q-.608.06-1.122.31c-.6.29-1.027.738-1.323 1.23C3.114 7.813 3 8.97 3 9.786c0 .891.271 1.65.76 2.214h16.562l.03-.038c.492-.632.641-1.418.648-2.125c.123-1.327-.304-2.47-1.097-3.279a4.1 4.1 0 0 0-2.22-1.154c-.43-1.194-1.444-2.102-2.897-2.262c-.913-.101-1.819.19-2.525.652M9 8a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H10a1 1 0 0 1-1-1m4 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H14a1 1 0 0 1-1-1m-7 2a1 1 0 0 1 1-1h.01a1 1 0 0 1 0 2H7a1 1 0 0 1-1-1m5 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1m5 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H17a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/><path d=\"M20.613 14.622c.115-.309-.125-.622-.454-.622H3.841c-.33 0-.569.313-.454.622c.769 2.075 2.462 3.742 4.59 4.674V20a1 1 0 0 0 1 1h6.046a1 1 0 0 0 1-1v-.704c2.128-.932 3.821-2.599 4.59-4.674\"/></g>"}, "brain-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 18.5A2.493 2.493 0 0 1 7.51 20H7.5a2.468 2.468 0 0 1-2.4-3.154a2.98 2.98 0 0 1-.85-5.274a2.47 2.47 0 0 1 .92-3.182a2.477 2.477 0 0 1 1.876-3.344a2.5 2.5 0 0 1 3.41-1.856A2.5 2.5 0 0 1 12 5.5m0 13v-13m0 13a2.493 2.493 0 0 0 4.49 1.5h.01a2.468 2.468 0 0 0 2.403-3.154a2.98 2.98 0 0 0 .847-5.274a2.47 2.47 0 0 0-.921-3.182a2.477 2.477 0 0 0-1.875-3.344A2.5 2.5 0 0 0 14.5 3A2.5 2.5 0 0 0 12 5.5m-8 5a2.5 2.5 0 0 1 3.48-2.3m-.28 8.551a3 3 0 0 1-2.953-5.185M20 10.5a2.5 2.5 0 0 0-3.481-2.3m.28 8.551a3 3 0 0 0 2.954-5.185\"/>"}, "brain-solid": {"body": "<path fill=\"currentColor\" d=\"M11 21V2.352A3.45 3.45 0 0 0 9.5 2a3.5 3.5 0 0 0-3.261 2.238A3.5 3.5 0 0 0 4.04 8.015a3.5 3.5 0 0 0-.766 1.128c-.042.1-.064.209-.1.313a3 3 0 0 0-.106.344a3.5 3.5 0 0 0 .02 1.468A4 4 0 0 0 2.3 12.5l-.015.036a4 4 0 0 0-.216.779A4 4 0 0 0 2 14q.005.361.072.716a4 4 0 0 0 .235.832l.021.041a4 4 0 0 0 .417.727q.158.22.342.415q.109.113.225.216q.15.137.315.26c.11.081.2.14.308.2l.059.04v.053a3.506 3.506 0 0 0 3.03 3.469a3.426 3.426 0 0 0 4.154.577A.97.97 0 0 1 11 21m10.934-7.68a4 4 0 0 0-.215-.779l-.017-.038a4 4 0 0 0-.79-1.235a3.4 3.4 0 0 0 .017-1.468a3 3 0 0 0-.1-.333c-.034-.108-.057-.22-.1-.324a3.5 3.5 0 0 0-.766-1.128a3.5 3.5 0 0 0-2.202-3.777A3.5 3.5 0 0 0 14.5 2a3.45 3.45 0 0 0-1.5.352V21a.97.97 0 0 1-.184.546a3.426 3.426 0 0 0 4.154-.577A3.506 3.506 0 0 0 20 17.5v-.049l.059-.04q.159-.096.308-.2c.149-.104.214-.169.315-.26q.116-.104.225-.216a4 4 0 0 0 .459-.588q.173-.264.3-.554l.021-.041q.131-.32.205-.659q.019-.086.035-.173q.069-.356.073-.72a4 4 0 0 0-.066-.68\"/>"}, "bread-slice-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 19v-9c-1.5 0-2-1.5-2-2.5s.5-2 2-2.5c2.364-.643 4.2-.976 6-.989c1.8.013 3.636.346 6 .989c1.5.5 2 1.5 2 2.5s-.5 2.5-2 2.5v9a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1\"/>"}, "bread-slice-solid": {"body": "<path fill=\"currentColor\" d=\"M11.993 3.011c-1.922.014-3.852.37-6.256 1.024l-.053.016c-.913.305-1.603.79-2.06 1.43A3.44 3.44 0 0 0 3 7.501c0 .636.154 1.457.59 2.154c.314.503.782.943 1.41 1.174V19a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-8.171a2.8 2.8 0 0 0 1.41-1.174c.436-.697.59-1.518.59-2.155c0-.668-.169-1.381-.624-2.019c-.457-.64-1.147-1.125-2.06-1.43l-.053-.016c-2.404-.654-4.334-1.01-6.255-1.024z\"/>"}, "briefcase-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7H5a2 2 0 0 0-2 2v4m5-6h8M8 7V5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m0 0h3a2 2 0 0 1 2 2v4m0 0v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-6m18 0s-4 2-9 2s-9-2-9-2m9-2h.01\"/>"}, "briefcase-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10 2a3 3 0 0 0-3 3v1H5a3 3 0 0 0-3 3v2.382l1.447.723l.005.003l.027.013l.12.056q.163.077.486.212c.429.177 1.056.416 1.834.655C7.481 13.524 9.63 14 12 14c2.372 0 4.52-.475 6.08-.956c.78-.24 1.406-.478 1.835-.655a14 14 0 0 0 .606-.268l.027-.013l.005-.002L22 11.381V9a3 3 0 0 0-3-3h-2V5a3 3 0 0 0-3-3zm5 4V5a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v1zm6.447 7.894l.553-.276V19a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3v-5.382l.553.276l.002.002l.004.002l.013.006l.041.02l.151.07c.13.06.318.144.557.242c.478.198 1.163.46 2.01.72C7.019 15.476 9.37 16 12 16c2.628 0 4.98-.525 6.67-1.044a23 23 0 0 0 2.01-.72a16 16 0 0 0 .707-.312l.041-.02l.013-.006l.004-.002zl-.431-.866zM12 10a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "bug-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 5L9 4V3m5 2l1-1V3m-3 6v11m0-11a5 5 0 0 1 5 5m-5-5a5 5 0 0 0-5 5m5-5a4.96 4.96 0 0 1 2.973 1H15V8a3 3 0 0 0-6 0v2h.027A4.96 4.96 0 0 1 12 9m-5 5H5m2 0v2a5 5 0 0 0 10 0v-2m2.025 0H17m-9.975 4H6a1 1 0 0 0-1 1v2m12-3h1.025a1 1 0 0 1 1 1v2M16 11h1a1 1 0 0 0 1-1V8m-9.975 3H7a1 1 0 0 1-1-1V8\"/>"}, "bug-solid": {"body": "<path fill=\"currentColor\" d=\"M18 17h-.09q.087-.496.09-1v-1h1a1 1 0 0 0 0-2h-1.09a6 6 0 0 0-.26-1H17a2 2 0 0 0 2-2V8a1 1 0 1 0-2 0v2h-.54a6 6 0 0 0-.46-.46V8a3.96 3.96 0 0 0-.986-2.6l.693-.693A1 1 0 0 0 16 4V3a1 1 0 1 0-2 0v.586l-.661.661a3.75 3.75 0 0 0-2.678 0L10 3.586V3a1 1 0 1 0-2 0v1a1 1 0 0 0 .293.707l.693.693A3.96 3.96 0 0 0 8 8v1.54a6 6 0 0 0-.46.46H7V8a1 1 0 0 0-2 0v2a2 2 0 0 0 2 2h-.65a6 6 0 0 0-.26 1H5a1 1 0 0 0 0 2h1v1a6 6 0 0 0 .09 1H6a2 2 0 0 0-2 2v2a1 1 0 1 0 2 0v-2h.812A6.01 6.01 0 0 0 11 21.907V12a1 1 0 0 1 2 0v9.907A6.01 6.01 0 0 0 17.188 19H18v2a1 1 0 0 0 2 0v-2a2 2 0 0 0-2-2m-4-8.65a6 6 0 0 0-.941-.251l-.111-.017a5.5 5.5 0 0 0-1.9 0l-.111.017A6 6 0 0 0 10 8.35V8a2 2 0 1 1 4 0z\"/>"}, "building-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 4h12M6 4v16M6 4H5m13 0v16m0-16h1m-1 16H6m12 0h1M6 20H5M9 7h1v1H9zm5 0h1v1h-1zm-5 4h1v1H9zm5 0h1v1h-1zm-3 4h2a1 1 0 0 1 1 1v4h-4v-4a1 1 0 0 1 1-1\"/>"}, "building-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2v14a1 1 0 1 1 0 2H5a1 1 0 1 1 0-2V5a1 1 0 0 1-1-1m5 2a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zm5 0a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zm-5 4a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-1a1 1 0 0 0-1-1zm5 0a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-1a1 1 0 0 0-1-1zm-3 4a2 2 0 0 0-2 2v3h2v-3h2v3h2v-3a2 2 0 0 0-2-2z\" clip-rule=\"evenodd\"/>"}, "bullhorn-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 9H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h6m0-6v6m0-6l5.419-3.87A1 1 0 0 1 18 5.942v12.114a1 1 0 0 1-1.581.814L11 15m7 0a3 3 0 0 0 0-6M6 15h3v5H6z\"/>"}, "bullhorn-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18.458 3.11A1 1 0 0 1 19 4v16a1 1 0 0 1-1.581.814L12 16.944V7.056l5.419-3.87a1 1 0 0 1 1.039-.076M22 12c0 1.48-.804 2.773-2 3.465v-6.93c1.196.692 2 1.984 2 3.465M10 8H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6zm0 9H5v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1z\" clip-rule=\"evenodd\"/>"}, "burger-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m4 12l2.667-1l2.666 1L12 11l2.667 1l2.666-1L20 12m-1 5H5v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2zM5 9h14V8a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4zm13.5 5h-13a1.5 1.5 0 0 0 0 3h13a1.5 1.5 0 1 0 0-3\"/>"}, "burger-solid": {"body": "<g fill=\"currentColor\"><path d=\"M15 3H9a5 5 0 0 0-5 5v1a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V8a5 5 0 0 0-5-5\"/><path d=\"m4.351 12.936l2.316-.868l2.315.868a1 1 0 0 0 .702 0L12 12.068l2.316.868a1 1 0 0 0 .702 0l2.315-.868l2.316.868a1 1 0 0 0 .702-1.872l-2.667-1a1 1 0 0 0-.702 0l-2.315.868l-2.316-.868a1 1 0 0 0-.702 0l-2.316.868l-2.315-.868a1 1 0 0 0-.702 0l-2.667 1a1 1 0 1 0 .702 1.872\"/><path d=\"M21 15.5c0 .818-.393 1.544-1 2v.5a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3v-.5A2.5 2.5 0 0 1 5.5 13h13a2.5 2.5 0 0 1 2.5 2.5\"/></g>"}, "cake-candles-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 17.43V15h4.333M5 17.43V21h14v-6h-4.333M5 17.43c.387.337.923.57 1.667.57c2.666 0 2.666-3 2.666-3m5.334 0H9.333m5.334 0s0 3-2.667 3s-2.667-3-2.667-3m5.334 0s0 3 2.666 3c.744 0 1.28-.233 1.667-.57M12 8c.11 0 2-1.12 2-2.5S12.092 3 12 3s-2 1.12-2 2.5S11.895 8 12 8m0 0v3m-6 0v4h12v-4z\"/>"}, "cake-candles-solid": {"body": "<path fill=\"currentColor\" d=\"M11.664 2.056c.02-.007.16-.056.336-.056s.316.05.336.056l.002.001q.07.025.109.043q.078.037.145.073q.136.075.31.189c.229.15.52.367.812.637C14.247 3.492 15 4.37 15 5.5c0 1.127-.745 2.004-1.278 2.5c-.253.235-.507.43-.722.577V10h5a1 1 0 0 1 1 1v3h-4q-.085 0-.167.014a1 1 0 0 0-.166-.014H5v-3a1 1 0 0 1 1-1h5V8.576A6 6 0 0 1 10.28 8C9.747 7.505 9 6.628 9 5.5c0-1.13.753-2.008 1.286-2.501c.291-.27.583-.486.812-.637q.174-.115.31-.189a2 2 0 0 1 .254-.116zM4 17.838V21a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-2.824q-.09.08-.192.155c-.56.42-1.274.669-2.141.669c-.868 0-1.582-.249-2.142-.669a3.3 3.3 0 0 1-.692-.704c-.183.251-.41.494-.691.704c-.56.42-1.274.669-2.142.669s-1.582-.249-2.142-.669a3.3 3.3 0 0 1-.525-.493a3.3 3.3 0 0 1-.525.493c-.56.42-1.274.669-2.141.669s-1.582-.249-2.142-.669A3.3 3.3 0 0 1 4 17.838M5.208 16q.018.047.04.094c.118.266.275.486.477.637c.19.143.476.269.942.269s.751-.126.941-.269a1.6 1.6 0 0 0 .478-.637l.04-.094zm5.373.094L10.54 16h2.918l-.04.094a1.6 1.6 0 0 1-.477.637c-.19.143-.476.269-.942.269s-.752-.126-.942-.269a1.6 1.6 0 0 1-.478-.637m5.666 0l-.04-.094h2.918l-.039.094a1.6 1.6 0 0 1-.478.637c-.19.143-.476.269-.941.269c-.466 0-.752-.126-.942-.269a1.6 1.6 0 0 1-.478-.637\"/>"}, "calendar-edit-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m11.5 11.5l2.071 1.994M4 10h5m11 0h-1.5M12 7V4M7 7V4m10 3V4m-7 13H8v-2l5.227-5.292a1.46 1.46 0 0 1 2.065 2.065zm-5 3h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "calendar-edit-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M12.512 8.72a2.46 2.46 0 0 1 3.479 0a2.46 2.46 0 0 1 0 3.479l-.004.005l-1.094 1.08a1 1 0 0 0-.194-.272l-3-3a1 1 0 0 0-.272-.193zm-2.415 2.445L7.28 14.017a1 1 0 0 0-.289.702v2a1 1 0 0 0 1 1h2a1 1 0 0 0 .703-.288l2.851-2.816a1 1 0 0 1-.26-.189l-3-3a1 1 0 0 1-.19-.26Z\"/><path d=\"M7 3a1 1 0 0 1 1 1v1h3V4a1 1 0 1 1 2 0v1h3V4a1 1 0 1 1 2 0v1h1a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h1V4a1 1 0 0 1 1-1m10.67 8H19v8H5v-8h3.855l.53-.537a1 1 0 0 1 .87-.285c.**************.277.087s-.073-.18-.09-.276a1 1 0 0 1 .274-.873l1.09-1.104a3.46 3.46 0 0 1 4.892 0l.001.002A3.46 3.46 0 0 1 17.67 11\"/></g>"}, "calendar-month-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1m3-7h.01v.01H8zm4 0h.01v.01H12zm4 0h.01v.01H16zm-8 4h.01v.01H8zm4 0h.01v.01H12zm4 0h.01v.01H16z\"/>"}, "calendar-month-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5a1 1 0 0 0 1-1a1 1 0 1 1 2 0a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1a1 1 0 1 1 2 0a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1a1 1 0 1 1 2 0a1 1 0 0 0 1 1a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a2 2 0 0 1 2-2M3 19v-7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m6.01-6a1 1 0 1 0-2 0a1 1 0 0 0 2 0m2 0a1 1 0 1 1 2 0a1 1 0 0 1-2 0m6 0a1 1 0 1 0-2 0a1 1 0 0 0 2 0m-10 4a1 1 0 1 1 2 0a1 1 0 0 1-2 0m6 0a1 1 0 1 0-2 0a1 1 0 0 0 2 0m2 0a1 1 0 1 1 2 0a1 1 0 0 1-2 0\" clip-rule=\"evenodd\"/>"}, "calendar-plus-outline": {"body": "<path fill=\"currentColor\" d=\"M4 9.05H3v2h1zm16 2h1v-2h-1zM10 14a1 1 0 1 0 0 2zm4 2a1 1 0 1 0 0-2zm-3 1a1 1 0 1 0 2 0zm2-4a1 1 0 1 0-2 0zm-2-5.95a1 1 0 1 0 2 0zm2-3a1 1 0 1 0-2 0zm-7 3a1 1 0 0 0 2 0zm2-3a1 1 0 1 0-2 0zm8 3a1 1 0 1 0 2 0zm2-3a1 1 0 1 0-2 0zm-13 3h14v-2H5zm14 0v12h2v-12zm0 12H5v2h14zm-14 0v-12H3v12zm0 0H3a2 2 0 0 0 2 2zm14 0v2a2 2 0 0 0 2-2zm0-12h2a2 2 0 0 0-2-2zm-14-2a2 2 0 0 0-2 2h2zm-1 6h16v-2H4zM10 16h4v-2h-4zm3 1v-4h-2v4zm0-9.95v-3h-2v3zm-5 0v-3H6v3zm10 0v-3h-2v3z\"/>"}, "calendar-plus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18 5.05h1a2 2 0 0 1 2 2v2H3v-2a2 2 0 0 1 2-2h1v-1a1 1 0 1 1 2 0v1h3v-1a1 1 0 1 1 2 0v1h3v-1a1 1 0 1 1 2 0zm-15 6v8a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-8zM11 18a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1a1 1 0 1 0-2 0v1h-1a1 1 0 1 0 0 2h1z\" clip-rule=\"evenodd\"/>"}, "calendar-week-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 10h16M8 14h8m-4-7V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "calendar-week-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 5V4a1 1 0 1 1 2 0v1h3V4a1 1 0 1 1 2 0v1h3V4a1 1 0 1 1 2 0v1h1a2 2 0 0 1 2 2v2H3V7a2 2 0 0 1 2-2zM3 19v-8h18v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m5-6a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "camera-foto-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M4 18V8c0-.6.4-1 1-1h1.5l1.7-1.7c.2-.2.4-.3.7-.3h6.2c.3 0 .5.1.7.3L17.5 7H19c.6 0 1 .4 1 1v10c0 .6-.4 1-1 1H5a1 1 0 0 1-1-1Z\"/><path d=\"M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/></g>", "hidden": true}, "camera-foto-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.5 4.6A2 2 0 0 1 8.9 4h6.2c.5 0 1 .2 1.4.6L17.9 6H19a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8c0-1.1.9-2 2-2h1zM10 12a2 2 0 1 1 4 0a2 2 0 0 1-4 0m2-4a4 4 0 1 0 0 8a4 4 0 0 0 0-8\" clip-rule=\"evenodd\"/>", "hidden": true}, "camera-photo-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M4 18V8a1 1 0 0 1 1-1h1.5l1.707-1.707A1 1 0 0 1 8.914 5h6.172a1 1 0 0 1 .707.293L17.5 7H19a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1Z\"/><path d=\"M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/></g>"}, "camera-photo-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.5 4.586A2 2 0 0 1 8.914 4h6.172a2 2 0 0 1 1.414.586L17.914 6H19a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h1.086zM10 12a2 2 0 1 1 4 0a2 2 0 0 1-4 0m2-4a4 4 0 1 0 0 8a4 4 0 0 0 0-8\" clip-rule=\"evenodd\"/>"}, "candy-cane-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 8c0-.828.5-2 2-2m-2 2v2.5a1.5 1.5 0 1 1-3 0v-3m3 .5l-3-.5M12 6c1.5 0 2 1.172 2 2m-2-2V3M7 7.5C7 5.015 9.515 3 12 3m0 0c2.485 0 5 2.015 5 4.5v12a1.5 1.5 0 1 1-3 0V8m0 0h3m-3 4h3m-3 4h3\"/>"}, "candy-cane-solid": {"body": "<path fill=\"currentColor\" d=\"m11 9.163l-5-.802V10.5a2.5 2.5 0 0 0 5 0zM6.14 6.358l4.86.78v-5.05c-1.145.2-2.228.73-3.087 1.474c-.834.723-1.496 1.686-1.773 2.796M13 2.088V7h4.973c-.147-1.382-.896-2.579-1.886-3.438A6.46 6.46 0 0 0 13 2.088M18 9h-5v2h5zm0 4h-5v2h5zm0 4h-5v2.5a2.5 2.5 0 0 0 5 0z\"/>"}, "caption-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.855 14.322a2.475 2.475 0 1 1 .133-4.241m6.053 4.241a2.475 2.475 0 1 1 .133-4.241M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1\"/>"}, "caption-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm6.962 4.856a1.48 1.48 0 0 1 1.484.066A1 1 0 1 0 11.53 9.24a3.475 3.475 0 1 0-.187 5.955a1 1 0 1 0-.976-1.746a1.474 1.474 0 1 1-1.405-2.593m6.186 0a1.48 1.48 0 0 1 1.484.066a1 1 0 1 0 1.084-1.682a3.475 3.475 0 1 0-.187 5.955a1 1 0 1 0-.976-1.746a1.474 1.474 0 1 1-1.405-2.593\" clip-rule=\"evenodd\"/>"}, "captioning-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.9 14.3a2.5 2.5 0 1 1 0-4.2m6.1 4.2a2.5 2.5 0 1 1 .2-4.2M4 5h16c.6 0 1 .4 1 1v12c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V6c0-.6.4-1 1-1\"/>", "hidden": true}, "captioning-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 6c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm7 4.9a1.5 1.5 0 0 1 1.4 0a1 1 0 1 0 1.1-1.7a3.5 3.5 0 1 0-.2 6a1 1 0 1 0-1-1.8A1.5 1.5 0 1 1 9 11Zm6.1 0a1.5 1.5 0 0 1 1.5 0a1 1 0 1 0 1.1-1.7a3.5 3.5 0 1 0-.2 6a1 1 0 1 0-1-1.8a1.5 1.5 0 1 1-1.4-2.5\" clip-rule=\"evenodd\"/>", "hidden": true}, "caret-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7.119 8h9.762a1 1 0 0 1 .772 1.636l-4.881 5.927a1 1 0 0 1-1.544 0l-4.88-5.927A1 1 0 0 1 7.118 8Z\"/>"}, "caret-down-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M18.425 10.271C19.499 8.967 18.57 7 16.88 7H7.12c-1.69 0-2.618 1.967-1.544 3.271l4.881 5.927a2 2 0 0 0 3.088 0z\" clip-rule=\"evenodd\"/>"}, "caret-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 16.881V7.119a1 1 0 0 0-1.636-.772l-5.927 4.881a1 1 0 0 0 0 1.544l5.927 4.88a1 1 0 0 0 1.636-.77Z\"/>"}, "caret-left-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13.729 5.575c1.304-1.074 3.27-.146 3.27 1.544v9.762c0 1.69-1.966 2.618-3.27 1.544l-5.927-4.881a2 2 0 0 1 0-3.088z\" clip-rule=\"evenodd\"/>"}, "caret-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 16.881V7.119a1 1 0 0 1 1.636-.772l5.927 4.881a1 1 0 0 1 0 1.544l-5.927 4.88A1 1 0 0 1 8 16.882Z\"/>"}, "caret-right-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.271 5.575C8.967 4.501 7 5.43 7 7.12v9.762c0 1.69 1.967 2.618 3.271 1.544l5.927-4.881a2 2 0 0 0 0-3.088l-5.927-4.88Z\" clip-rule=\"evenodd\"/>"}, "caret-sort-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 10l4-6l4 6zm8 4l-4 6l-4-6z\"/>"}, "caret-sort-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.832 3.445a1 1 0 0 0-1.664 0l-4 6A1 1 0 0 0 8 11h8a1 1 0 0 0 .832-1.555zm-1.664 17.11a1 1 0 0 0 1.664 0l4-6A1 1 0 0 0 16 13H8a1 1 0 0 0-.832 1.555z\" clip-rule=\"evenodd\"/>"}, "caret-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.881 16H7.119a1 1 0 0 1-.772-1.636l4.881-5.927a1 1 0 0 1 1.544 0l4.88 5.927a1 1 0 0 1-.77 1.636Z\"/>"}, "caret-up-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.575 13.729C4.501 15.033 5.43 17 7.12 17h9.762c1.69 0 2.618-1.967 1.544-3.271l-4.881-5.927a2 2 0 0 0-3.088 0l-4.88 5.927Z\" clip-rule=\"evenodd\"/>"}, "carrot-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" stroke-width=\"2\" d=\"M16 8c-1.629-1.629-3.9-1.915-6.066.25s-7.112 9.78-5.483 11.409s9.241-3.318 11.407-5.484C17.95 12.081 17.629 9.63 16 8m0 0l3.26-3.151M16 8V4m0 4h4m-3.798 5.802l-2.2-2.215m-5.7-1.376l2.072 2.042m-1.259 3.184l2.112 2.163\"/>"}, "carrot-solid": {"body": "<path fill=\"currentColor\" d=\"M16 3a1 1 0 0 1 1 1v1.642l1.564-1.512a1 1 0 1 1 1.39 1.437L18.474 7H20a1 1 0 1 1 0 2h-2.105c.263.59.416 1.227.43 1.892c.019.937-.238 1.874-.778 2.756L14.7 10.871a1 1 0 0 0-1.397 1.431l2.949 2.878c-.83.762-2.106 1.728-3.513 2.647l-.012-.013l-2.885-3.063a1 1 0 0 0-1.456 1.372L11 18.897q-.284.163-.566.319c-1.217.667-2.468 1.242-3.553 1.54c-.541.15-1.093.244-1.606.222c-.512-.023-1.089-.17-1.532-.613s-.59-1.02-.612-1.532c-.023-.513.072-1.065.22-1.606c.299-1.085.874-2.336 1.542-3.553a37 37 0 0 1 2.073-3.318l2.714 2.617a1 1 0 1 0 1.388-1.44L8.172 8.741a15 15 0 0 1 1.055-1.198c1.223-1.223 2.576-1.846 3.945-1.84A4.5 4.5 0 0 1 15 6.098V4a1 1 0 0 1 1-1\"/>"}, "cart-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 4h1.5L9 16m0 0h8m-8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m-8.5-3h9.25L19 7H7.312\"/>"}, "cart-plus-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4h1.5L8 16m0 0h8m-8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m.75-3H7.5M11 7H6.312M17 4v6m-3-3h6\"/>"}, "cart-plus-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12.268 6A2 2 0 0 0 14 9h1v1a2 2 0 0 0 3.04 1.708l-.311 1.496a1 1 0 0 1-.979.796H8.605l.208 1H16a3 3 0 1 1-2.83 2h-2.34a3 3 0 1 1-4.009-1.76L4.686 5H4a1 1 0 0 1 0-2h1.5a1 1 0 0 1 .979.796L6.939 6z\"/><path d=\"M18 4a1 1 0 1 0-2 0v2h-2a1 1 0 1 0 0 2h2v2a1 1 0 1 0 2 0V8h2a1 1 0 1 0 0-2h-2z\"/></g>"}, "cart-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 4h1.5L9 16m0 0h8m-8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m8 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m-8.5-3h9.25L19 7h-1M8 7h-.688M13 5v4m-2-2h4\"/>"}, "cart-plus-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M5 3a1 1 0 0 0 0 2h.687L7.82 15.24A3 3 0 1 0 11.83 17h2.34A3 3 0 1 0 17 15H9.813l-.208-1h8.145a1 1 0 0 0 .979-.796l1.25-6A1 1 0 0 0 19 6h-2.268A2 2 0 0 1 15 9a2 2 0 1 1-4 0a2 2 0 0 1-1.732-3h-1.33L7.48 3.796A1 1 0 0 0 6.5 3z\"/><path d=\"M14 5a1 1 0 1 0-2 0v1h-1a1 1 0 1 0 0 2h1v1a1 1 0 1 0 2 0V8h1a1 1 0 1 0 0-2h-1z\"/></g>"}, "cart-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a1 1 0 0 1 1-1h1.5a1 1 0 0 1 .979.796L7.939 6H19a1 1 0 0 1 .979 1.204l-1.25 6a1 1 0 0 1-.979.796H9.605l.208 1H17a3 3 0 1 1-2.83 2h-2.34a3 3 0 1 1-4.009-1.76L5.686 5H5a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "cash-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0Z\"/>"}, "cash-register-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 18h14M5 18v3h14v-3M5 18l1-9h12l1 9M16 6v3m-4-3v3m-2-6h8v3h-8zm-1 9h.01v.01H9zm3 0h.01v.01H12zm3 0h.01v.01H15zm-6 3h.01v.01H9zm3 0h.01v.01H12zm3 0h.01v.01H15z\"/>"}, "cash-register-solid": {"body": "<g fill=\"currentColor\"><path d=\"M4 19v2a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-2z\"/><path fill-rule=\"evenodd\" d=\"M9 3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-2v1h2a1 1 0 0 1 .994.89l.901 8.11H4.105l.901-8.11A1 1 0 0 1 6 8h6V7h-2a1 1 0 0 1-1-1zm1.01 8H8v2.01h2.01zm.99 0h2.01v2.01H11zm5.01 0H14v2.01h2.01zM8 14h2.01v2.01H8zm5.01 0H11v2.01h2.01zm.99 0h2.01v2.01H14zM11 4h6v1h-6z\" clip-rule=\"evenodd\"/></g>"}, "cash-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M7 6a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-2v-4a3 3 0 0 0-3-3H7z\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M2 11a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm7.5 1a2.5 2.5 0 1 0 0 5a2.5 2.5 0 0 0 0-5\" clip-rule=\"evenodd\"/><path d=\"M10.5 14.5a1 1 0 1 1-2 0a1 1 0 0 1 2 0\"/></g>"}, "cell-attributes-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h8v-8m-9 4v-4m0 4h9m-9-4V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v4M3 11h11m6.25 5A2.25 2.25 0 0 1 18 18.25M20.25 16A2.25 2.25 0 0 0 18 13.75M20.25 16H21m-3 2.25A2.25 2.25 0 0 1 15.75 16M18 18.25V19m-2.25-3A2.25 2.25 0 0 1 18 13.75M15.75 16H15m3-2.25V13m-1.591 1.409l-.53-.53m4.242 4.242l-.53-.53m-3.182 0l-.53.53m4.242-4.242l-.53.53\"/>"}, "chalkboard-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 14H4m6.5 3L8 20m5.5-3l2.5 3M4.889 17H19.11c.491 0 .889-.416.889-.929V4.93c0-.513-.398-.929-.889-.929H4.89C4.398 4 4 4.416 4 4.929V16.07c0 .513.398.929.889.929M13 14v-3h4v3z\"/>"}, "chalkboard-solid": {"body": "<g fill=\"currentColor\"><path d=\"M3 4.929C3 3.905 3.805 3 4.889 3H19.11C20.195 3 21 3.905 21 4.929V13h-3v-2a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v2H3zM3 15v1.071C3 17.095 3.805 18 4.889 18h3.476l-1.133 1.36a1 1 0 0 0 1.536 1.28l2.2-2.64h2.064l2.2 2.64a1 1 0 0 0 1.536-1.28L15.635 18h3.476C20.195 18 21 17.095 21 16.071V15z\"/><path d=\"M16 12v1h-2v-1z\"/></g>"}, "chalkboard-user-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.714 15h4.268c.404 0 .732-.384.732-.857V3.857c0-.473-.328-.857-.732-.857H6.714a1 1 0 0 0-1 1v4m11 7v-3h3v3zm-3 6h-7a1 1 0 0 1-1-1a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3a1 1 0 0 1-1 1m-1-9.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "chalkboard-user-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6 2a2 2 0 0 0-2 2v4a1 1 0 0 0 2 0V4h12v7h-2a1 1 0 0 0-1 1v2h-1a1 1 0 1 0 0 2h5a1 1 0 0 0 1-1V3.857C20 2.985 19.367 2 18.268 2z\"/><path d=\"M6 11.5a3.5 3.5 0 1 1 7 0a3.5 3.5 0 0 1-7 0M4 20a4 4 0 0 1 4-4h3a4 4 0 0 1 4 4a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2\"/></g>"}, "champagne-glasses-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 15a3 3 0 0 0 3-3V6H4v6a3 3 0 0 0 3 3m0 0v5m-2 0h4M4 9h6m6.608 4.486a3 3 0 0 0 1.94-3.773L16.712 4L11 5.835l1.835 5.712a3 3 0 0 0 3.773 1.94m0 0l1.53 4.76m-1.905.612l3.808-1.223m-8.124-8.944l5.713-1.835\"/>"}, "champagne-glasses-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.665 3.694a1 1 0 0 0-1.258-.646l-5.713 1.835a1 1 0 0 0-.335.183A1 1 0 0 0 10 5H4a1 1 0 0 0-1 1v6a4 4 0 0 0 3 3.874V19H5a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2H8v-3.126c1.725-.444 3-2.01 3-3.874V9.105l.883 2.748a4 4 0 0 0 4.04 2.771l.956 2.976l-.952.306a1 1 0 0 0 .612 1.904l3.808-1.223a1 1 0 1 0-.611-1.904l-.953.306l-.955-2.976a4 4 0 0 0 1.671-4.606zM9 7v1H5V7zm7.066-1.742l.306.952l-3.808 1.223l-.306-.952z\" clip-rule=\"evenodd\"/>"}, "chart-line-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4.5V19a1 1 0 0 0 1 1h15M7 10l4 4l4-4l5 5m0 0h-3.207M20 15v-3.207\"/>"}, "chart-line-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4.5V19a1 1 0 0 0 1 1h15M7 14l4-4l4 4l5-5m0 0h-3.207M20 9v3.207\"/>"}, "chart-line-up-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M4 4.5V19c0 .6.4 1 1 1h15\"/><path d=\"m7 10l4 4l4-4l5 5m0 0h-3.2m3.2 0v-3.2\"/></g>", "hidden": true}, "chart-mixed-dollar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.6 16.733c.234.269.548.456.895.534a1.4 1.4 0 0 0 1.75-.762c.172-.615-.446-1.287-1.242-1.481s-1.41-.861-1.241-1.481a1.4 1.4 0 0 1 1.75-.762c.343.077.654.26.888.524m-1.358 4.017v.617m0-5.939v.725M4 15v4m3-6v6M6 8.5L10.5 5L14 7.5L18 4m0 0h-3.5M18 4v3m2 8a5 5 0 1 1-10 0a5 5 0 0 1 10 0\"/>"}, "chart-mixed-dollar-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 15a6 6 0 1 1 12 0a6 6 0 0 1-12 0m3.845-1.855a2.4 2.4 0 0 1 1.2-1.226a1 1 0 0 1 1.992-.026c.426.15.809.408 1.111.749a1 1 0 1 1-1.496 1.327a.7.7 0 0 0-.36-.213a1 1 0 0 1-.113-.032a.4.4 0 0 0-.394.074a.93.93 0 0 0 .455.254a2.9 2.9 0 0 1 1.504.9c.373.433.669 1.092.464 1.823a1 1 0 0 1-.046.129c-.226.519-.627.94-1.132 1.192a1 1 0 0 1-1.956.093a2.7 2.7 0 0 1-1.227-.798a1 1 0 1 1 1.506-1.315a.7.7 0 0 0 .363.216q.057.014.111.032a.4.4 0 0 0 .395-.074a.93.93 0 0 0-.455-.254a2.9 2.9 0 0 1-1.503-.9c-.375-.433-.666-1.089-.466-1.817a1 1 0 0 1 .047-.134m1.884.573l.003.008zm.55 2.613s-.002-.002-.003-.007zM4 14a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0v-4a1 1 0 0 1 1-1m3-2a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1m6.5-8a1 1 0 0 1 1-1H18a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-.796l-2.341 2.049a1 1 0 0 1-1.24.06l-2.894-2.066L6.614 9.29a1 1 0 1 1-1.228-1.578l4.5-3.5a1 1 0 0 1 1.195-.025l2.856 2.04L15.34 5h-.84a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "chart-mixed-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v4m6-6v6m6-4v4m6-6v6M3 11l6-5l6 5l5.5-5.5\"/>"}, "chart-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5l3 3L17.273 7L20 9.667\"/>"}, "chart-pie-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M10 6.025A7.5 7.5 0 1 0 17.975 14H10z\"/><path d=\"M13.5 3c-.169 0-.334.014-.5.025V11h7.975c.011-.166.025-.331.025-.5A7.5 7.5 0 0 0 13.5 3\"/></g>"}, "chart-pie-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13.5 2c-.178 0-.356.013-.492.022l-.074.005a1 1 0 0 0-.934.998V11a1 1 0 0 0 1 1h7.975a1 1 0 0 0 .998-.934l.005-.074A7 7 0 0 0 22 10.5A8.5 8.5 0 0 0 13.5 2\"/><path d=\"M11 6.025a1 1 0 0 0-1.065-.998a8.5 8.5 0 1 0 9.038 9.039A1 1 0 0 0 17.975 13H11z\"/></g>"}, "chart-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M4 4v15c0 .6.4 1 1 1h15\"/><path stroke-linejoin=\"round\" d=\"m8 16l2.5-5.5l3 3L17.3 7L20 9.7\"/></g>", "hidden": true}, "check-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.5 11.5L11 14l4-4m6 2a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "check-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m13.707-1.293a1 1 0 0 0-1.414-1.414L11 12.586l-1.793-1.793a1 1 0 0 0-1.414 1.414l2.5 2.5a1 1 0 0 0 1.414 0z\" clip-rule=\"evenodd\"/>"}, "check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 11.917L9.724 16.5L19 7.5\"/>"}, "check-plus-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 21a9 9 0 1 1 0-18c1.052 0 2.062.18 3 .512M7 9.577l3.923 3.923l8.5-8.5M17 14v6m-3-3h6\"/>"}, "check-plus-circle-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M18 14a1 1 0 1 0-2 0v2h-2a1 1 0 1 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2z\"/><path d=\"M15.026 21.534A10 10 0 0 1 12 22C6.477 22 2 17.523 2 12S6.477 2 12 2c2.51 0 4.802.924 6.558 2.45l-7.635 7.636L7.707 8.87a1 1 0 0 0-1.414 1.414l3.923 3.923a1 1 0 0 0 1.414 0l8.3-8.3A9.96 9.96 0 0 1 22 12a10 10 0 0 1-.466 3.026A2.5 2.5 0 0 0 20 14.5h-.5V14a2.5 2.5 0 0 0-5 0v.5H14a2.5 2.5 0 0 0 0 5h.5v.5c0 .578.196 1.11.526 1.534\"/></g>"}, "check-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m5 12l4.7 4.5l9.3-9\"/>", "hidden": true}, "cheese-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m4 12l6.427-4.66a2.006 2.006 0 0 0 2.789.473c.896-.638 1.349-1.692.468-2.801C13.637 4.953 14.91 4 15 4s5 1 5 8M4 12h16M4 12v8h16v-8M8 15h.01M12 17h.01M16 15h.01\"/>"}, "cheese-solid": {"body": "<g fill=\"currentColor\"><path d=\"M15 3c-.179 0-.315.049-.348.06l-.002.002a1 1 0 0 0-.121.051a2 2 0 0 0-.13.072a5 5 0 0 0-.216.141a14 14 0 0 0-.975.743a3 3 0 0 0-.204.188a1.3 1.3 0 0 0-.146.178a1.01 1.01 0 0 0 .042 1.199c.28.351.277.583.244.723c-.043.18-.193.417-.508.64a1.006 1.006 0 0 1-1.394-.235a1 1 0 0 0-1.402-.23L3.676 11h17.291c-.213-3.174-1.435-5.16-2.761-6.365a7.2 7.2 0 0 0-1.992-1.288c-.278-.12-.517-.2-.698-.252a4 4 0 0 0-.32-.077A1.4 1.4 0 0 0 15 3\"/><path fill-rule=\"evenodd\" d=\"M21 13H3v7a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1zM7 15a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m8 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H16a1 1 0 0 1-1-1m-4 2a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/></g>"}, "chevron-double-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 7l4 4l4-4m-8 6l4 4l4-4\"/>"}, "chevron-double-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m17 16l-4-4l4-4m-6 8l-4-4l4-4\"/>"}, "chevron-double-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7 16l4-4l-4-4m6 8l4-4l-4-4\"/>"}, "chevron-double-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m16 17l-4-4l-4 4m8-6l-4-4l-4 4\"/>"}, "chevron-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 10l4 4l4-4\"/>"}, "chevron-left-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m14 8l-4 4l4 4\"/>"}, "chevron-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m10 16l4-4l-4-4\"/>"}, "chevron-sort-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 15l4 4l4-4m0-6l-4-4l-4 4\"/>"}, "chevron-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m16 14l-4-4l-4 4\"/>"}, "circle-minus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7.757 12h8.486M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "circle-minus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m5.757-1a1 1 0 1 0 0 2h8.486a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "circle-pause-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 9v6m4-6v6m7-3a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "circle-pause-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m9-3a1 1 0 1 0-2 0v6a1 1 0 1 0 2 0zm4 0a1 1 0 1 0-2 0v6a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "circle-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "circle-plus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m11-4.243a1 1 0 1 0-2 0V11H7.757a1 1 0 1 0 0 2H11v3.243a1 1 0 1 0 2 0V13h3.243a1 1 0 1 0 0-2H13z\" clip-rule=\"evenodd\"/>"}, "clapperboard-play-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 4H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1m0 0l-4 4m5 0H4m1 0l4-4m1 4l4-4m-4 7v6l4-3z\"/>"}, "clapperboard-play-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M19.003 3A2 2 0 0 1 21 5v2h-2V5.414L17.414 7h-2.828l2-2h-2.172l-2 2H9.586l2-2H9.414l-2 2H3V5a2 2 0 0 1 2-2zM3 9v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9zm2-2.414L6.586 5H5zm4.553 4.52a1 1 0 0 1 1.047.094l4 3a1 1 0 0 1 0 1.6l-4 3A1 1 0 0 1 9 18v-6a1 1 0 0 1 .553-.894\" clip-rule=\"evenodd\"/>"}, "clipboard-check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 4h3a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3m0 3h6m-6 7l2 2l4-4m-5-9v4h4V3z\"/>"}, "clipboard-check-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2a1 1 0 0 0-1 1H6a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-2a1 1 0 0 0-1-1zm1 2h4v2h1a1 1 0 1 1 0 2H9a1 1 0 0 1 0-2h1zm5.707 8.707a1 1 0 0 0-1.414-1.414L11 14.586l-1.293-1.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0z\" clip-rule=\"evenodd\"/>"}, "clipboard-clean-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 4h3a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3m0 3h6m-5-4v4h4V3z\"/>"}, "clipboard-clean-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1h2a2 2 0 0 1 2 2v15a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm6 1h-4v2H9a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2h-1z\" clip-rule=\"evenodd\"/>"}, "clipboard-list-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 4h3a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3m0 3h6m-3 5h3m-6 0h.01M12 16h3m-6 0h.01M10 3v4h4V3z\"/>"}, "clipboard-list-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1h2a2 2 0 0 1 2 2v15a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm6 1h-4v2H9a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2h-1zm-3 8a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1m-2-1a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm2 5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1m-2-1a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "clipboard-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 4h3a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3m0 3h6m-6 5h6m-6 4h6M10 3v4h4V3z\"/>"}, "clipboard-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1h2a2 2 0 0 1 2 2v15a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm6 1h-4v2H9a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2h-1zm-6 8a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m1 3a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "clock-arrow-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3M3.223 14A9 9 0 1 0 12 3a9 9 0 0 0-8.294 5.5M7 9H3V5\"/>"}, "clock-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "clock-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586z\" clip-rule=\"evenodd\"/>"}, "close-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m15 9l-6 6m0-6l6 6m6-3a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "close-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m7.707-3.707a1 1 0 0 0-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414L12 13.414l2.293 2.293a1 1 0 0 0 1.414-1.414L13.414 12l2.293-2.293a1 1 0 0 0-1.414-1.414L12 10.586z\" clip-rule=\"evenodd\"/>"}, "close-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L17.94 6M18 18L6.06 6\"/>"}, "close-sidebar-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m9 10l-2 2l2 2m3-9v14M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1\"/>"}, "close-sidebar-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13 21h6a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-6z\"/><path fill-rule=\"evenodd\" d=\"M11 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6zm-2.293 7.707a1 1 0 0 0-1.414-1.414l-2 2a1 1 0 0 0 0 1.414l2 2a1 1 0 0 0 1.414-1.414L7.414 12z\" clip-rule=\"evenodd\"/></g>"}, "close-sidebar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 10l-2 2l2 2m3-9v14m-7 0h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "close-sidebar-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6zM7.793 9.293a1 1 0 0 1 0 1.414L6.5 12l1.293 1.293a1 1 0 1 1-1.414 1.414l-2-2a1 1 0 0 1 0-1.414l2-2a1 1 0 0 1 1.414 0\" clip-rule=\"evenodd\"/><path d=\"M12 20h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-8z\"/></g>"}, "close-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6m0 12L6 6\"/>", "hidden": true}, "cloud-arrow-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 17h3a3 3 0 0 0 0-6h-.025a6 6 0 0 0 .025-.5A5.5 5.5 0 0 0 7.207 9.021C7.137 9.017 7.071 9 7 9a4 4 0 1 0 0 8h2.167M12 19v-9m0 0l-2 2m2-2l2 2\"/>"}, "cloud-arrow-up-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13.383 4.076a6.5 6.5 0 0 0-6.887 3.95A5 5 0 0 0 7 18h3v-4a2 2 0 0 1-1.414-3.414l2-2a2 2 0 0 1 2.828 0l2 2A2 2 0 0 1 14 14v4h4a4 4 0 0 0 .988-7.876a6.5 6.5 0 0 0-5.605-6.048\"/><path d=\"M12.707 9.293a1 1 0 0 0-1.414 0l-2 2a1 1 0 1 0 1.414 1.414l.293-.293V19a1 1 0 1 0 2 0v-6.586l.293.293a1 1 0 0 0 1.414-1.414z\"/></g>"}, "cloud-meatball-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.333 14.653c.708 0 1.386-.21 1.886-.71s.781-1.25.781-1.957a2.667 2.667 0 0 0-2.667-2.667h-.022q.02-.221.022-.444A4.889 4.889 0 0 0 7.74 7.56c-.063-.003-.121-.018-.184-.018c-.943 0-1.91.366-2.577 1.032c-.667.667-.979 1.58-.979 2.523a3.555 3.555 0 0 0 2.667 3.443M12 14v3m0 0v3m0-3l-2.121-2.121M12 17l2.121 2.121M12 17H9m3 0h3m-3 0l-2.121 2.121M12 17l2.121-2.121M6 18h.01M18 18h.01\"/>"}, "cloud-meatball-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6 17a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm12 0a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\"/><path fill-rule=\"evenodd\" d=\"M12 13a1 1 0 0 1 1 1v.586l.414-.414a1 1 0 0 1 1.414 1.414l-.414.414H15a1 1 0 1 1 0 2h-.586l.414.414a1 1 0 0 1-1.414 1.415L13 19.414V20a1 1 0 0 1-2 0v-.586l-.414.414a1 1 0 1 1-1.414-1.414L9.586 18H9a1 1 0 0 1 0-2h.586l-.414-.414a1 1 0 1 1 1.414-1.414l.414.414V14a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/><path d=\"M9.219 3.962a5.89 5.89 0 0 1 9.098 4.492A3.67 3.67 0 0 1 21 11.986c0 .945-.368 1.957-1.074 2.664c-.642.641-1.456.928-2.251.99a3 3 0 0 0-.554-.761a3 3 0 0 0-.878-2.122a3 3 0 0 0-2.122-.878A3 3 0 0 0 12 11a3 3 0 0 0-2.121.879a3 3 0 0 0-2.122.879a3 3 0 0 0-.878 2.12a3 3 0 0 0-.48.626A4.556 4.556 0 0 1 3 11.097c0-1.158.385-2.343 1.272-3.23c.742-.742 1.746-1.182 2.759-1.296a5.9 5.9 0 0 1 2.188-2.609\"/></g>"}, "code-branch-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 8v8m0-8a2 2 0 1 0 0-4a2 2 0 0 0 0 4m0 8a2 2 0 1 0 0 4a2 2 0 0 0 0-4m8-8a2 2 0 1 0 0-4a2 2 0 0 0 0 4m0 0a4 4 0 0 1-4 4h-1a3 3 0 0 0-3 3\"/>"}, "code-branch-solid": {"body": "<path fill=\"currentColor\" d=\"M8 3a3 3 0 0 0-1 5.83v6.34a3.001 3.001 0 1 0 2 0V15a2 2 0 0 1 2-2h1a5 5 0 0 0 4.927-4.146A3.001 3.001 0 0 0 16 3a3 3 0 0 0-1.105 5.79A3 3 0 0 1 12 11h-1c-.729 0-1.412.195-2 .535V8.83A3.001 3.001 0 0 0 8 3\"/>"}, "code-fork-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 12v4m0 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4M8 8a2 2 0 1 0 0-4a2 2 0 0 0 0 4m0 0v2a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V8m0 0a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "code-fork-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 6a3 3 0 1 1 4 2.83V10a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V8.83a3.001 3.001 0 1 1 2 0V10a3 3 0 0 1-3 3h-1v2.17a3.001 3.001 0 1 1-2 0V13h-1a3 3 0 0 1-3-3V8.83A3 3 0 0 1 5 6\" clip-rule=\"evenodd\"/>"}, "code-merge-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 8v8m0-8a2 2 0 1 0 0-4a2 2 0 0 0 0 4m0 8a2 2 0 1 0 0 4a2 2 0 0 0 0-4m6-2a2 2 0 1 1 4 0a2 2 0 0 1-4 0m0 0h-1a5 5 0 0 1-5-5v-.5\"/>"}, "code-merge-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 6a3 3 0 1 1 4 2.83V9a4 4 0 0 0 4 4h.17a3.001 3.001 0 1 1 0 2H13a5.98 5.98 0 0 1-4-1.528v1.699a3.001 3.001 0 1 1-2 0V8.829A3 3 0 0 1 5 6\" clip-rule=\"evenodd\"/>"}, "code-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 8l-4 4l4 4m8 0l4-4l-4-4m-2-3l-4 14\"/>"}, "code-pull-request-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 8v8m0-8a2 2 0 1 0 0-4a2 2 0 0 0 0 4m0 8a2 2 0 1 0 0 4a2 2 0 0 0 0-4m12 0a2 2 0 1 0 0 4a2 2 0 0 0 0-4m0 0V9a3 3 0 0 0-3-3h-3m1.5-2l-2 2l2 2\"/>"}, "code-pull-request-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 6a3 3 0 1 1 4 2.83v6.34a3.001 3.001 0 1 1-2 0V8.83A3 3 0 0 1 3 6m11.207-2.707a1 1 0 0 1 0 1.414L13.914 5H15a4 4 0 0 1 4 4v6.17a3.001 3.001 0 1 1-2 0V9a2 2 0 0 0-2-2h-1.086l.293.293a1 1 0 0 1-1.414 1.414l-2-2a1 1 0 0 1 0-1.414l2-2a1 1 0 0 1 1.414 0\" clip-rule=\"evenodd\"/>"}, "code-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path stroke-linejoin=\"round\" d=\"m8 8l-4 4l4 4m8 0l4-4l-4-4\"/><path d=\"m14 5l-4 14\"/></g>", "hidden": true}, "cog-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M21 13v-2a1 1 0 0 0-1-1h-.757l-.707-1.707l.535-.536a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0l-.536.535L14 4.757V4a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v.757l-1.707.707l-.536-.535a1 1 0 0 0-1.414 0L4.929 6.343a1 1 0 0 0 0 1.414l.536.536L4.757 10H4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h.757l.707 1.707l-.535.536a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l.536-.535l1.707.707V20a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-.757l1.707-.708l.536.536a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-.535-.536l.707-1.707H20a1 1 0 0 0 1-1\"/><path d=\"M12 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/></g>"}, "cog-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9.586 2.586A2 2 0 0 1 11 2h2a2 2 0 0 1 2 2v.089l.473.196l.063-.063a2 2 0 0 1 2.828 0l1.414 1.414a2 2 0 0 1 0 2.827l-.063.064l.196.473H20a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-.089l-.196.473l.063.063a2 2 0 0 1 0 2.828l-1.414 1.414a2 2 0 0 1-2.828 0l-.063-.063l-.473.196V20a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.089l-.473-.196l-.063.063a2 2 0 0 1-2.828 0l-1.414-1.414a2 2 0 0 1 0-2.827l.063-.064L4.089 15H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09l.195-.473l-.063-.063a2 2 0 0 1 0-2.828l1.414-1.414a2 2 0 0 1 2.827 0l.064.063L9 4.089V4a2 2 0 0 1 .586-1.414M8 12a4 4 0 1 1 8 0a4 4 0 0 1-8 0\" clip-rule=\"evenodd\"/>"}, "column-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1\"/>"}, "column-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 4H9v16h6zm2 16h3a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-3zM4 4h3v16H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2\" clip-rule=\"evenodd\"/>"}, "command-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 8v8m0-8h8M8 8H6a2 2 0 1 1 2-2zm0 8h8m-8 0H6a2 2 0 1 0 2 2zm8 0V8m0 8h2a2 2 0 1 1-2 2zm0-8h2a2 2 0 1 0-2-2z\"/>"}, "command-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 16V8h8v8zm0-8H6a2 2 0 1 1 2-2zm8 0h2a2 2 0 1 0-2-2zm-8 8H6a2 2 0 1 0 2 2zm8 0h2a2 2 0 1 1-2 2z\"/>", "hidden": true}, "compress-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 8h4V4m12 4h-4V4M4 16h4v4m12-4h-4v4\"/>"}, "computer-speaker-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 16H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v1M9 12H4m8 8V9h8v11zm0 0H9m8-4a1 1 0 1 0-2 0a1 1 0 0 0 2 0\"/>"}, "computer-speaker-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M12 8a1 1 0 0 0-1 1v10H9a1 1 0 1 0 0 2h11a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1zm4 10a2 2 0 1 1 0-4a2 2 0 0 1 0 4\"/><path d=\"M5 3a2 2 0 0 0-2 2v6h6V9a3 3 0 0 1 3-3h8c.35 0 .687.06 1 .17V5a2 2 0 0 0-2-2zm4 10H3v2a2 2 0 0 0 2 2h4z\"/></g>"}, "cookie-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.657 9.415h.01M7.27 13h.01m7.677 1.916h.01M11 17h.01m3.178-10.907a2.254 2.254 0 0 1-3.173-1.755c-.058-.44-.02-.99.183-1.395a9 9 0 1 0 9.87 9.73c-.21.008-.672.022-.832.015a2.196 2.196 0 0 1-2.405-1.902c-.056-.43.015-.872.212-1.267a2.633 2.633 0 1 1-3.852-3.41\"/>"}, "cookie-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.021 2.375a1 1 0 0 1 .072 1.015c-.083.167-.129.496-.087.818a1.254 1.254 0 0 0 1.765.976a1 1 0 0 1 1.401.732a1 1 0 0 1-.39 1a1.633 1.633 0 1 0 2.388 2.116a1 1 0 0 1 1.768.933a1.2 1.2 0 0 0 .226 1.383a1.2 1.2 0 0 0 .97.346q.072-.008.143-.005c.111.005.525-.007.754-.016a1 1 0 0 1 1.034 1.085a9.96 9.96 0 0 1-2.892 6.211c-3.905 3.905-10.237 3.905-14.142 0c-3.906-3.905-3.906-10.237 0-14.142a9.96 9.96 0 0 1 6.067-2.879a1 1 0 0 1 .923.427m-3.364 6.04a1 1 0 1 0 0 2h.01a1 1 0 0 0 0-2zM7.271 12a1 1 0 0 0 0 2h.01a1 1 0 1 0 0-2zm7.688 1.916a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM11 16a1 1 0 0 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "credit-card-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 10h18M6 14h2m3 0h5M3 7v10a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1\"/>"}, "credit-card-plus-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 14h2m3 0h4m2 2h2m0 0h2m-2 0v2m0-2v-2m-5 4H4a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v4M3 10h18\"/>"}, "credit-card-plus-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a1 1 0 1 1-2 0v-1H4v7h10a1 1 0 1 1 0 2H4a2 2 0 0 1-2-2z\"/><path d=\"M5 14a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m5 0a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1m9-1a1 1 0 0 1 1 1v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0v-1h-1a1 1 0 1 1 0-2h1v-1a1 1 0 0 1 1-1\"/></g>"}, "credit-card-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"square\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.5 15v1.5m0 0V18m0-1.5H15m1.5 0H18M3 9V6a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v3M3 9v6a1 1 0 0 0 1 1h5M3 9h16m0 0v1M6 12h3m12 4.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0Z\"/>"}, "credit-card-plus-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M11 16.5a5.5 5.5 0 1 1 11 0a5.5 5.5 0 0 1-11 0m4.5 2.5v-1.5H14v-2h1.5V14h2v1.5H19v2h-1.5V19z\" clip-rule=\"evenodd\"/><path d=\"M3.987 4A2 2 0 0 0 2 6v9a2 2 0 0 0 2 2h5v-2H4v-5h16V6a2 2 0 0 0-2-2z\"/><path fill-rule=\"evenodd\" d=\"M5 12a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/></g>"}, "credit-card-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M4 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2zm0 6h16v6H4z\"/><path d=\"M5 14a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m5 0a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1\"/></g>"}, "css-solid": {"body": "<path fill=\"currentColor\" d=\"m3 2l1.578 17.834L12 22l7.468-2.165L21 2zm13.3 14.722l-4.293 1.204H12l-4.297-1.204l-.297-3.167h2.108l.15 1.526l2.335.639l2.34-.64l.245-3.05h-7.27l-.187-2.006h7.64l.174-2.006H6.924l-.176-2.006h10.506z\"/>"}, "cube-solid": {"body": "<path fill=\"currentColor\" d=\"M9.982 4.506a4.28 4.28 0 0 1 4.036 0l3.964 2.142c.363.196.686.44.961.722l-6.929 3.602l-6.807-3.747a4 4 0 0 1 .811-.577zM4.167 8.844A3.6 3.6 0 0 0 4 9.918v4.283c0 1.35.77 2.597 2.018 3.271l3.964 2.142c.32.173.66.302 1.009.386v-7.4zM13.009 20a4.2 4.2 0 0 0 1.01-.386l3.963-2.142C19.23 16.798 20 15.551 20 14.202V9.917q-.001-.451-.11-.88l-6.881 3.578z\"/>"}, "cubes-stacked-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 10.857l4 2.286m-4-2.286l-4 2.286m4-2.286V6.286m4 6.857v4.571m0-4.571l-4 2.286m4-2.286l4-2.286m-4 2.286V8.57m0 4.572l4 2.286m-4 2.285L8 20m4-2.286L16 20m-8 0l-4-2.286v-4.571M8 20v-4.571m-4-2.286l4 2.285m8-9.142L12 4L8 6.286m8 0v4.571m0-4.571L12 8.57m4 2.286l4 2.286M8 6.286l4 2.285m8 4.572v4.571L16 20m4-6.857l-4 2.286M16 20v-4.571\"/>"}, "cubes-stacked-solid": {"body": "<path fill=\"currentColor\" d=\"M11.504 3.132a1 1 0 0 1 .992 0l3.556 2.031L12 7.426L7.948 5.163zM7 6.925v3.352l-3.158 1.805l4.141 2.204L11 12.564V9.158zm-4 6.974v3.815a1 1 0 0 0 .504.869L7 20.58v-4.55zm6 6.681l3-1.714l3 1.714v-4.57l-3-1.714l-3 1.714zm8 0l3.496-1.997a1 1 0 0 0 .504-.869V13.9l-4 2.13zm3.158-8.498L17 10.277V6.925l-4 2.233v3.405l3.017 1.723z\"/>"}, "database-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 6c0 1.657-3.134 3-7 3S5 7.657 5 6m14 0c0-1.657-3.134-3-7-3S5 4.343 5 6m14 0v6M5 6v6m0 0c0 1.657 3.134 3 7 3s7-1.343 7-3M5 12v6c0 1.657 3.134 3 7 3s7-1.343 7-3v-6\"/>"}, "database-solid": {"body": "<path fill=\"currentColor\" d=\"M12 7.205c4.418 0 8-1.165 8-2.602C20 3.165 16.418 2 12 2S4 3.165 4 4.603c0 1.437 3.582 2.602 8 2.602M12 22c4.963 0 8-1.686 8-2.603v-4.404c-.052.032-.112.06-.165.09a8 8 0 0 1-.745.387q-.29.132-.6.253q-.093.037-.189.073a19 19 0 0 1-6.3.998c-2.135.027-4.26-.31-6.3-.998l-.189-.073a10 10 0 0 1-.852-.373a8 8 0 0 1-.493-.267c-.053-.03-.113-.058-.165-.09v4.404C4 20.315 7.037 22 12 22m7.09-13.928a10 10 0 0 1-.6.253q-.093.038-.189.074a19 19 0 0 1-6.3.998c-2.135.027-4.26-.31-6.3-.998l-.189-.074a10 10 0 0 1-.852-.372a8 8 0 0 1-.493-.268c-.055-.03-.115-.058-.167-.09V12c0 .917 3.037 2.603 8 2.603s8-1.686 8-2.603V7.596c-.052.031-.112.059-.165.09a8 8 0 0 1-.745.386\"/>"}, "delete-column-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5v14m-6-8h6m-6 4h6m4.506-1.494L15.012 12m0 0l1.506-1.506M15.012 12l1.506 1.506M15.012 12l-1.506-1.506M20 19H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1\"/>"}, "delete-row-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-3M3 15V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v9M3 15h18M8 15v4m4-4v4m4-4v4m-5.506-7.494L12 10m0 0l1.506-1.506M12 10l1.506 1.506M12 10l-1.506-1.506\"/>"}, "delete-table-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h10.5M3 15v-4m0 4h11M3 11V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v5M3 11h18m0 0v1M8 11v8m4-8v8m4-8v2m1.896 5.953l1.504-1.505m0 0l1.505-1.505M19.4 17.448l1.46 1.46m-1.46-1.46l-1.46-1.46\"/>"}, "desktop-pc-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v5m-3 0h6M4 11h16M5 15h14a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1\"/>"}, "desktop-pc-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v5h18V5a2 2 0 0 0-2-2zM3 14v-2h18v2a2 2 0 0 1-2 2h-6v3h2a1 1 0 1 1 0 2H9a1 1 0 1 1 0-2h2v-3H5a2 2 0 0 1-2-2\" clip-rule=\"evenodd\"/>"}, "discord-solid": {"body": "<path fill=\"currentColor\" d=\"M18.942 5.556a16.3 16.3 0 0 0-4.126-1.3a12 12 0 0 0-.529 1.1a15.2 15.2 0 0 0-4.573 0a12 12 0 0 0-.535-1.1a16.3 16.3 0 0 0-4.129 1.3a17.4 17.4 0 0 0-2.868 11.662a15.8 15.8 0 0 0 4.963 2.521q.616-.847 1.084-1.785a10.6 10.6 0 0 1-1.706-.83q.215-.16.418-.331a11.66 11.66 0 0 0 10.118 0q.206.172.418.331q-.817.492-1.71.832a12.6 12.6 0 0 0 1.084 1.785a16.5 16.5 0 0 0 5.064-2.595a17.3 17.3 0 0 0-2.973-11.59M8.678 14.813a1.94 1.94 0 0 1-1.8-2.045a1.93 1.93 0 0 1 1.8-2.047a1.92 1.92 0 0 1 1.8 2.047a1.93 1.93 0 0 1-1.8 2.045m6.644 0a1.94 1.94 0 0 1-1.8-2.045a1.93 1.93 0 0 1 1.8-2.047a1.92 1.92 0 0 1 1.8 2.047a1.93 1.93 0 0 1-1.8 2.045\"/>"}, "dna-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.041 13.862A5 5 0 0 1 17 17.831V21M7 3v3.169a5 5 0 0 0 1.891 3.916M17 3v3.169a5 5 0 0 1-2.428 4.288l-5.144 3.086A5 5 0 0 0 7 17.831V21M7 5h10M7.399 8h9.252M8 16h8.652M7 19h10\"/>"}, "dollar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 17.345a4.76 4.76 0 0 0 2.558 1.618c2.274.589 4.512-.446 4.999-2.31c.487-1.866-1.273-3.9-3.546-4.49S7.977 9.54 8.464 7.675s2.724-2.899 4.998-2.31c.982.236 1.87.793 2.538 1.592m-3.879 12.171V21m0-18v2.2\"/>"}, "dollar-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.1 2c.6 0 1 .4 1 1v1.3a6 6 0 0 1 3.7 2a1 1 0 1 1-1.6 1.3c-.5-.6-1.2-1-2-1.3a4.2 4.2 0 0 0-1.1-.1c-1.4 0-2.4.8-2.7 1.7c-.1.6 0 1.2.6 1.9c.5.6 1.3 1.2 2.3 1.4c1.3.3 2.5 1.1 3.3 2.1a4 4 0 0 1 1 3.6a4.3 4.3 0 0 1-3.5 3V21a1 1 0 1 1-2 0v-1a6 6 0 0 1-3.9-2a1 1 0 1 1 1.6-1.3c.5.7 1.2 1.1 2 1.3c2 .5 3.5-.4 3.8-1.6a2 2 0 0 0-.6-1.8a4.4 4.4 0 0 0-2.2-1.5a6.4 6.4 0 0 1-3.4-2a4 4 0 0 1-.9-3.7c.4-1.7 2-2.8 3.6-3.1V3c0-.6.5-1 1-1\" clip-rule=\"evenodd\"/>", "hidden": true}, "dots-horizontal-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"3\" d=\"M6 12h.01m6 0h.01m5.99 0h.01\"/>"}, "dots-horizontal-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"3\" d=\"M6 12h0m6 0h0m6 0h0\"/>", "hidden": true}, "dots-vertical-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"3\" d=\"M12 6h.01M12 12h.01M12 18h.01\"/>"}, "dots-vertical-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"3\" d=\"M12 6h0m0 6h0m0 6h0\"/>", "hidden": true}, "download-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5l-4 5l-4-5m9 8h.01\"/>"}, "download-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M13 11.15V4a1 1 0 1 0-2 0v7.15L8.78 8.374a1 1 0 1 0-1.56 1.25l4 5a1 1 0 0 0 1.56 0l4-5a1 1 0 1 0-1.56-1.25z\"/><path d=\"M9.657 15.874L7.358 13H5a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-2.358l-2.3 2.874a3 3 0 0 1-4.685 0M17 16a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\"/></g>"}, "draw-square-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 6.5h2M11 18h2m-7-5v-2m12 2v-2M5 8h2a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1m0 12h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1m12 0h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1m0-12h2a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1\"/>"}, "draw-square-solid": {"body": "<g fill=\"currentColor\"><path d=\"M5 3a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm0 12a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2zm12 0a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2zm0-12a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"/><path fill-rule=\"evenodd\" d=\"M10 6.5a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1M10 18a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1m-4-4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v2a1 1 0 0 1-1 1m12 0a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v2a1 1 0 0 1-1 1\" clip-rule=\"evenodd\"/></g>"}, "dribbble-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2a10 10 0 1 0 10 10A10.01 10.01 0 0 0 12 2m6.613 4.614a8.52 8.52 0 0 1 1.93 5.32a20.1 20.1 0 0 0-5.949-.274c-.059-.149-.122-.292-.184-.441a24 24 0 0 0-.566-1.239a11.4 11.4 0 0 0 4.769-3.366M10 3.707a8.8 8.8 0 0 1 2-.238a8.5 8.5 0 0 1 5.664 2.152a9.6 9.6 0 0 1-4.476 3.087A46 46 0 0 0 10 3.707m-6.358 6.555a8.57 8.57 0 0 1 4.73-5.981a54 54 0 0 1 3.168 4.941a32 32 0 0 1-7.9 1.04zm2.01 7.46a8.5 8.5 0 0 1-2.2-5.707v-.262a31.6 31.6 0 0 0 8.777-1.219c.243.477.477.964.692 1.449q-.172.05-.336.1a13.57 13.57 0 0 0-6.942 5.636zM12 20.556a8.5 8.5 0 0 1-5.243-1.8a11.72 11.72 0 0 1 6.7-5.332l.055-.02a35.7 35.7 0 0 1 1.819 6.476a8.5 8.5 0 0 1-3.331.676m4.772-1.462A37 37 0 0 0 15.113 13a12.5 12.5 0 0 1 5.321.364a8.56 8.56 0 0 1-3.66 5.73z\" clip-rule=\"evenodd\"/>"}, "dropbox-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12.013 6.175L7.006 9.369l5.007 3.194l-5.007 3.193L2 12.545l5.006-3.193L2 6.175l5.006-3.194zM6.981 17.806l5.006-3.193l5.006 3.193L11.987 21z\"/><path d=\"m12.013 12.545l5.006-3.194l-5.006-3.176l4.98-3.194L22 6.175l-5.007 3.194L22 12.562l-5.007 3.194z\"/></g>"}, "droplet-bottle-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 6v2.5C10 9 7 9.75 7 12v7c0 .938 0 2 2.143 2h5.714C17 21 17 19.938 17 19v-7c0-2.25-3-3-3-3.5V6m-4 0H9V3h6v3h-1m-4 0h4m0 10a2 2 0 1 1-4 0c0-1.5 1.787-4 2-4s2 2.5 2 4\"/>"}, "droplet-bottle-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12 11c-.23 0-.4.077-.47.112a1.3 1.3 0 0 0-.19.118a2 2 0 0 0-.204.184c-.108.11-.221.247-.332.392c-.226.294-.492.69-.748 1.128c-.255.438-.512.941-.707 1.45C9.159 14.879 9 15.449 9 16a3 3 0 1 0 6 0c0-.551-.159-1.12-.349-1.616a10 10 0 0 0-.707-1.45a11 11 0 0 0-.748-1.128a5 5 0 0 0-.332-.392a2 2 0 0 0-.205-.184a1.2 1.2 0 0 0-.19-.118A1.06 1.06 0 0 0 12 11\"/><path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1v1.094l-.21.129a14 14 0 0 0-.868.555C7.105 9.357 6 10.368 6 12v7.07c-.002.416-.006 1.206.448 1.855C6.993 21.703 7.942 22 9.143 22h5.714c1.201 0 2.15-.297 2.694-1.075c.454-.65.451-1.439.45-1.854L18 19v-7c0-1.633-1.105-2.643-1.922-3.222c-.302-.214-.627-.41-.867-.555L15 8.094V7a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1zm2 6.5V7h2v1.5c0 .537.37.866.465.95c.************.474.34l.303.185c.223.134.442.266.68.435c.683.483 1.078.973 1.078 1.59v7c0 .248-.002.417-.023.565a.5.5 0 0 1-.065.214c-.007.01-.038.055-.17.105c-.152.058-.424.116-.885.116H9.143c-.461 0-.733-.058-.884-.116c-.133-.05-.165-.096-.172-.106a.5.5 0 0 1-.064-.213A4 4 0 0 1 8 19v-7c0-.617.395-1.107 1.078-1.59c.238-.169.457-.301.68-.435q.15-.09.303-.186a3.6 3.6 0 0 0 .474-.34c.095-.083.465-.412.465-.949\" clip-rule=\"evenodd\"/></g>"}, "droplet-bottle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 6h6m-5-3v6c-2 1.5-3 3.5-3 5.5V19a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-4.5c0-2-1-4-3-5.5V3zm4 13a2 2 0 1 1-4 0c0-1.105 1.885-4 2-4s2 2.895 2 4\"/>"}, "droplet-bottle-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12 11a1 1 0 0 0-.638.225c-.062.05-.109.098-.133.124a2 2 0 0 0-.14.168a8 8 0 0 0-.281.397c-.208.31-.467.728-.722 1.18s-.513.954-.712 1.43C9.19 14.96 9 15.508 9 16a3 3 0 1 0 6 0c0-.491-.19-1.04-.374-1.477a14 14 0 0 0-.712-1.428a18 18 0 0 0-.721-1.18a8 8 0 0 0-.281-.398a2 2 0 0 0-.14-.168a1.1 1.1 0 0 0-.298-.231A1 1 0 0 0 12 11\"/><path fill-rule=\"evenodd\" d=\"M10 2a1 1 0 0 0-1 1v2a1 1 0 0 0 0 2v1.516c-1.961 1.631-3 3.777-3 5.984V19a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3v-4.5c0-2.207-1.039-4.353-3-5.984V7a1 1 0 1 0 0-2V3a1 1 0 0 0-1-1zm1 7V7h2v2a1 1 0 0 0 .4.8c1.78 1.335 2.6 3.053 2.6 4.7V19a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-4.5c0-1.647.82-3.365 2.6-4.7A1 1 0 0 0 11 9\" clip-rule=\"evenodd\"/></g>"}, "drumstick-bite-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12.443 9.885L9.936 7.51c.814-.815.805-2.07-.02-2.895c-.827-.827-2.081-.835-2.896-.021c-.479.48-.715 1.201-.566 1.83c-.628-.15-1.35.086-1.83.566c-.814.814-.805 2.069.021 2.895s2.08.835 2.895.02l2.503 2.293m.806-1.38c1.83-1.83 5.124-1.222 7.193.846s2.319 4.623.297 6.645c-1.032 1.032-2.128 1.609-3.23 1.651c.588-1.34.225-2.587-.813-3.28c-.918-.614-2.18-.781-3.548.178c-1.104-2.058-1.303-4.635.101-6.04\"/>"}, "drumstick-bite-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.622 3.907C9.415 2.7 7.527 2.673 6.313 3.887a3.13 3.13 0 0 0-.841 1.554a3.15 3.15 0 0 0-1.555.841c-1.214 1.215-1.186 3.103.02 4.31c.97.968 2.376 1.178 3.522.595l1.479 1.355a6 6 0 0 0-.08 1.063c.014 1.26.392 2.575 1.01 3.726a1 1 0 0 0 1.455.346c.537-.377.998-.497 1.37-.498c.377-.001.73.12 1.048.332c.591.395.867 1.102.452 2.048a1 1 0 0 0 .954 1.4c1.437-.055 2.759-.803 3.9-1.943c1.183-1.185 1.763-2.598 1.675-4.071c-.087-1.453-.813-2.827-1.974-3.988c-1.18-1.18-2.717-1.955-4.255-2.168a5.9 5.9 0 0 0-1.741.011l-1.507-1.427c.548-1.135.33-2.513-.623-3.466m.092 5.718l-1.466-1.39a1 1 0 0 1-.02-1.432c.415-.415.425-1.036-.02-1.481s-1.066-.435-1.48-.02c-.266.264-.36.641-.301.89a1 1 0 0 1-1.204 1.204c-.25-.059-.626.035-.891.3c-.415.415-.425 1.037.02 1.482s1.066.435 1.481.02a1 1 0 0 1 1.383-.03l1.53 1.403q.178-.24.396-.46q.272-.271.572-.486\" clip-rule=\"evenodd\"/>"}, "edit-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m14.304 4.844l2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565l6.844-6.844a2.015 2.015 0 0 1 2.852 0Z\"/>"}, "edit-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M11.32 6.176H5c-1.105 0-2 .949-2 2.118v10.588C3 20.052 3.895 21 5 21h11c1.105 0 2-.948 2-2.118v-7.75l-3.914 4.144A2.46 2.46 0 0 1 12.81 16l-2.681.568c-1.75.37-3.292-1.263-2.942-3.115l.536-2.839c.097-.512.335-.983.684-1.352z\"/><path d=\"M19.846 4.318a2.2 2.2 0 0 0-.437-.692a2 2 0 0 0-.654-.463a1.92 1.92 0 0 0-1.544 0a2 2 0 0 0-.654.463l-.546.578l2.852 3.02l.546-.579a2.1 2.1 0 0 0 .437-.692a2.24 2.24 0 0 0 0-1.635M17.45 8.721L14.597 5.7L9.82 10.76a.54.54 0 0 0-.137.27l-.536 2.84c-.07.37.239.696.588.622l2.682-.567a.5.5 0 0 0 .255-.145l4.778-5.06Z\"/></g>"}, "egg-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 13c0-.888.402-2.383 1-3.272M18.05 14a6 6 0 1 1-12 0c0-3.314 2.686-10 6-10s6 6.686 6 10\"/>"}, "egg-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9.028 4.37C9.848 3.594 10.87 3 12.05 3s2.202.594 3.023 1.37c.825.78 1.533 1.823 2.107 2.926c1.139 2.188 1.87 4.847 1.87 6.704a7 7 0 1 1-14 0c0-1.857.731-4.516 1.87-6.704c.575-1.103 1.282-2.146 2.108-2.926m2.802 5.916a1 1 0 1 0-1.66-1.116c-.375.558-.66 1.257-.853 1.912C9.124 11.736 9 12.431 9 13a1 1 0 0 0 2 0c0-.319.077-.816.235-1.353s.372-1.029.595-1.36\" clip-rule=\"evenodd\"/>"}, "envelope-open-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 8v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8m18 0l-8.029-4.46a2 2 0 0 0-1.942 0L3 8m18 0l-9 6.5L3 8\"/>"}, "envelope-open-solid": {"body": "<g fill=\"currentColor\"><path d=\"m3.62 6.389l8.396 6.724l8.638-6.572l-7.69-4.29a1.98 1.98 0 0 0-1.928 0z\"/><path d=\"m22 8.053l-8.784 6.683a1.98 1.98 0 0 1-2.44-.031L2.02 7.693a1 1 0 0 0-.019.199v11.065C2 20.637 3.343 22 5 22h14c1.657 0 3-1.362 3-3.043z\"/></g>"}, "envelope-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m3.5 5.5l7.893 6.036a1 1 0 0 0 1.214 0L20.5 5.5M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z\"/>"}, "envelope-solid": {"body": "<g fill=\"currentColor\"><path d=\"M2.038 5.61A2 2 0 0 0 2 6v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6q0-.18-.03-.352l-.866.65l-7.89 6.032a2 2 0 0 1-2.429 0L2.884 6.288l-.846-.677Z\"/><path d=\"M20.677 4.117A2 2 0 0 0 20 4H4q-.338.002-.642.105l.758.607L12 10.742L19.9 4.7z\"/></g>"}, "euro-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 10h9.231M6 14h9.231M18 5.086A5.95 5.95 0 0 0 14.615 4c-3.738 0-6.769 3.582-6.769 8s3.031 8 6.769 8A5.94 5.94 0 0 0 18 18.916\"/>"}, "euro-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.3 9c1-3.4 3.8-6 7.3-6a7 7 0 0 1 4 1.3a1 1 0 1 1-1.2 1.6a5 5 0 0 0-2.8-.9c-2.2 0-4.3 1.6-5.2 4h5.8a1 1 0 1 1 0 2H9a8.7 8.7 0 0 0 0 2h6.3a1 1 0 1 1 0 2H9.4c1 2.4 3 4 5.2 4c1 0 2-.3 2.8-.9a1 1 0 1 1 1.2 1.6a7 7 0 0 1-4 1.3c-3.5 0-6.3-2.6-7.3-6H6a1 1 0 1 1 0-2h.9a10.4 10.4 0 0 1 0-2H6a1 1 0 1 1 0-2z\" clip-rule=\"evenodd\"/>", "hidden": true}, "exclamation-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 13V8m0 8h.01M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "exclamation-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m11-4a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0zm-1 7a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "expand-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 4H4m0 0v4m0-4l5 5m7-5h4m0 0v4m0-4l-5 5M8 20H4m0 0v-4m0 4l5-5m7 5h4m0 0v-4m0 4l-5-5\"/>"}, "expand-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 4H4v4m.5-3.5L9 9m7-5h4v4m-.5-3.5L15 9M8 20H4v-4m.5 3.5L9 15m7 5h4v-4m-.5 3.5L15 15\"/>", "hidden": true}, "eye-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"><path d=\"M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6s4.03-6 9-6s9 4.8 9 6Z\"/><path d=\"M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/></g>"}, "eye-slash-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3.933 13.909A4.36 4.36 0 0 1 3 12c0-1 4-6 9-6m7.6 3.8A5.07 5.07 0 0 1 21 12c0 1-3 6-9 6q-.471 0-.918-.04M5 19L19 5m-4 7a3 3 0 1 1-6 0a3 3 0 0 1 6 0\"/>"}, "eye-slash-solid": {"body": "<g fill=\"currentColor\"><path d=\"m4 15.6l3.055-3.056A5 5 0 0 1 7 12.012a5.006 5.006 0 0 1 5-5q.268.014.532.054l1.744-1.744A9 9 0 0 0 12 5.012c-5.388 0-10 5.336-10 7A6.5 6.5 0 0 0 4 15.6\"/><path d=\"m14.7 10.726l4.995-5.007A.998.998 0 0 0 18.99 4a1 1 0 0 0-.71.305l-4.995 5.007a3 3 0 0 0-.588-.21l-.035-.01a2.98 2.98 0 0 0-3.584 3.583c0 .012.008.022.01.033q.075.307.211.59l-4.995 4.983a1 1 0 1 0 1.414 1.414l4.995-4.983q.284.137.59.211c.011 0 .021.007.033.01a2.982 2.982 0 0 0 3.584-3.584c0-.012-.008-.023-.011-.035a3 3 0 0 0-.21-.588Z\"/><path d=\"m19.821 8.605l-2.857 2.857a4.952 4.952 0 0 1-5.514 5.514l-1.785 1.785c.767.166 1.55.25 2.335.251c6.453 0 10-5.258 10-7c0-1.166-1.637-2.874-2.179-3.407\"/></g>"}, "eye-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4.998 7.78C6.729 6.345 9.198 5 12 5s5.27 1.345 7.002 2.78a12.7 12.7 0 0 1 2.096 2.183c.253.344.465.682.618.997c.14.286.284.658.284 1.04s-.145.754-.284 1.04a6.6 6.6 0 0 1-.618.997a12.7 12.7 0 0 1-2.096 2.183C17.271 17.655 14.802 19 12 19s-5.27-1.345-7.002-2.78a12.7 12.7 0 0 1-2.096-2.183a6.6 6.6 0 0 1-.618-.997C2.144 12.754 2 12.382 2 12s.145-.754.284-1.04c.153-.315.365-.653.618-.997A12.7 12.7 0 0 1 4.998 7.78M12 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6\" clip-rule=\"evenodd\"/>"}, "face-explode-outline": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M12 17a2 2 0 0 1 2 2h-4a2 2 0 0 1 2-2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.815 9H16.5a2 2 0 1 0-1.03-3.707A2 2 0 0 0 15.5 5A1.992 1.992 0 0 0 12 3.69A1.992 1.992 0 0 0 8.5 5q.003.147.03.293A2 2 0 1 0 7.5 9h3.388m2.927-.985v3.604M10.228 9v2.574M15 16h.01M9 16h.01m11.962-4.426a1.805 1.805 0 0 1-1.74 1.326a1.89 1.89 0 0 1-1.811-1.326a1.9 1.9 0 0 1-3.621 0a1.8 1.8 0 0 1-1.749 1.326a1.98 1.98 0 0 1-1.87-1.326A1.76 1.76 0 0 1 8.46 12.9a2.035 2.035 0 0 1-1.905-1.326A1.9 1.9 0 0 1 4.74 12.9A1.805 1.805 0 0 1 3 11.574V12a9 9 0 0 0 18 0z\"/></g>"}, "face-explode-solid": {"body": "<path fill=\"currentColor\" d=\"M21.972 11.517a.527.527 0 0 0-1.034-.105a1.38 1.38 0 0 1-1.324 1.01a1.47 1.47 0 0 1-1.4-1.009a.526.526 0 0 0-1.015 0a1.467 1.467 0 0 1-2.737.143l-.049-.204l.021-.146V9.369h2.304a2.63 2.63 0 0 0 2.631-2.632a2.68 2.68 0 0 0-2.654-2.632l-.526.022l-.13-.369A2.63 2.63 0 0 0 13.579 2c-.461 0-.915.124-1.313.358L12 2.513l-.266-.155A2.6 2.6 0 0 0 10.422 2a2.63 2.63 0 0 0-2.483 1.759l-.13.37l-.518-.024a2.68 2.68 0 0 0-2.66 2.632A2.63 2.63 0 0 0 7.264 9.37H9.61v1.887l-.007.09l-.028.08a1.33 1.33 0 0 1-1.301.996a1.63 1.63 0 0 1-1.502-1.024a.526.526 0 0 0-1.01.013a1.47 1.47 0 0 1-1.404 1.01a1.38 1.38 0 0 1-1.325-1.01a.55.55 0 0 0-.569-.382h-.008a.526.526 0 0 0-.456.526v.446a10.01 10.01 0 0 0 10 10a9.9 9.9 0 0 0 7.067-2.94A10.02 10.02 0 0 0 22 11.966zM8.316 15.685a1.053 1.053 0 1 1 2.105 0a1.053 1.053 0 0 1-2.105 0m1.58 3.684a2.105 2.105 0 0 1 4.21 0zm4.736-2.631a1.052 1.052 0 1 1 0-2.105a1.052 1.052 0 0 1 0 2.105\"/>"}, "face-grin-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.99 9H15M8.99 9H9m12 3a9 9 0 1 1-18 0a9 9 0 0 1 18 0M7 13c0 1 .507 2.397 1.494 3.216a5.5 5.5 0 0 0 7.022 0C16.503 15.397 17 14 17 13c0 0-1.99 1-4.995 1S7 13 7 13\"/>"}, "face-grin-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m5.495.93A.5.5 0 0 0 6.5 13c0 1.19.644 2.438 1.618 3.375C9.099 17.319 10.469 18 12 18s2.9-.681 3.882-1.625c.974-.937 1.618-2.184 1.618-3.375a.5.5 0 0 0-.995-.07a.8.8 0 0 1-.156.096c-.214.106-.554.208-1.006.295c-.896.173-2.111.262-3.343.262s-2.447-.09-3.343-.262c-.452-.087-.792-.19-1.005-.295a.8.8 0 0 1-.157-.096M8.99 8a1 1 0 0 0 0 2H9a1 1 0 1 0 0-2zm6 0a1 1 0 1 0 0 2H15a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "face-grin-stars-outline": {"body": "<g fill=\"none\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 21a9 9 0 1 0 0-18a9 9 0 0 0 0 18\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 13c0 2.038-2.239 4.5-5 4.5S7 15.038 7 13c0 1.444 10 1.444 10 0\"/><path fill=\"currentColor\" d=\"m9 6.811l.618 1.253l1.382.2l-1 .975l.236 1.377L9 9.966l-1.236.65L8 9.239l-1-.975l1.382-.2zm6 0l.618 1.253l1.382.2l-1 .975l.236 1.377L15 9.966l-1.236.65L14 9.239l-1-.975l1.382-.2z\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m9 6.811l.618 1.253l1.382.2l-1 .975l.236 1.377L9 9.966l-1.236.65L8 9.239l-1-.975l1.382-.2zm6 0l.618 1.253l1.382.2l-1 .975l.236 1.377L15 9.966l-1.236.65L14 9.239l-1-.975l1.382-.2z\"/></g>"}, "face-grin-stars-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2M7 12.5a.5.5 0 0 1 .495.43a.8.8 0 0 0 .157.096c.213.106.553.208 1.005.295c.896.173 2.111.262 3.343.262s2.447-.09 3.343-.262c.452-.087.792-.19 1.006-.295a.8.8 0 0 0 .156-.096a.5.5 0 0 1 .995.07c0 1.19-.644 2.438-1.618 3.375C14.9 17.319 13.531 18 12 18s-2.9-.681-3.882-1.625C7.144 15.438 6.5 14.19 6.5 13a.5.5 0 0 1 .5-.5m9.519.417l.003-.004zm-9.038 0l-.003-.004zm.901-4.853L9 6.81l.619 1.253l1.381.2l-1 .976l.236 1.376l-1.237-.65l-1.235.65L8 9.239l-1-.975zm6 0L15 6.81l.619 1.253l1.381.2l-1 .976l.236 1.376l-1.237-.65l-1.235.65L14 9.239l-1-.975z\" clip-rule=\"evenodd\"/>"}, "face-laugh-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 9h.01M8.99 9H9m12 3a9 9 0 1 1-18 0a9 9 0 0 1 18 0M6.6 13a5.5 5.5 0 0 0 10.81 0z\"/>"}, "face-laugh-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2M7.99 9a1 1 0 0 1 1-1H9a1 1 0 0 1 0 2h-.01a1 1 0 0 1-1-1M14 9a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H15a1 1 0 0 1-1-1m-5.506 7.216A5.5 5.5 0 0 1 6.6 13h10.81a5.5 5.5 0 0 1-8.916 3.216\" clip-rule=\"evenodd\"/>"}, "facebook-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13.135 6H15V3h-1.865a4.147 4.147 0 0 0-4.142 4.142V9H7v3h2v9.938h3V12h2.021l.592-3H12V6.591A.6.6 0 0 1 12.592 6z\" clip-rule=\"evenodd\"/>"}, "file-chart-bar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m4 10v-2m3 2v-6m3 6v-3m4-11v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1\"/>"}, "file-chart-bar-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m-1 9a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0zm2-5a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1m4 4a1 1 0 1 0-2 0v3a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "file-check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m4 6l2 2l4-4m4-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1\"/>"}, "file-check-solid": {"body": "<g fill=\"currentColor\"><path d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5z\"/><path fill-rule=\"evenodd\" d=\"M11 7V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m4.707 5.707a1 1 0 0 0-1.414-1.414L11 14.586l-1.293-1.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0z\" clip-rule=\"evenodd\"/></g>"}, "file-circle-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 9V4a1 1 0 0 0-1-1H8.914a1 1 0 0 0-.707.293L4.293 7.207A1 1 0 0 0 4 7.914V20a1 1 0 0 0 1 1h4M9 3v4a1 1 0 0 1-1 1H4m11 6v4m-2-2h4m3 0a5 5 0 1 1-10 0a5 5 0 0 1 10 0\"/>"}, "file-circle-plus-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v6.41A7.5 7.5 0 1 0 10.5 22H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2\"/><path d=\"M9 16a6 6 0 1 1 12 0a6 6 0 0 1-12 0m6-3a1 1 0 0 1 1 1v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0v-1h-1a1 1 0 1 1 0-2h1v-1a1 1 0 0 1 1-1\"/></g>"}, "file-clone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 4v3a1 1 0 0 1-1 1h-3m2 10v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-7.13a1 1 0 0 1 .24-.65L6.7 8.35A1 1 0 0 1 7.46 8H9m-1 4H4m16-7v10a1 1 0 0 1-1 1h-7a1 1 0 0 1-1-1V7.87a1 1 0 0 1 .24-.65l2.46-2.87a1 1 0 0 1 .76-.35H19a1 1 0 0 1 1 1Z\"/>"}, "file-clone-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 12.732A2 2 0 0 1 7 13H3v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2h-2a4 4 0 0 1-4-4zM7 11V7.054a2 2 0 0 0-1.059.644l-2.46 2.87A2 2 0 0 0 3.2 11z\"/><path d=\"M14 3.054V7h-3.8q.111-.232.282-.432l2.46-2.87A2 2 0 0 1 14 3.054M16 3v4a2 2 0 0 1-2 2h-4v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"/></g>"}, "file-code-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m5 4l-2 2l2 2m4-4l2 2l-2 2m5-12v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1\"/>"}, "file-code-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm-.293 9.293a1 1 0 0 1 0 1.414L9.414 14l1.293 1.293a1 1 0 0 1-1.414 1.414l-2-2a1 1 0 0 1 0-1.414l2-2a1 1 0 0 1 1.414 0m2.586 1.414a1 1 0 0 1 1.414-1.414l2 2a1 1 0 0 1 0 1.414l-2 2a1 1 0 0 1-1.414-1.414L14.586 14z\" clip-rule=\"evenodd\"/>"}, "file-copy-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 4v3a1 1 0 0 1-1 1h-3m4 10v1a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h2m11-3v10a1 1 0 0 1-1 1h-7a1 1 0 0 1-1-1V7.87a1 1 0 0 1 .24-.65l2.46-2.87a1 1 0 0 1 .76-.35H18a1 1 0 0 1 1 1Z\"/>"}, "file-copy-alt-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M7 9v6a4 4 0 0 0 4 4h4a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1z\"/><path d=\"M13 3.054V7H9.2a2 2 0 0 1 .281-.432l2.46-2.87A2 2 0 0 1 13 3.054M15 3v4a2 2 0 0 1-2 2H9v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"/></g>"}, "file-copy-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 8v3a1 1 0 0 1-1 1H5m11 4h2a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1h-7a1 1 0 0 0-1 1v1m4 3v10a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1v-7.13a1 1 0 0 1 .24-.65L7.7 8.35A1 1 0 0 1 8.46 8H13a1 1 0 0 1 1 1Z\"/>"}, "file-copy-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M18 3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1V9a4 4 0 0 0-4-4h-3a2 2 0 0 0-1 .267V5a2 2 0 0 1 2-2z\"/><path d=\"M8 7.054V11H4.2a2 2 0 0 1 .281-.432l2.46-2.87A2 2 0 0 1 8 7.054M10 7v4a2 2 0 0 1-2 2H4v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z\"/></g>"}, "file-csv-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 10V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v6M5 19v1a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1M10 3v4a1 1 0 0 1-1 1H5m2.665 9H6.647A1.647 1.647 0 0 1 5 15.353v-1.706A1.647 1.647 0 0 1 6.647 12h1.018M16 12l1.443 4.773L19 12m-6.057-.152l-.943-.02a1.34 1.34 0 0 0-1.359 1.22a1.32 1.32 0 0 0 1.172 1.421l.536.059a1.273 1.273 0 0 1 1.226 1.718c-.2.571-.636.754-1.337.754h-1.13\"/>"}, "file-csv-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2zm1.018 8.828a2.34 2.34 0 0 0-2.373 2.13v.008a2.32 2.32 0 0 0 2.06 2.497l.535.059a1 1 0 0 0 .136.006a.272.272 0 0 1 .263.367l-.008.02l-.018.044l-.078.02a2 2 0 0 1-.297.021h-1.13a1 1 0 1 0 0 2h1.13c.417 0 .892-.05 1.324-.279c.47-.248.78-.648.953-1.134a2.272 2.272 0 0 0-2.115-3.06l-.478-.052a.32.32 0 0 1-.285-.341a.34.34 0 0 1 .344-.306l.94.02a1 1 0 1 0 .043-2l-.943-.02zm7.933 1.482a1 1 0 1 0-1.902-.62l-.57 1.747l-.522-1.726a1 1 0 0 0-1.914.578l1.443 4.773a1 1 0 0 0 1.908.021zm-13.762.88a.65.65 0 0 1 .458-.19h1.018a1 1 0 1 0 0-2H6.647A2.647 2.647 0 0 0 4 13.647v1.706A2.647 2.647 0 0 0 6.647 18h1.018a1 1 0 1 0 0-2H6.647A.647.647 0 0 1 6 15.353v-1.706c0-.172.068-.336.19-.457Z\" clip-rule=\"evenodd\"/>"}, "file-doc-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 10V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v6M5 19v1a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1M10 3v4a1 1 0 0 1-1 1H5m14 9.006h-.335a1.647 1.647 0 0 1-1.647-1.647v-1.706a1.647 1.647 0 0 1 1.647-1.647L19 12M5 12v5h1.375A1.626 1.626 0 0 0 8 15.375v-1.75A1.626 1.626 0 0 0 6.375 12zm9 1.5v2a1.5 1.5 0 0 1-1.5 1.5v0a1.5 1.5 0 0 1-1.5-1.5v-2a1.5 1.5 0 0 1 1.5-1.5v0a1.5 1.5 0 0 1 1.5 1.5\"/>"}, "file-doc-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6 16v-3h.375a.626.626 0 0 1 .625.626v1.749a.626.626 0 0 1-.626.625zm6-2.5a.5.5 0 1 1 1 0v2a.5.5 0 0 1-1 0z\"/><path fill-rule=\"evenodd\" d=\"M11 7V2h7a2 2 0 0 1 2 2v5h1a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-1a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2H3a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1h6a2 2 0 0 0 2-2m7.683 6.006l1.335-.024l-.037-2l-1.327.024a2.647 2.647 0 0 0-2.636 2.647v1.706a2.647 2.647 0 0 0 2.647 2.647H20v-2h-1.335a.647.647 0 0 1-.647-.647v-1.706a.647.647 0 0 1 .647-.647zM5 11a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 9 15.375v-1.75A2.626 2.626 0 0 0 6.375 11zm7.5 0a2.5 2.5 0 0 0-2.5 2.5v2a2.5 2.5 0 0 0 5 0v-2a2.5 2.5 0 0 0-2.5-2.5\" clip-rule=\"evenodd\"/><path d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5z\"/></g>"}, "file-export-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 10V4a1 1 0 0 0-1-1H9.914a1 1 0 0 0-.707.293L5.293 7.207A1 1 0 0 0 5 7.914V20a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2M10 3v4a1 1 0 0 1-1 1H5m5 6h9m0 0l-2-2m2 2l-2 2\"/>"}, "file-export-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v9.293l-2-2a1 1 0 0 0-1.414 1.414l.293.293h-6.586a1 1 0 1 0 0 2h6.586l-.293.293A1 1 0 0 0 18 16.707l2-2V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2\" clip-rule=\"evenodd\"/>"}, "file-icvoice-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.2a2 2 0 0 0-.5.4l-4 3.9a2 2 0 0 0-.3.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m2-2a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2zm0 3a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2zm-6 4c0-.6.4-1 1-1h8c.6 0 1 .4 1 1v6c0 .6-.4 1-1 1H8a1 1 0 0 1-1-1zm8 1v1h-2v-1zm0 3h-2v1h2zm-4-3v1H9v-1zm0 3H9v1h2z\" clip-rule=\"evenodd\"/>", "hidden": true}, "file-image-outline": {"body": "<g fill=\"none\"><path fill=\"currentColor\" d=\"M16 18H8l2.5-6l2 4l1.5-2zm-1-8.5a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m14-4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1M8 18h8l-2-4l-1.5 2l-2-4zm7-8.5a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0\"/></g>"}, "file-image-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm.394 9.553a1 1 0 0 0-1.817.062l-2.5 6A1 1 0 0 0 8 19h8a1 1 0 0 0 .894-1.447l-2-4A1 1 0 0 0 13.2 13.4l-.53.706zM13 9.5a1.5 1.5 0 1 1 3 0a1.5 1.5 0 0 1-3 0\" clip-rule=\"evenodd\"/>"}, "file-import-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1v-4m5-13v4a1 1 0 0 1-1 1H5m0 6h9m0 0l-2-2m2 2l-2 2\"/>"}, "file-import-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-5h7.586l-.293.293a1 1 0 0 0 1.414 1.414l2-2a1 1 0 0 0 0-1.414l-2-2a1 1 0 0 0-1.414 1.414l.293.293H4V9h5a2 2 0 0 0 2-2\" clip-rule=\"evenodd\"/>"}, "file-invoice-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m8-2h3m-3 3h3m-4 3v6m4-3H8M19 4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1M8 12v6h8v-6z\"/>"}, "file-invoice-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m2-2a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2zm0 3a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2zm-6 4a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1zm8 1v1h-2v-1zm0 3h-2v1h2zm-4-3v1H9v-1zm0 3H9v1h2z\" clip-rule=\"evenodd\"/>"}, "file-lines-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m4 8h6m-6-4h6m4-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1\"/>"}, "file-lines-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zM8 16a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m1-5a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "file-music-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m8 7.5V8s3 1 3 4m3-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1m-6 12c0 1.105-1.12 2-2.5 2S8 17.105 8 16s1.12-2 2.5-2s2.5.895 2.5 2\"/>"}, "file-music-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m2.318.052h-.002A1 1 0 0 0 12 8v5.293A4 4 0 0 0 10.5 13C8.787 13 7 14.146 7 16s1.787 3 3.5 3s3.5-1.146 3.5-3q0-.16-.017-.313A1 1 0 0 0 14 15.5V9.766c.538.493 1 1.204 1 2.234a1 1 0 1 0 2 0c0-1.881-.956-3.14-1.86-3.893a6.4 6.4 0 0 0-1.636-.985l-.165-.063l-.014-.005l-.005-.001zM9 16c0-.356.452-1 1.5-1s1.5.644 1.5 1s-.452 1-1.5 1S9 16.356 9 16\" clip-rule=\"evenodd\"/>"}, "file-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m14-4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1Z\"/>"}, "file-paste-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 20H5a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h2.429M7 8h3M8 8V4h4v2m4 0V5h-4m3 4v3a1 1 0 0 1-1 1h-3m9-3v9a1 1 0 0 1-1 1h-7a1 1 0 0 1-1-1v-6.397a1 1 0 0 1 .27-.683l2.434-2.603a1 1 0 0 1 .73-.317H19a1 1 0 0 1 1 1\"/>"}, "file-paste-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M6.5 3.85c0-.47.392-.85.875-.85h5.25c.483 0 .875.38.875.85h1.75c.966 0 1.75.761 1.75 1.7V6h-1c-.728 0-1.732-.06-2.434.095a4 4 0 0 0-.88.307l-.061-.002h-.875V4.7h-3.5v1.7h-.875a.863.863 0 0 0-.875.85c0 .47.392.85.875.85h3.36L9.077 9.871a4 4 0 0 0-.892 1.526C7.97 12.083 8 13.268 8 14v5c0 .729.195 1.412.535 2H4.75C3.784 21 3 20.239 3 19.3V5.55c0-.939.784-1.7 1.75-1.7z\"/><path d=\"M14 8.048V12h-3.907a2 2 0 0 1 .446-.763l2.434-2.603A2 2 0 0 1 14 8.048M16 8v4a2 2 0 0 1-2 2h-4v5a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2v-9a2 2 0 0 0-2-2z\"/></g>"}, "file-pdf-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 17v-5h1.5a1.5 1.5 0 1 1 0 3H5m12 2v-5h2m-2 3h2M5 10V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v6M5 19v1a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1M10 3v4a1 1 0 0 1-1 1H5m6 4v5h1.375A1.627 1.627 0 0 0 14 15.375v-1.75A1.627 1.627 0 0 0 12.375 12z\"/>"}, "file-pdf-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1m4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "file-pen-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 5V4a1 1 0 0 0-1-1H8.914a1 1 0 0 0-.707.293L4.293 7.207A1 1 0 0 0 4 7.914V20a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5M9 3v4a1 1 0 0 1-1 1H4m11.383.772l2.745 2.746m1.215-3.906a2.09 2.09 0 0 1 0 2.953l-6.65 6.646L9 17.95l.739-3.692l6.646-6.646a2.087 2.087 0 0 1 2.958 0\"/>"}, "file-pen-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 7V2.221a2 2 0 0 0-.5.365L3.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v.126a5.09 5.09 0 0 0-4.74 1.368v.001l-6.642 6.642a3 3 0 0 0-.82 1.532l-.74 3.692a3 3 0 0 0 3.53 3.53l3.694-.738a3 3 0 0 0 1.532-.82L19 15.149V20a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2\"/><path d=\"M17.447 8.08a1.09 1.09 0 0 1 1.187.238l.002.001a1.09 1.09 0 0 1 0 1.539l-.377.377l-1.54-1.542l.373-.374l.002-.001q.152-.154.353-.237Zm-2.143 2.027l-4.644 4.644l-.385 1.924l1.925-.385l4.644-4.642l-1.54-1.54Zm2.56-4.11a3.1 3.1 0 0 0-2.187.909l-6.645 6.645a1 1 0 0 0-.274.51l-.739 3.693a1 1 0 0 0 1.177 1.176l3.693-.738a1 1 0 0 0 .51-.274l6.65-6.646a3.088 3.088 0 0 0-2.185-5.275\"/></g>"}, "file-ppt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 17v-5h1.5a1.5 1.5 0 1 1 0 3H5m6 2v-5h1.5a1.5 1.5 0 1 1 0 3H11m7-3v5m-1-5h2M5 10V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1v6M5 19v1a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1M10 3v4a1 1 0 0 1-1 1H5\"/>"}, "file-ppt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1m4.5-3a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5zm1.5 3H12v-1h.5a.5.5 0 0 1 0 1m4.5-3a1 1 0 1 0 0 2v4a1 1 0 1 0 2 0v-4a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "file-search-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m8 7.5l2.5 2.5M19 4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1m-5 9.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "file-search-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m.5 5a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3m0 5c.47 0 .917-.092 1.326-.26l1.967 1.967a1 1 0 0 0 1.414-1.414l-1.817-1.818A3.5 3.5 0 1 0 11.5 17\" clip-rule=\"evenodd\"/>"}, "file-shield-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 9V4a1 1 0 0 0-1-1H8.914a1 1 0 0 0-.707.293L4.293 7.207A1 1 0 0 0 4 7.914V20a1 1 0 0 0 1 1h6M9 3v4a1 1 0 0 1-1 1H4m11 13a11.4 11.4 0 0 1-3.637-3.99A11.14 11.14 0 0 1 10 11.833L15 10l5 1.833a11.14 11.14 0 0 1-1.363 5.176A11.4 11.4 0 0 1 15.001 21Z\"/>"}, "file-shield-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v5.703l-4.311-1.58a2 2 0 0 0-1.377 0l-5 1.832A2 2 0 0 0 8 11.861c.03 2.134.582 4.228 1.607 6.106c.848 1.555 2 2.924 3.382 4.033H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2\"/><path d=\"M15.345 9.061a1 1 0 0 0-.689 0l-5 1.833a1 1 0 0 0-.656.953c.028 1.97.538 3.905 1.485 5.641a12.4 12.4 0 0 0 3.956 4.34a1 1 0 0 0 1.12 0a12.4 12.4 0 0 0 3.954-4.34A12.14 12.14 0 0 0 21 11.848a1 1 0 0 0-.656-.954zM15 19.765a10.4 10.4 0 0 0 2.76-3.235a10.15 10.15 0 0 0 1.206-4.011L15 11.065z\"/></g>"}, "file-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2z\" clip-rule=\"evenodd\"/>"}, "file-video-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m14-4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1ZM9 12h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1Zm5.697 2.395v-.733l1.269-1.219v2.984z\"/>"}, "file-video-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m-2 4a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2zm0 2h2v2H9zm7.965-.557a1 1 0 0 0-1.692-.72l-1.268 1.218a1 1 0 0 0-.308.721v.733a1 1 0 0 0 .37.776l1.267 1.032a1 1 0 0 0 1.631-.776z\" clip-rule=\"evenodd\"/>"}, "file-word-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m4 4l1 5l2-3.333L14 17l1-5m4-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1\"/>"}, "file-word-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2m-1.02 4.804a1 1 0 1 0-1.96.392l1 5a1 1 0 0 0 1.838.319L12 15.61l1.143 1.905a1 1 0 0 0 1.838-.319l1-5a1 1 0 0 0-1.962-.392l-.492 2.463l-.67-1.115a1 1 0 0 0-1.714 0l-.67 1.116l-.492-2.464Z\" clip-rule=\"evenodd\"/>"}, "file-zip-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M10 3v4a1 1 0 0 1-1 1H5m14-4v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1Zm-4 1h.01v.01H15zm-2 2h.01v.01H13zm2 2h.01v.01H15zm-2 2h.01v.01H13zm2 2h.01v.01H15zm-2 2h.01v.01H13zm2 2h.01v.01H15zm-2 2h.01v.01H13z\"/>"}, "file-zip-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm3 2h2.01v2.01h-2V8h2v2.01h-2V12h2v2.01h-2V16h2v2.01h-2v2H12V18h2v-1.99h-2V14h2v-1.99h-2V10h2V8.01h-2V6h2z\" clip-rule=\"evenodd\"/>"}, "filter-dollar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m11 18l-.854-.854a.5.5 0 0 1-.146-.353v-4.417a1 1 0 0 0-.247-.659L4.45 5.66C3.886 5.012 4.345 4 5.204 4h13.55c.866 0 1.323 1.025.744 1.669L16.5 9M14 18.375a2.43 2.43 0 0 0 1.279.72c.477.158 1 .14 1.464-.05s.834-.54 1.036-.977c.246-.829-.637-1.734-1.774-1.995s-2.016-1.16-1.773-1.995a1.95 1.95 0 0 1 1.035-.977a2.12 2.12 0 0 1 1.464-.05c.491.105.935.352 1.27.707m-1.94 5.41V20m0-8v.977\"/>"}, "filter-dollar-solid": {"body": "<g fill=\"currentColor\"><path d=\"M3.699 6.317C2.567 5.024 3.486 3 5.204 3h13.55c1.732 0 2.646 2.05 1.487 3.338L17.47 9.342s-.463-.206-.618-.24a4 4 0 0 0-.921-.106c-.903 0-2.138.66-2.572 1.73c-1.325.85-1.692 1.814-1.793 2.008c-.1.195-.261.532-.34 1.215c-.078.684 0 1.606.524 2.469a3 3 0 0 0-.332.438c-.094.148-.59.874-.352 2.144c-.199 0-.638-.158-.92-.44l-.707-.706A1.5 1.5 0 0 1 9 16.793v-4.417z\"/><path fill-rule=\"evenodd\" d=\"M16.06 11a1 1 0 0 1 1 1v.101a3.43 3.43 0 0 1 1.668.972a1 1 0 1 1-1.456 1.37c-.19-.202-.45-.35-.749-.414a1 1 0 0 1-.107-.029a1 1 0 0 0-.166-.04a1 1 0 0 1-.374 0a1 1 0 0 0-.23.066a.97.97 0 0 0-.45.367l.003.007a.6.6 0 0 0 .14.215c.18.197.497.393.89.484c.746.17 1.437.557 1.919 1.083c.476.52.846 1.306.59 2.17a1 1 0 0 1-.051.135a2.95 2.95 0 0 1-1.627 1.508V20a1 1 0 0 1-1.997.075l-.046-.014a3.43 3.43 0 0 1-1.75-1.006a1 1 0 0 1 1.467-1.359c.19.206.453.357.754.42a1 1 0 0 1 .106.03q.113.037.231.05a1 1 0 0 1 .36-.02q.09-.02.178-.056a.97.97 0 0 0 .45-.368v-.003a.6.6 0 0 0-.14-.216c-.182-.198-.5-.395-.892-.485c-.745-.171-1.435-.555-1.917-1.082c-.478-.52-.844-1.304-.593-2.167a1 1 0 0 1 .052-.14a2.95 2.95 0 0 1 1.737-1.548V12a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/></g>"}, "filter-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18.796 4H5.204a1 1 0 0 0-.753 1.659l5.302 6.058a1 1 0 0 1 .247.659v4.874a.5.5 0 0 0 .2.4l3 2.25a.5.5 0 0 0 .8-.4v-7.124a1 1 0 0 1 .247-.659l5.302-6.059c.566-.646.106-1.658-.753-1.658Z\"/>"}, "filter-solid": {"body": "<path fill=\"currentColor\" d=\"M5.05 3C3.291 3 2.352 5.024 3.51 6.317l5.422 6.059v4.874c0 .472.227.917.613 1.2l3.069 2.25c1.01.742 2.454.036 2.454-1.2v-7.124l5.422-6.059C21.647 5.024 20.708 3 18.95 3z\"/>"}, "fingerprint-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 12a28.1 28.1 0 0 1-1.091 9M7.231 4.37a8.994 8.994 0 0 1 12.88 3.73M2.958 15S3 14.577 3 12a8.95 8.95 0 0 1 1.735-5.307m12.84 3.088A6 6 0 0 1 18 12a30 30 0 0 1-.464 6.232M6 12a6 6 0 0 1 9.352-4.974M4 21a5.96 5.96 0 0 1 1.01-3.328a5.15 5.15 0 0 0 .786-1.926m8.66 2.486a14 14 0 0 1-.962 2.683M7.5 19.336C9 17.092 9 14.845 9 12a3 3 0 1 1 6 0c0 .749 0 1.521-.031 2.311M12 12c0 3 0 6-2 9\"/>"}, "fingerprint-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 12c.1 3-.2 6-1 9M7.1 4.4a9 9 0 0 1 13 3.7M3 15v-3a9 9 0 0 1 1.7-5.3m12.9 3c.3.8.4 1.5.4 2.3c0 2 0 4.2-.5 6.2M6 12a6 6 0 0 1 9.4-5M4 21a6 6 0 0 1 1-3.3a5 5 0 0 0 .8-2m8.7 2.5a14 14 0 0 1-1 2.7m-6-1.6C9 17.1 9 14.8 9 12a3 3 0 1 1 6 0v2.3M12 12c0 3 0 6-2 9\"/>", "hidden": true}, "fire-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.122 17.645a7.2 7.2 0 0 1-2.656 2.495a7.06 7.06 0 0 1-3.52.853a6.6 6.6 0 0 1-3.306-.718a6.73 6.73 0 0 1-2.54-2.266c-2.672-4.57.287-8.846.887-9.668A4.45 4.45 0 0 0 8.07 6.31A4.5 4.5 0 0 0 7.997 4c1.284.965 6.43 3.258 5.525 10.631c1.496-1.136 2.7-3.046 2.846-6.216c1.43 1.061 3.985 5.462 1.754 9.23\"/>"}, "fire-solid": {"body": "<path fill=\"currentColor\" d=\"M8.597 3.2A1 1 0 0 0 7.04 4.289a3.5 3.5 0 0 1 .057 1.795a3.45 3.45 0 0 1-.84 1.575a1 1 0 0 0-.077.094c-.596.817-3.96 5.6-.941 10.762l.03.049a7.73 7.73 0 0 0 2.917 2.602a7.6 7.6 0 0 0 3.772.829a8.06 8.06 0 0 0 3.986-.975a8.2 8.2 0 0 0 3.04-2.864c1.301-2.2 1.184-4.556.588-6.441c-.583-1.848-1.68-3.414-2.607-4.102a1 1 0 0 0-1.594.757c-.067 1.431-.363 2.551-.794 3.431c-.222-2.407-1.127-4.196-2.224-5.524c-1.147-1.39-2.564-2.3-3.323-2.788a9 9 0 0 1-.432-.287Z\"/>"}, "fish-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 12c0 1.6 3.358 4 7.5 4s6.923-3.2 7.5-4c-.577-.8-3.358-4-7.5-4S6 10.4 6 12m0 0L3 9m3 3l-3 3m12.987-3.372h.01m-2-3.613c-1.726 3.302-1.711 5.026-.001 7.97m-4.61-.796L7.7 17.4a1 1 0 0 0 .8 1.6H10c1 0 2.758-3.026 2.758-3.026M9 5l3.056 3.097\"/>"}, "fish-alt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9.707 4.293a1 1 0 0 0-1.414 1.414l1.795 1.795c-.894.268-1.705.627-2.4 1.037c-.75.442-1.395.96-1.863 1.516a5 5 0 0 0-.158.198l-1.96-1.96a1 1 0 0 0-1.414 1.414L4.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414l1.96-1.96q.077.102.158.198c.468.555 1.112 1.073 1.864 1.516l.16.093l-.944 1.24l-.005.006c-.989 1.319-.048 3.2 1.6 3.2H10c.578 0 1.047-.282 1.359-.532a4.8 4.8 0 0 0 .816-.868a8 8 0 0 0 .702-1.119l.012-.022l.003-.007l.002-.005c.074-.147.145-.287.196-.447c.138.006.187-.001.327 0a7 7 0 0 1-.298-.525c-.883-1.526-1.405-2.874-1.408-4.339c-.002-1.461.514-2.899 1.403-4.6l.028-.05c.092-.154.187-.32.28-.486c-.347.003-.618.013-.955.045c-.077-.077-.182-.174-.26-.252zm1.3 12.442a12 12 0 0 1-1.276-.35L8.5 18h1.467a.7.7 0 0 0 .143-.093a3 3 0 0 0 .465-.507a6 6 0 0 0 .432-.665m8.154-1.616c-.94.658-2.121 1.283-3.505 1.62c-.295-.404-.595-.85-.76-1.182l-.031-.06c-.817-1.406-1.152-2.4-1.154-3.364c-.001-.972.335-2.057 1.162-3.645c.242-.405.494-.867.696-1.248c1.421.332 2.632.969 3.592 1.64c1.391.976 2.308 2.06 2.65 2.535a1 1 0 0 1 0 1.17c-.342.475-1.259 1.56-2.65 2.534m-4.174-3.491a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2h-.01a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "fish-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 12c0 1.6 3.358 4 7.5 4s6.923-3.2 7.5-4c-.577-.8-3.358-4-7.5-4S6 10.4 6 12m0 0L3 9m3 3l-3 3m12.987-3.372h.01M14 8c-1.726 3.302-1.71 5.056 0 8\"/>"}, "fish-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13.423 7c-2.227.015-4.247.662-5.734 1.539c-.752.442-1.396.96-1.864 1.516a5 5 0 0 0-.158.198l-1.96-1.96a1 1 0 0 0-1.414 1.414L4.586 12l-2.293 2.293a1 1 0 1 0 1.414 1.414l1.96-1.96q.077.102.158.198c.468.555 1.112 1.073 1.864 1.516c1.486.876 3.504 1.523 5.728 1.539a7 7 0 0 1-.298-.525c-.883-1.526-1.405-2.874-1.408-4.339c-.002-1.461.514-2.899 1.403-4.6l.028-.05c.092-.154.187-.32.28-.486\"/><path fill-rule=\"evenodd\" d=\"M15.656 16.74c1.384-.338 2.565-.963 3.505-1.62c1.391-.976 2.308-2.06 2.65-2.535a1 1 0 0 0 0-1.17c-.342-.475-1.259-1.56-2.65-2.534c-.96-.672-2.17-1.309-3.592-1.641c-.202.381-.454.843-.696 1.248c-.828 1.588-1.163 2.672-1.162 3.645c.002.964.337 1.958 1.154 3.365q.017.03.031.059c.165.332.465.778.76 1.182m.33-6.112a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/></g>"}, "fix-tables-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h4v-4m-5 0v-4m0 4h5m-5-4V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v1.99M3 11h5v4m9.47 4.172l-.869-1.439l-2.816-.235l-2.573-4.257l1.487-2.836l1.444 2.389a1.353 1.353 0 1 0 2.316-1.4l-1.444-2.39h3.136l2.61 4.278l-1.072 2.585l.87 1.438\"/>"}, "flag-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 14v7M5 4.971v9.541c5.6-5.538 8.4 2.64 14-.086v-9.54C13.4 7.61 10.6-.568 5 4.97Z\"/>"}, "flag-solid": {"body": "<path fill=\"currentColor\" d=\"M13.09 3.294c1.924.95 3.422 1.69 5.472.692a1 1 0 0 1 1.438.9v9.54a1 1 0 0 1-.562.9c-2.981 1.45-5.382.24-7.25-.701a39 39 0 0 0-.622-.31c-1.033-.497-1.887-.812-2.756-.77c-.76.036-1.672.357-2.81 1.396V21a1 1 0 1 1-2 0V4.971a1 1 0 0 1 .297-.71c1.522-1.506 2.967-2.185 4.417-2.255c1.407-.068 2.653.453 3.72.967q.337.163.655.32Z\"/>"}, "flask-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.05 3v5c-2.719.934-5 3.24-5 6.2c0 3.756 3.134 6.8 7 6.8s7-3.044 7-6.8c0-2.96-2.281-5.266-5-6.2V3m-4 0h4m-4 0h-2m6 0h2M5.098 15h13.904\"/>"}, "flask-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.05 2a1 1 0 1 0 0 2h1v3.315c-2.699 1.164-5 3.63-5 6.885c0 4.335 3.61 7.8 8 7.8s8-3.465 8-7.8c0-3.254-2.301-5.721-5-6.885V4h1a1 1 0 1 0 0-2zm3 6V4h2v4a1 1 0 0 0 .675.946c2.39.82 4.22 2.744 4.32 5.054H6.056c.1-2.31 1.93-4.233 4.32-5.054A1 1 0 0 0 11.05 8\" clip-rule=\"evenodd\"/>"}, "floppy-disk-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M11 16h2m6.707-9.293l-2.414-2.414A1 1 0 0 0 16.586 4H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V7.414a1 1 0 0 0-.293-.707ZM16 20v-6a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v6zM9 4h6v3a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1z\"/>"}, "floppy-disk-alt-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7.414A2 2 0 0 0 20.414 6L18 3.586A2 2 0 0 0 16.586 3zm3 11a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v6H8zm1-7V5h6v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1\"/><path d=\"M14 17h-4v-2h4z\"/></g>"}, "floppy-disk-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M4 5a1 1 0 0 1 1-1h11.586a1 1 0 0 1 .707.293l2.414 2.414a1 1 0 0 1 .293.707V19a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1z\"/><path d=\"M8 4h8v4H8zm7 10a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/></g>"}, "floppy-disk-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7.414A2 2 0 0 0 20.414 6L18 3.586A2 2 0 0 0 16.586 3zm10 11a3 3 0 1 1-6 0a3 3 0 0 1 6 0M8 7V5h8v2a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "flowbite-solid": {"body": "<g fill=\"currentColor\"><path d=\"M15.907 11.998L10.332 9.23a1 1 0 0 1-.16-.037l-.018-.007v6.554c0 .**************.051l2.388-2.974z\"/><path d=\"m11.463 4.054l5.579 3.323A4 4 0 0 1 18.525 9c.332.668.47 1.414.398 2.155a3.07 3.07 0 0 1-.745 1.65a3.1 3.1 0 0 1-1.55.951c-.022.007-.045.005-.07.01q-.093.045-.191.08l-2.72.667l-1.992 2.48c-.18.227-.41.409-.67.534c.***************.137.107a2.05 2.05 0 0 0 1.995.035c.592-.33 2.15-1.201 4.636-2.892l.28-.19c1.328-.895 3.616-2.442 3.967-4.215a9.94 9.94 0 0 0-1.713-4.154a10 10 0 0 0-3.375-2.989a10.1 10.1 0 0 0-8.802-.418c1.162.287 2.287.704 3.354 1.243Z\"/><path d=\"M5.382 17.082v-6.457a3.7 3.7 0 0 1 .45-1.761a3.7 3.7 0 0 1 1.238-1.34a3.92 3.92 0 0 1 3.433-.245q.265.045.508.161l5.753 2.856q.123.075.236.165a2.13 2.13 0 0 0-.953-1.455l-5.51-3.284c-1.74-.857-3.906-1.523-5.244-1.097a10 10 0 0 0-2.5 3.496a9.9 9.9 0 0 0 .283 8.368a10 10 0 0 0 2.73 3.322a17 17 0 0 1-.424-2.729\"/><path d=\"m19.102 16.163l-.272.183c-2.557 1.74-4.169 2.64-4.698 2.935a4.1 4.1 0 0 1-2 .53a3.95 3.95 0 0 1-1.983-.535a3.8 3.8 0 0 1-1.36-1.361a3.75 3.75 0 0 1-.51-1.85a2 2 0 0 1-.043-.26V9.143c0-.024.009-.046.01-.07q-.084.03-.162.07a1.8 1.8 0 0 0-.787 1.516v6.377a10.7 10.7 0 0 0 1.113 4.27a10.11 10.11 0 0 0 8.505-.53a10 10 0 0 0 3.282-2.858a9.9 9.9 0 0 0 1.75-3.97a19.6 19.6 0 0 1-2.845 2.216Z\"/></g>"}, "folder-arrow-right-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.5 8H4m4 6h8m0 0l-2-2m2 2l-2 2M4 6v13a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1h-5.032a1 1 0 0 1-.768-.36l-1.9-2.28a1 1 0 0 0-.768-.36H5a1 1 0 0 0-1 1\"/>"}, "folder-arrow-right-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 4a2 2 0 0 0-2 2v1h10.968l-1.9-2.28A2 2 0 0 0 10.532 4zM3 19V9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m11.707-7.707a1 1 0 0 0-1.414 1.414l.293.293H8a1 1 0 1 0 0 2h5.586l-.293.293a1 1 0 0 0 1.414 1.414l2-2a1 1 0 0 0 0-1.414z\" clip-rule=\"evenodd\"/>"}, "folder-duplicate-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 11H4m15.5 5a.5.5 0 0 0 .5-.5V8a1 1 0 0 0-1-1h-3.75a1 1 0 0 1-.829-.44l-1.436-2.12a1 1 0 0 0-.828-.44H8a1 1 0 0 0-1 1M4 9v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-7a1 1 0 0 0-1-1h-3.75a1 1 0 0 1-.829-.44L9.985 8.44A1 1 0 0 0 9.157 8H5a1 1 0 0 0-1 1\"/>"}, "folder-duplicate-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M6 5a2 2 0 0 1 2-2h4.157a2 2 0 0 1 1.656.879L15.249 6H19a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2v-5a3 3 0 0 0-3-3h-3.22l-1.14-1.682A3 3 0 0 0 9.157 6H6z\"/><path d=\"M3 9a2 2 0 0 1 2-2h4.157a2 2 0 0 1 1.656.879L12.249 10H3zm0 3v7a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2v-7z\"/></g>"}, "folder-open-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 19V6a1 1 0 0 1 1-1h4.032a1 1 0 0 1 .768.36l1.9 2.28a1 1 0 0 0 .768.36H16a1 1 0 0 1 1 1v1M3 19l3-8h15l-3 8z\"/>"}, "folder-open-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 .087.586l2.977-7.937A1 1 0 0 1 6 10h12V9a2 2 0 0 0-2-2h-4.532l-1.9-2.28A2 2 0 0 0 8.032 4zm2.693 8H6.5l-3 8H18l3-8z\" clip-rule=\"evenodd\"/>"}, "folder-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.5 8H4m0-2v13a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1h-5.032a1 1 0 0 1-.768-.36l-1.9-2.28a1 1 0 0 0-.768-.36H5a1 1 0 0 0-1 1\"/>"}, "folder-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 8H4m8 3.5v5M9.5 14h5M4 6v13a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1h-5.032a1 1 0 0 1-.768-.36l-1.9-2.28a1 1 0 0 0-.768-.36H5a1 1 0 0 0-1 1\"/>"}, "folder-plus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 4a2 2 0 0 0-2 2v1h10.968l-1.9-2.28A2 2 0 0 0 10.532 4zM3 19V9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m9-8.5a1 1 0 0 1 1 1V13h1.5a1 1 0 1 1 0 2H13v1.5a1 1 0 1 1-2 0V15H9.5a1 1 0 1 1 0-2H11v-1.5a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/>"}, "folder-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 6a2 2 0 0 1 2-2h5.532a2 2 0 0 1 1.536.72l1.9 2.28H3zm0 3v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9z\" clip-rule=\"evenodd\"/>"}, "font-color-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M20 18.066C20 19.686 18.657 21 17 21s-3-1.314-3-2.934S17 12 17 12s3 4.446 3 6.066\"/><path fill-rule=\"evenodd\" d=\"m10.482 7.525l-1.36 3.457h2.719zm3.75 4.07l-2.717-6.909c-.37-.94-1.697-.94-2.066 0l-2.686 6.831a1 1 0 0 0-.08.202L5.4 14.982h-.418a1 1 0 1 0 0 2h2.75a1 1 0 1 0 0-2h-.183l.787-2h4.292l.367.935a1 1 0 1 0 1.861-.732l-.608-1.548z\" clip-rule=\"evenodd\"/></g>"}, "font-color-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m6.082 15.982l1.573-4m-1.573 4h-1.1m1.1 0h1.65m-.077-4l2.725-6.93a.11.11 0 0 1 .204 0l2.725 6.93m-5.654 0h-.006m.006 0h5.654m0 0l.617 1.569m5.11 4.453c0 1.102-.854 1.996-1.908 1.996s-1.908-.894-1.908-1.996c0-1.103 1.908-4.128 1.908-4.128s1.908 3.025 1.908 4.128Z\"/>"}, "font-family-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m10.579 19l4.297-10.93a.11.11 0 0 1 .205 0L19.378 19m-8.8 0h-1.1m1.1 0h1.65m7.15 0h-1.65m1.65 0h1.1m-7.7-3.985h4.4M3 16l1.567-3.985m0 0l2.73-6.945a.11.11 0 0 1 .205 0l2.504 6.945z\"/>"}, "font-highlight-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 20H5.5a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5H18m-6-1l1.42 1.893a.1.1 0 0 0 .16 0L15 19m-7-6l3.907-9.768a.1.1 0 0 1 .186 0L16 13m-8 0H7m1 0h1.5m6.5 0h-1.5m1.5 0h1m-7-3h4\"/>"}, "forward-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.248 19C3.22 15.77 5.275 8.232 12.466 8.232V6.079a1.025 1.025 0 0 1 1.644-.862l5.479 4.307a1.108 1.108 0 0 1 0 1.723l-5.48 4.307a1.026 1.026 0 0 1-1.643-.861v-2.154C5.275 13.616 4.248 19 4.248 19\"/>"}, "forward-solid": {"body": "<path fill=\"currentColor\" d=\"M5.027 10.9a8.73 8.73 0 0 1 6.422-3.62v-1.2A2.06 2.06 0 0 1 12.61 4.2a1.99 1.99 0 0 1 2.104.23l5.491 4.308a2.11 2.11 0 0 1 .588 2.566a2.1 2.1 0 0 1-.588.734l-5.489 4.308a1.98 1.98 0 0 1-2.104.228a2.07 2.07 0 0 1-1.16-1.876v-.942c-5.33 1.284-6.212 5.251-6.25 5.441a1 1 0 0 1-.923.806h-.06a1 1 0 0 1-.955-.7A10.22 10.22 0 0 1 5.027 10.9\"/>"}, "forward-step-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 6v12M8 6v12l8-6z\"/>"}, "forward-step-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 6a1 1 0 1 0-2 0v4L8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8L15 14v4a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "gift-box-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 21v-9m3-4H7.5a2.5 2.5 0 1 1 0-5c1.5 0 2.875 1.25 3.875 2.5M14 21v-9m-9 0h14v8a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1zM4 8h16a1 1 0 0 1 1 1v3H3V9a1 1 0 0 1 1-1m12.155-5c-3 0-5.5 5-5.5 5h5.5a2.5 2.5 0 0 0 0-5\"/>"}, "gift-box-solid": {"body": "<path fill=\"currentColor\" d=\"M20 7h-.7c.229-.467.349-.98.351-1.5a3.5 3.5 0 0 0-3.5-3.5c-1.717 0-3.215 1.2-4.331 2.481C10.4 2.842 8.949 2 7.5 2A3.5 3.5 0 0 0 4 5.5c.003.52.123 1.033.351 1.5H4a2 2 0 0 0-2 2v2a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V9a2 2 0 0 0-2-2m-9.942 0H7.5a1.5 1.5 0 0 1 0-3c.9 0 2 .754 3.092 2.122c-.219.337-.392.635-.534.878m6.1 0h-3.742c.933-1.368 2.371-3 3.739-3a1.5 1.5 0 0 1 0 3zM13 14h-2v8h2zm-4 0H4v6a2 2 0 0 0 2 2h3zm6 0v8h3a2 2 0 0 0 2-2v-6z\"/>"}, "github-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.006 2a9.85 9.85 0 0 0-6.484 2.44a10.32 10.32 0 0 0-3.393 6.17a10.48 10.48 0 0 0 1.317 6.955a10.05 10.05 0 0 0 5.4 4.418c.504.095.683-.223.683-.494c0-.245-.01-1.052-.014-1.908c-2.78.62-3.366-1.21-3.366-1.21a2.7 2.7 0 0 0-1.11-1.5c-.907-.637.07-.621.07-.621c.317.044.62.163.885.346c.266.183.487.426.647.71c.135.253.318.476.538.655a2.08 2.08 0 0 0 2.37.196c.045-.52.27-1.006.635-1.37c-2.219-.259-4.554-1.138-4.554-5.07a4.02 4.02 0 0 1 1.031-2.75a3.77 3.77 0 0 1 .096-2.713s.839-.275 2.749 1.05a9.26 9.26 0 0 1 5.004 0c1.906-1.325 2.74-1.05 2.74-1.05c.37.858.406 1.828.101 2.713a4.02 4.02 0 0 1 1.029 2.75c0 3.939-2.339 4.805-4.564 5.058a2.47 2.47 0 0 1 .679 1.897c0 1.372-.012 2.477-.012 2.814c0 .272.18.592.687.492a10.05 10.05 0 0 0 5.388-4.421a10.47 10.47 0 0 0 1.313-6.948a10.32 10.32 0 0 0-3.39-6.165A9.85 9.85 0 0 0 12.007 2Z\" clip-rule=\"evenodd\"/>"}, "gitlab-solid": {"body": "<path fill=\"currentColor\" d=\"m20.701 10.126l-.025-.068l-2.45-6.64a.637.637 0 0 0-1.22.07l-1.654 5.255H8.653L7 3.488a.642.642 0 0 0-.967-.385a.66.66 0 0 0-.252.315l-2.455 6.637l-.024.067a4.9 4.9 0 0 0-.124 2.991a4.73 4.73 0 0 0 1.633 2.469l.008.006l.023.017l3.732 2.902l1.846 1.451l1.125.882a.74.74 0 0 0 .915 0l1.124-.882l1.847-1.45l3.755-2.92l.009-.008a4.73 4.73 0 0 0 1.63-2.466a4.9 4.9 0 0 0-.123-2.988\"/>"}, "glass-water-droplet-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 17h10M6 3l1.076 16.133A2 2 0 0 0 9.07 21h5.858a2 2 0 0 0 1.995-1.867L18 3zm8 8a2 2 0 1 1-4 0c0-1.105 1.791-4 2-4s2 2.895 2 4\"/>"}, "glass-water-droplet-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12 6c-.268 0-.46.105-.525.143c-.086.05-.151.103-.19.137a2 2 0 0 0-.194.2c-.1.12-.21.268-.317.425c-.219.32-.479.745-.729 1.199c-.249.454-.5.957-.69 1.433C9.178 9.977 9 10.518 9 11a3 3 0 1 0 6 0c0-.482-.178-1.023-.355-1.463c-.19-.476-.441-.98-.69-1.433c-.25-.454-.51-.879-.729-1.199a5 5 0 0 0-.317-.425a2 2 0 0 0-.194-.2a1.2 1.2 0 0 0-.19-.137A1.05 1.05 0 0 0 12 6\"/><path fill-rule=\"evenodd\" d=\"M6 2a1 1 0 0 0-.998 1.067L6.078 19.2A3 3 0 0 0 9.07 22h5.858a3 3 0 0 0 2.993-2.8l1.076-16.133A1 1 0 0 0 18 2zm1.869 14l-.8-12h9.862l-.8 12z\" clip-rule=\"evenodd\"/></g>"}, "glass-water-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6.315 7c1.419 0 1.419 1.5 2.837 1.5S10.571 7 11.99 7s1.419 1.5 2.837 1.5S17.663 7 17.663 7M6 3l1.076 16.133A2 2 0 0 0 9.07 21h5.858a2 2 0 0 0 1.995-1.867L18 3z\"/>"}, "glass-water-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 2a1 1 0 0 0-.998 1.067L6.078 19.2A3 3 0 0 0 9.07 22h5.858a3 3 0 0 0 2.993-2.8l1.076-16.133A1 1 0 0 0 18 2zm1.212 4.153L7.07 4h9.862l-.165 2.477l-.018.016a6 6 0 0 1-.547.44c-.486.342-.983.567-1.375.567c-.247 0-.339-.064-.691-.437l-.028-.029C13.747 6.652 13.131 6 11.99 6s-1.757.652-2.118 1.034l-.027.029c-.352.373-.444.437-.692.437s-.34-.064-.692-.437l-.027-.029c-.252-.267-.628-.665-1.22-.881\" clip-rule=\"evenodd\"/>"}, "globe-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M4.37 7.657c2.063.528 2.396 2.806 3.202 3.87c1.07 1.413 2.075 1.228 3.192 2.644c1.805 2.289 1.312 5.705 1.312 6.705M20 15h-1a4 4 0 0 0-4 4v1M8.587 3.992c0 .822.112 1.886 1.515 2.58c1.402.693 2.918.351 2.918 2.334c0 .276 0 2.008 1.972 2.008c2.026.031 2.026-1.678 2.026-2.008c0-.65.527-.9 1.177-.9H20M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0Z\"/>"}, "globe-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.64 4.737A8 8 0 0 1 12 4a8 8 0 0 1 6.933 4.006h-.738c-.65 0-1.177.25-1.177.9c0 .33 0 2.04-2.026 2.008c-1.972 0-1.972-1.732-1.972-2.008c0-1.429-.787-1.65-1.752-1.923c-.374-.105-.774-.218-1.166-.411c-1.004-.497-1.347-1.183-1.461-1.835ZM6 4a10.1 10.1 0 0 0-2.812 3.27A9.96 9.96 0 0 0 2 12c0 5.289 4.106 9.619 9.304 9.976l.054.004a10 10 0 0 0 1.155.007h.002a10 10 0 0 0 1.5-.19a10 10 0 0 0 2.259-.754a10.04 10.04 0 0 0 4.987-5.263A9.9 9.9 0 0 0 22 12a10 10 0 0 0-.315-2.5A10 10 0 0 0 12 2a9.96 9.96 0 0 0-6 2m13.372 11.113a2.6 2.6 0 0 0-.75-.112h-.217A3.405 3.405 0 0 0 15 18.405v1.014a8.03 8.03 0 0 0 4.372-4.307ZM12.114 20H12A8 8 0 0 1 5.1 7.95c.95.541 1.421 1.537 1.835 2.415c.209.441.403.853.637 1.162c.54.712 1.063 1.019 1.591 1.328c.52.305 1.047.613 1.6 1.316c1.44 1.825 1.419 4.366 1.35 5.828Z\" clip-rule=\"evenodd\"/>"}, "go-to-next-cell-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h9.5M3 15v-4m0 4h9m-9-4V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v5zm5 0v8m4-8v8m7.1-1.1L21 16m0 0l-1.9-1.9M21 16h-5\"/>"}, "go-to-prev-cell-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h9.5M3 15v-4m0 4h9m-9-4V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v5zm5 0v8m4-8v8m5.9-1.1L16 16m0 0l1.9-1.9M16 16h5\"/>"}, "google-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12.037 21.998a10.3 10.3 0 0 1-7.168-3.049a9.9 9.9 0 0 1-2.868-7.118a9.95 9.95 0 0 1 3.064-6.949A10.37 10.37 0 0 1 12.212 2h.176a9.94 9.94 0 0 1 6.614 2.564L16.457 6.88a6.2 6.2 0 0 0-4.131-1.566a6.9 6.9 0 0 0-4.794 1.913a6.62 6.62 0 0 0-2.045 4.657a6.6 6.6 0 0 0 1.882 4.723a6.9 6.9 0 0 0 4.725 2.07h.143c1.41.072 2.8-.354 3.917-1.2a5.77 5.77 0 0 0 2.172-3.41l.043-.117H12.22v-3.41h9.678q.113.927.1 1.859c-.099 5.741-4.017 9.6-9.746 9.6l-.215-.002Z\" clip-rule=\"evenodd\"/>"}, "graduation-cap-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m3.786 9.5l9 4.5l9-4.5l-9-4.5zm0 0V17m3-6v6.222c0 .348 2 1.778 6 1.778s6-1.374 6-1.778V11\"/>"}, "graduation-cap-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12.447 4.106a1 1 0 0 0-.894 0L2.77 8.497l9.22 4.39L21 8.382z\"/><path d=\"M5 17.222v-5.448l6.57 3.129a1 1 0 0 0 .877-.009L19 11.618v5.604c0 .286-.123.558-.336.748l-.003.003l-.004.003l-.01.01l-.012.01l-.018.014l-.097.078q-.12.096-.34.244a8.6 8.6 0 0 1-1.274.693C15.791 19.52 14.153 20 12 20s-3.79-.48-4.906-.975a8.6 8.6 0 0 1-1.274-.693a6 6 0 0 1-.467-.347l-.01-.009l-.004-.004l-.002-.001l.01-.012v-.001l-.011.012A1 1 0 0 1 5 17.222m-3-6.876l2 .952V17a1 1 0 1 1-2 0z\"/></g>"}, "grid-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.143 4H4.857A.857.857 0 0 0 4 4.857v4.286c0 .473.384.857.857.857h4.286A.857.857 0 0 0 10 9.143V4.857A.857.857 0 0 0 9.143 4m10 0h-4.286a.857.857 0 0 0-.857.857v4.286c0 .473.384.857.857.857h4.286A.857.857 0 0 0 20 9.143V4.857A.857.857 0 0 0 19.143 4m-10 10H4.857a.857.857 0 0 0-.857.857v4.286c0 .473.384.857.857.857h4.286a.857.857 0 0 0 .857-.857v-4.286A.857.857 0 0 0 9.143 14m10 0h-4.286a.857.857 0 0 0-.857.857v4.286c0 .473.384.857.857.857h4.286a.857.857 0 0 0 .857-.857v-4.286a.857.857 0 0 0-.857-.857\"/>"}, "grid-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 17h6m-3 3v-6M4.857 4h4.286c.473 0 .857.384.857.857v4.286a.857.857 0 0 1-.857.857H4.857A.857.857 0 0 1 4 9.143V4.857C4 4.384 4.384 4 4.857 4m10 0h4.286c.473 0 .857.384.857.857v4.286a.857.857 0 0 1-.857.857h-4.286A.857.857 0 0 1 14 9.143V4.857c0-.473.384-.857.857-.857m-10 10h4.286c.473 0 .857.384.857.857v4.286a.857.857 0 0 1-.857.857H4.857A.857.857 0 0 1 4 19.143v-4.286c0-.473.384-.857.857-.857\"/>"}, "grid-plus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4.857 3A1.857 1.857 0 0 0 3 4.857v4.286C3 10.169 3.831 11 4.857 11h4.286A1.857 1.857 0 0 0 11 9.143V4.857A1.857 1.857 0 0 0 9.143 3zm10 0A1.857 1.857 0 0 0 13 4.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 21 9.143V4.857A1.857 1.857 0 0 0 19.143 3zm-10 10A1.857 1.857 0 0 0 3 14.857v4.286C3 20.169 3.831 21 4.857 21h4.286A1.857 1.857 0 0 0 11 19.143v-4.286A1.857 1.857 0 0 0 9.143 13zM18 14a1 1 0 1 0-2 0v2h-2a1 1 0 1 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2z\" clip-rule=\"evenodd\"/>"}, "grid-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4.857 3A1.857 1.857 0 0 0 3 4.857v4.286C3 10.169 3.831 11 4.857 11h4.286A1.857 1.857 0 0 0 11 9.143V4.857A1.857 1.857 0 0 0 9.143 3zm10 0A1.857 1.857 0 0 0 13 4.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 21 9.143V4.857A1.857 1.857 0 0 0 19.143 3zm-10 10A1.857 1.857 0 0 0 3 14.857v4.286C3 20.169 3.831 21 4.857 21h4.286A1.857 1.857 0 0 0 11 19.143v-4.286A1.857 1.857 0 0 0 9.143 13zm10 0A1.857 1.857 0 0 0 13 14.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 21 19.143v-4.286A1.857 1.857 0 0 0 19.143 13z\" clip-rule=\"evenodd\"/>"}, "hammer-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"m20.953 11.763l-2.052-2.052l-2.052 2.052l2.052 2.053zm-1.368-2.736L15.48 4.922l-3.42 3.42l4.105 4.105zm-4.105 2.736l-2.736-2.736l-8.21 8.21l2.737 2.736z\"/><path d=\"m12.93 3.74l1.866 1.867l-2.052 2.052l-1.555-1.555c-.995-.995-3.234-.497-3.918.187l2.737-2.737c.684-.684 1.99-.746 2.923.187Z\"/></g>"}, "hammer-solid": {"body": "<path fill=\"currentColor\" d=\"M8.4 6.763c-.251.1-.383.196-.422.235L6.564 5.584l2.737-2.737c1.113-1.113 3.053-1.097 4.337.187l1.159 1.159a1 1 0 0 1 1.39.022l4.105 4.105a1 1 0 0 1 .023 1.39l1.345 1.346a1 1 0 0 1 0 1.415l-2.052 2.052a1 1 0 0 1-1.414 0l-1.346-1.346a1 1 0 0 1-1.323.039L11.29 8.983a1 1 0 0 1 .04-1.324l-.849-.848c-.18-.18-.606-.322-1.258-.25a3.3 3.3 0 0 0-.824.202Zm1.519 3.675L3.828 16.53a1 1 0 0 0 0 1.414l2.736 2.737a1 1 0 0 0 1.414 0l6.091-6.091l-4.15-4.15Z\"/>"}, "headphones-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 16v-4a8 8 0 1 0-16 0v4m16 0v2a2 2 0 0 1-2 2h-2v-6h2a2 2 0 0 1 2 2ZM4 16v2a2 2 0 0 0 2 2h2v-6H6a2 2 0 0 0-2 2Z\"/>"}, "headphones-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 5a7 7 0 0 0-7 7v1.17c.313-.11.65-.17 1-.17h2a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H6a3 3 0 0 1-3-3v-6a9 9 0 0 1 18 0v6a3 3 0 0 1-3 3h-2a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1h2c.35 0 .687.06 1 .17V12a7 7 0 0 0-7-7\" clip-rule=\"evenodd\"/>"}, "heart-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z\"/>"}, "heart-solid": {"body": "<path fill=\"currentColor\" d=\"m12.75 20.66l6.184-7.098c2.677-2.884 2.559-6.506.754-8.705c-.898-1.095-2.206-1.816-3.72-1.855c-1.293-.034-2.652.43-3.963 1.442c-1.315-1.012-2.678-1.476-3.973-1.442c-1.515.04-2.825.76-3.724 1.855c-1.806 2.201-1.915 5.823.772 8.706l6.183 7.097c.************.743.34a1 1 0 0 0 .743-.34Z\"/>"}, "home-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m4 12l8-8l8 8M6 10.5V19a1 1 0 0 0 1 1h3v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3h3a1 1 0 0 0 1-1v-8.5\"/>"}, "home-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11.293 3.293a1 1 0 0 1 1.414 0l6 6l2 2a1 1 0 0 1-1.414 1.414L19 12.414V19a2 2 0 0 1-2 2h-3a1 1 0 0 1-1-1v-3h-2v3a1 1 0 0 1-1 1H7a2 2 0 0 1-2-2v-6.586l-.293.293a1 1 0 0 1-1.414-1.414l2-2z\" clip-rule=\"evenodd\"/>"}, "horizontal-lines-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\"><path stroke-width=\"2\" d=\"M5 12h14\"/><path d=\"M6 9.5h12m-12-2h12m-12-2h12m-12 13h12m-12-2h12m-12-2h12\"/></g>"}, "hotdog-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m11.5 8l1-1m-1 5.5l1-1m-1 5.5l1-1M9 20V4h-.5A3.5 3.5 0 0 0 5 7.5v9A3.5 3.5 0 0 0 8.5 20zm6 0V4h.5A3.5 3.5 0 0 1 19 7.5v9a3.5 3.5 0 0 1-3.5 3.5zm0-2V6a3 3 0 1 0-6 0v12a3 3 0 1 0 6 0\"/>"}, "hotdog-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10 2.535A4 4 0 0 1 12 2c.729 0 1.412.195 2 .535v18.93A4 4 0 0 1 12 22a4 4 0 0 1-2-.535zm3.207 5.172a1 1 0 0 0-1.414-1.414l-1 1a1 1 0 0 0 1.414 1.414zm0 4.5a1 1 0 0 0-1.414-1.414l-1 1a1 1 0 0 0 1.414 1.414zm0 4.5a1 1 0 0 0-1.414-1.414l-1 1a1 1 0 0 0 1.414 1.414z\" clip-rule=\"evenodd\"/><path d=\"M8 3.028A4.5 4.5 0 0 0 4 7.5v9a4.5 4.5 0 0 0 4 4.473zm8 17.945a4.5 4.5 0 0 0 4-4.473v-9a4.5 4.5 0 0 0-4-4.472z\"/></g>"}, "hourglass-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.5 4h-13m13 16h-13M8 20v-3.333a2 2 0 0 1 .4-1.2L10 12.6a1 1 0 0 0 0-1.2L8.4 8.533a2 2 0 0 1-.4-1.2V4h8v3.333a2 2 0 0 1-.4 1.2L13.957 11.4a1 1 0 0 0 0 1.2l1.643 2.867a2 2 0 0 1 .4 1.2V20z\"/>"}, "hourglass-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.5 3a1 1 0 0 0 0 2H7v2.333a3 3 0 0 0 .556 1.74l1.57 2.814A1 1 0 0 0 9.2 12a1 1 0 0 0-.073.113l-1.57 2.814A3 3 0 0 0 7 16.667V19H5.5a1 1 0 1 0 0 2h13a1 1 0 1 0 0-2H17v-2.333a3 3 0 0 0-.56-1.745l-1.616-2.82a1 1 0 0 0-.067-.102a1 1 0 0 0 .067-.103l1.616-2.819A3 3 0 0 0 17 7.333V5h1.5a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "html-solid": {"body": "<path fill=\"currentColor\" d=\"m3 2l1.578 17.824L12 22l7.467-2.175L21 2zm14.049 6.048H9.075l.172 2.016h7.697l-.626 6.565l-4.246 1.381l-4.281-1.455l-.288-2.932h2.024l.16 1.411l2.4.815l2.346-.763l.297-3.005H7.416l-.562-6.05h10.412z\"/>"}, "icecream-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 6a3 3 0 1 1 6 0M9 6c0 1.043.533 1.963 1.341 2.5M9 6a3 3 0 0 0 0 6m6.39 0a3 3 0 1 0-1.66-5.5M8 12h8l-4 9z\"/>"}, "icecream-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M16 12H8l4 9z\"/><path d=\"M14 6a1 1 0 1 0 2 0zM9.787 9.333a1 1 0 1 0 1.108-1.666zM15.39 11a1 1 0 1 0 0 2zm-2.212-5.333a1 1 0 0 0 1.107 1.666zM8 12v-1a1 1 0 0 0-.914 1.406zm8 0l.914.406A1 1 0 0 0 16 11zm-4 9l-.914.406a1 1 0 0 0 1.828 0zM10 6a2 2 0 0 1 2-2V2a4 4 0 0 0-4 4zm2-2a2 2 0 0 1 2 2h2a4 4 0 0 0-4-4zm-1.105 3.667A2 2 0 0 1 10 6H8c0 1.392.712 2.618 1.787 3.333zM9 11a2 2 0 0 1-2-2H5a4 4 0 0 0 4 4zM7 9a2 2 0 0 1 2-2V5a4 4 0 0 0-4 4zm10.39 0a2 2 0 0 1-2 2v2a4 4 0 0 0 4-4zm-2-2a2 2 0 0 1 2 2h2a4 4 0 0 0-4-4zm-1.106.333C14.6 7.123 14.98 7 15.39 7V5a4 4 0 0 0-2.212.667zM8 13h8v-2H8zm7.086-1.406l-4 9l1.828.812l4-9zm-2.172 9l-4-9l-1.828.812l4 9z\"/></g>"}, "icecream-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 8v6a1 1 0 0 0 1 1h4M7 8a5 5 0 0 1 10 0v6a1 1 0 0 1-1 1h-4M7 8h2a2 2 0 0 1 2 2v.5a1.5 1.5 0 0 0 3 0A1.5 1.5 0 0 1 15.5 9H17m-5 6v6\"/>"}, "icecream-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6.083 7A6.002 6.002 0 0 1 18 8h-2.5a2.5 2.5 0 0 0-2.5 2.5a.5.5 0 0 1-1 0V10a3 3 0 0 0-3-3z\"/><path d=\"M6 9v5a2 2 0 0 0 2 2h3v5a1 1 0 1 0 2 0v-5h3a2 2 0 0 0 2-2v-4h-2.5a.5.5 0 0 0-.5.5a2.5 2.5 0 0 1-5 0V10a1 1 0 0 0-1-1z\"/></g>"}, "image-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m3 16l5-7l6 6.5m6.5 2.5L16 13l-4.286 6M14 10h.01M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "image-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M13 10a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H14a1 1 0 0 1-1-1\"/><path d=\"M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12c0 .556-.227 1.06-.593 1.422A1 1 0 0 1 20.5 20H4a2 2 0 0 1-2-2zm6.892 12l3.833-5.356l-3.99-4.322a1 1 0 0 0-1.549.097L4 12.879V6h16v9.95l-3.257-3.619a1 1 0 0 0-1.557.088L11.2 18z\"/></g>"}, "inbox-full-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 13h3.439a.99.99 0 0 1 .908.6a3.978 3.978 0 0 0 7.306 0a.99.99 0 0 1 .908-.6H20M4 13v6a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-6M4 13l2-9h12l2 9M9 7h6m-7 3h8\"/>"}, "inbox-full-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.024 3.783A1 1 0 0 1 6 3h12a1 1 0 0 1 .976.783L20.802 12h-4.244a1.99 1.99 0 0 0-1.824 1.205a2.978 2.978 0 0 1-5.468 0A1.99 1.99 0 0 0 7.442 12H3.198zM3 14v5a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5h-4.43a4.978 4.978 0 0 1-9.14 0zm5-7a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m0 2a1 1 0 0 0 0 2h8a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "inbox-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 13h3.439a.99.99 0 0 1 .908.6a3.978 3.978 0 0 0 7.306 0a.99.99 0 0 1 .908-.6H20M4 13v6a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-6M4 13l2-9h12l2 9\"/>"}, "inbox-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.024 3.783A1 1 0 0 1 6 3h12a1 1 0 0 1 .976.783L20.802 12h-4.244a1.99 1.99 0 0 0-1.824 1.205a2.978 2.978 0 0 1-5.468 0A1.99 1.99 0 0 0 7.442 12H3.198zM3 14v5a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5h-4.43a4.978 4.978 0 0 1-9.14 0z\" clip-rule=\"evenodd\"/>"}, "incoming-call-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.023 4L14 8.981m0 0h3.03m-3.03 0V6m4.233 8.515L17.09 13.37c-1.143-1.144-1.976-.37-2.839.457a1.684 1.684 0 0 1-2.382 0l-1.871-1.873c-.656-.656-.925-1.46 0-2.384c.849-.868 1.684-1.612.493-2.804L9.3 5.575c-.905-.905-1.992-.383-2.619.237c-3.132 3.111-1.72 6.77 1.41 9.901s6.788 4.547 9.906 1.426c.174-.147.55-.565.647-.772a1.56 1.56 0 0 0-.411-1.852\"/>"}, "incoming-call-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6.978 4a2.55 2.55 0 0 0-1.926.877C3.233 6.7 2.699 8.751 3.153 10.814c.44 1.995 1.778 3.893 3.456 5.572c1.68 1.679 3.577 3.018 5.57 3.459c2.062.456 4.115-.073 5.94-1.885a2.556 2.556 0 0 0 .001-3.861l-1.21-1.21a2.69 2.69 0 0 0-3.802 0l-.617.618a.806.806 0 0 1-1.14 0l-1.854-1.855a.807.807 0 0 1 0-1.14l.618-.62a2.69 2.69 0 0 0 0-3.804l-1.21-1.21A2.55 2.55 0 0 0 6.978 4\"/><path fill-rule=\"evenodd\" d=\"M18.03 8.981a1 1 0 0 1-1 1H14a1 1 0 0 1-1-1V6a1 1 0 0 1 2 0v.581l3.318-3.291a1 1 0 1 1 1.409 1.42l-3.299 3.271h.602a1 1 0 0 1 1 1\" clip-rule=\"evenodd\"/></g>"}, "indent-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 6h12M6 18h12m-5-8h5m-5 4h5M6 9v6l3.5-3z\"/>"}, "indent-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 6a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m0 12a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m1.65-9.76A1 1 0 0 0 5 9v6a1 1 0 0 0 1.65.76l3.5-3a1 1 0 0 0 0-1.52zM12 10a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "info-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 11h2v5m-2 0h4m-2.592-8.5h.01M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "info-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m9.408-5.5a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM10 10a1 1 0 1 0 0 2h1v3h-1a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-1v-4a1 1 0 0 0-1-1z\" clip-rule=\"evenodd\"/>"}, "insert-row-after-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 9V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v3M3 9v9a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V9M3 9h18M8 9V5m4 4V5m4 4V5m-6 9h2m0 0h2m-2 0v-2m0 2v2\"/>"}, "insert-row-before-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-3M3 15V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v9M3 15h18M8 15v4m4-4v4m4-4v4m-6-9h2m0 0h2m-2 0v2m0-2V8\"/>"}, "insert-table-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h10.5M3 15v-4m0 4h11M3 11V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v5M3 11h18m0 0v1M8 11v8m4-8v8m4-8v2m1 4h2m0 0h2m-2 0v2m0-2v-2\"/>"}, "insert-table-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M3 11h18M3 15h18M8 10.792V19m4-8.208V19m4-8.208V19M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z\"/>"}, "instagram-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 8a5 5 0 0 1 5-5h8a5 5 0 0 1 5 5v8a5 5 0 0 1-5 5H8a5 5 0 0 1-5-5zm5-3a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3zm7.597 2.214a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2h-.01a1 1 0 0 1-1-1M12 9a3 3 0 1 0 0 6a3 3 0 0 0 0-6m-5 3a5 5 0 1 1 10 0a5 5 0 0 1-10 0\" clip-rule=\"evenodd\"/>"}, "jar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 6H8m8 0s3 2.5 3 5m-3-5V3H8v3m0 0s-3 2.5-3 5v8.002A3 3 0 0 0 8 22h8c1.657 0 3-1.341 3-2.998V11m0 0h-8v7h8zM6 6h12\"/>"}, "jar-solid": {"body": "<g fill=\"currentColor\"><path d=\"M7 3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v2h1a1 1 0 0 1 .295 1.956c.611.767 1.317 1.842 1.59 3.044H11a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h9v.002A4 4 0 0 1 16 23H8c-2.208 0-4-1.788-4-3.998V11c0-1.598.928-3.07 1.705-4.044A1 1 0 0 1 6 5h1z\"/><path d=\"M20 17v-5h-8v5z\"/></g>"}, "jar-wheat-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 6H8m8 0s3 2.5 3 5v8.002A3 3 0 0 1 16 22H8c-1.657 0-3-1.341-3-2.998V11c0-2.5 3-5 3-5m8 0V3H8v3m4 3v10M6 6h12m-4.834 9.745c-.324.257-.965 1.172-.926 1.228s1.045-.429 1.4-.638c.356-.21.928-1.195.9-1.23c-.027-.036-1.076.404-1.374.64m0-3.972c-.324.257-.965 1.172-.926 1.228s1.045-.429 1.4-.638c.356-.21.928-1.195.9-1.23c-.027-.036-1.076.404-1.374.64m-2.332 3.972c.324.257.965 1.172.926 1.228s-1.045-.429-1.4-.638c-.356-.21-.928-1.195-.9-1.23c.027-.036 1.076.404 1.374.64m0-3.972c.324.257.965 1.172.926 1.228s-1.045-.429-1.4-.638c-.356-.21-.928-1.195-.9-1.23c.027-.036 1.076.404 1.374.64\"/>"}, "jar-wheat-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 2a1 1 0 0 0-1 1v2H6a1 1 0 0 0-.295 1.956C4.928 7.93 4 9.402 4 11v8.002A4 4 0 0 0 8 23h8c2.208 0 4-1.788 4-3.998V11c0-1.598-.928-3.07-1.705-4.044A1 1 0 0 0 18 5h-1V3a1 1 0 0 0-1-1zm5 7a1 1 0 1 0-2 0v1.703a9 9 0 0 0-.547-.265a8 8 0 0 0-.46-.19a3 3 0 0 0-.205-.07a1.05 1.05 0 0 0-.66.01a1.004 1.004 0 0 0-.625 1.25c.021.07.046.127.06.156c.03.068.066.137.1.198a6.4 6.4 0 0 0 .638.94c.117.139.309.35.55.492A12 12 0 0 0 11 13.8v.876a9 9 0 0 0-.547-.266a8 8 0 0 0-.46-.19a3 3 0 0 0-.205-.069a1.06 1.06 0 0 0-.66.01a1.004 1.004 0 0 0-.625 1.25c.021.07.046.127.06.156c.03.067.066.137.1.198a6.4 6.4 0 0 0 .638.94c.117.139.309.35.55.492a12 12 0 0 0 1.149.575V19a1 1 0 0 0 2 0v-1.23q.121-.052.245-.11c.313-.147.686-.336.904-.464a2.3 2.3 0 0 0 .55-.492a6.4 6.4 0 0 0 .638-.94c.034-.06.07-.13.1-.198a1.06 1.06 0 0 0 .094-.596a1.006 1.006 0 0 0-1.148-.857c-.077.011-.141.029-.17.037a3 3 0 0 0-.207.07a8 8 0 0 0-.458.19a9 9 0 0 0-.548.265v-.876q.121-.053.245-.111c.313-.147.686-.336.904-.464a2.3 2.3 0 0 0 .55-.492a6.4 6.4 0 0 0 .638-.94c.034-.06.07-.13.1-.198a1.06 1.06 0 0 0 .094-.596a1.005 1.005 0 0 0-1.148-.856a1 1 0 0 0-.17.037c-.07.02-.144.046-.207.069a8 8 0 0 0-.458.19a9 9 0 0 0-.548.265z\" clip-rule=\"evenodd\"/>"}, "keyboard-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"square\" stroke-width=\"2\"><path d=\"M8 15h7.01v.01H15z\"/><path d=\"M20 6H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1Z\"/><path d=\"M6 9h.01v.01H6zm0 3h.01v.01H6zm0 3h.01v.01H6zm3-6h.01v.01H9zm0 3h.01v.01H9zm3-3h.01v.01H12zm0 3h.01v.01H12zm3 0h.01v.01H15zm3 0h.01v.01H18zm0 3h.01v.01H18zm-3-6h.01v.01H15zm3 0h.01v.01H18z\"/></g>"}, "keyboard-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm5.01 1H5v2.01h2.01zm3 0H8v2.01h2.01zm3 0H11v2.01h2.01zm3 0H14v2.01h2.01zm3 0H17v2.01h2.01zm-12 3H5v2.01h2.01zm3 0H8v2.01h2.01zm3 0H11v2.01h2.01zm3 0H14v2.01h2.01zm3 0H17v2.01h2.01zm-12 3H5v2.01h2.01zM8 14l-.001 2l8.011.01V14zm11.01 0H17v2.01h2.01z\" clip-rule=\"evenodd\"/>"}, "label-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.2 6H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11.2a1 1 0 0 0 .747-.334l4.46-5a1 1 0 0 0 0-1.332l-4.46-5A1 1 0 0 0 15.2 6\"/>"}, "label-solid": {"body": "<path fill=\"currentColor\" d=\"M4 6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h11.613a2 2 0 0 0 1.346-.52l4.4-4a2 2 0 0 0 0-2.96l-4.4-4A2 2 0 0 0 15.613 6z\"/>"}, "landmark-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M3 21h18M4 18h16M6 10v8m4-8v8m4-8v8m4-8v8M4 9.5v-.955a1 1 0 0 1 .458-.84l7-4.52a1 1 0 0 1 1.084 0l7 4.52a1 1 0 0 1 .458.84V9.5a.5.5 0 0 1-.5.5h-15a.5.5 0 0 1-.5-.5Z\"/>"}, "landmark-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10.915 2.345a2 2 0 0 1 2.17 0l7 4.52A2 2 0 0 1 21 8.544V9.5a1.5 1.5 0 0 1-1.5 1.5H19v6h1a1 1 0 1 1 0 2H4a1 1 0 1 1 0-2h1v-6h-.5A1.5 1.5 0 0 1 3 9.5v-.955a2 2 0 0 1 .915-1.68zM17 17v-6h-2v6zm-6-6h2v6h-2zm-2 6v-6H7v6z\" clip-rule=\"evenodd\"/><path d=\"M2 21a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1\"/></g>"}, "language-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m13 19l3.5-9l3.5 9m-6.125-2h5.25M3 7h7m0 0h2m-2 0c0 1.63-.793 3.926-2.239 5.655M7.5 6.818V5m.261 7.655C6.79 13.82 5.521 14.725 4 15m3.761-2.345L5 10m2.761 2.655L10.2 15\"/>"}, "laptop-code-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.357 16V5.786c0-.434.348-.786.778-.786h12.444c.43 0 .778.352.778.786V16m-14 0h-1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-1a1 1 0 0 0-1-1h-1m-14 0h14m-10-8l2.625 2.5L9.357 13m4 0h2\"/>"}, "laptop-code-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M4 5.786C4 4.809 4.786 4 5.778 4h12.444C19.214 4 20 4.81 20 5.786V15H4zM12 12a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1M8.276 6.31a1 1 0 0 1 1.414-.034l2.625 2.5a1 1 0 0 1 0 1.448l-2.625 2.5a1 1 0 1 1-1.38-1.448L10.175 9.5L8.31 7.724a1 1 0 0 1-.034-1.414\" clip-rule=\"evenodd\"/><path d=\"M2 17v1a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-1z\"/></g>"}, "laptop-file-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.286 7V5.786A.78.78 0 0 0 18.508 5H6.063a.78.78 0 0 0-.777.786V16m0 0h-1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h5m-4-3h4m7-6v3a1 1 0 0 1-1 1h-3m8-3v8a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1v-5.397a1 1 0 0 1 .27-.683l2.433-2.603a1 1 0 0 1 .73-.317h3.567a1 1 0 0 1 1 1\"/>"}, "laptop-file-solid": {"body": "<g fill=\"currentColor\"><path d=\"M4 5.786C4 4.809 4.786 4 5.778 4h12.444C19.214 4 20 4.81 20 5.786v1.34A4 4 0 0 0 19 7h-3.566a4 4 0 0 0-2.922 1.268l-2.434 2.603A4 4 0 0 0 9 13.603V19q.002.519.126 1H4a2 2 0 0 1-2-2v-1a2 2 0 0 1 2-2z\"/><path d=\"M15 9.048V13h-3.907a2 2 0 0 1 .446-.763l2.434-2.603A2 2 0 0 1 15 9.048\"/><path d=\"M17 9v4a2 2 0 0 1-2 2h-4v4a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-8a2 2 0 0 0-2-2z\"/></g>"}, "laravel-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" d=\"m17 13l3.464-2V7L17 5l-3.464 2v4M17 13l-3.464-2M17 13v4l-7 4m7-8V9m0 4l-7 4m3.536-6L10.5 12.735M10 21l-3.464-2.132M10 21v-4m-3.464 2v-.132m0 0V15l3.964-2.265m-3.964 6.133L3.5 17V5m0 0L7 3l3.5 2m-7 0l3 2m4-2v7.735M10.5 5l-4 2M17 9l3.5-2M17 9l-3.5-2M10 17l-3.5-2m0 .5V7\"/>"}, "layers-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.005 11.19V12l6.998 4.042L19 12v-.81M5 16.15v.81L11.997 21l6.998-4.042v-.81M12.003 3L5.005 7.042l6.998 4.042L19 7.042z\"/>"}, "layers-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5.005 10.19a1 1 0 0 1 1 1v.233l5.998 3.464L18 11.423v-.232a1 1 0 1 1 2 0V12a1 1 0 0 1-.5.866l-6.997 4.042a1 1 0 0 1-1 0l-6.998-4.042a1 1 0 0 1-.5-.866v-.81a1 1 0 0 1 1-1M5 15.15a1 1 0 0 1 1 1v.232l5.997 3.464l5.998-3.464v-.232a1 1 0 1 1 2 0v.81a1 1 0 0 1-.5.865l-6.998 4.042a1 1 0 0 1-1 0L4.5 17.824a1 1 0 0 1-.5-.866v-.81a1 1 0 0 1 1-1Z\" clip-rule=\"evenodd\"/><path d=\"M12.503 2.134a1 1 0 0 0-1 0L4.501 6.17A1 1 0 0 0 4.5 7.902l7.002 4.047a1 1 0 0 0 1 0l6.998-4.04a1 1 0 0 0 0-1.732z\"/></g>"}, "lemon-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12.306 8.295C9.557 9.382 8.82 11.25 9.002 13.94m5.665-9.759l-1.056.505c-.342.133-1.524.32-1.716.32c-4.832.646-7.745 5.768-5.881 10.339l.45 1.105c.074.179.128.485.135.562l.098 1.181q0 0 0 0c.107 1.306 1.463 2.102 2.631 1.548l.01-.005c.071-.034.974-.472 1.052-.502c.08-.032.357-.138.547-.164l1.168-.156c4.832-.647 7.745-5.769 5.881-10.34l-.45-1.104a1.9 1.9 0 0 1-.135-.563l-.097-1.18c-.108-1.308-1.468-2.105-2.637-1.546\"/>"}, "lemon-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.236 3.279c1.82-.87 3.9.38 4.064 2.365l.098 1.181q.011.139.064.267l.45 1.105c2.105 5.16-1.176 10.972-6.674 11.708l-1.167.156l-.037.01l-.101.03a3 3 0 0 0-.197.073l-.105.05l-.336.16l-.521.252l-.017.008c-1.818.863-3.894-.387-4.057-2.368l-.1-1.192a2 2 0 0 0-.047-.21l-.014-.046l-.451-1.105C2.983 10.562 6.264 4.75 11.762 4.014l.066-.009h.067q-.024 0 .007-.002q.034-.003.159-.02a13 13 0 0 0 .946-.167a4 4 0 0 0 .221-.056zm-1.563 5.946a1 1 0 1 0-.735-1.86c-1.536.607-2.62 1.481-3.264 2.678c-.631 1.173-.767 2.527-.67 3.964A1 1 0 0 0 10 13.872c-.085-1.253.056-2.177.435-2.882c.367-.682 1.025-1.285 2.238-1.765\" clip-rule=\"evenodd\"/>"}, "letter-bold-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 5h4.5a3.5 3.5 0 1 1 0 7H8m0-7v7m0-7H6m2 7h6.5a3.5 3.5 0 1 1 0 7H8m0-7v7m0 0H6\"/>"}, "letter-bold-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5c0-.6.4-1 1-1h6.5a4.5 4.5 0 0 1 3.5 7.3a4.5 4.5 0 0 1-1.5 8.7H6a1 1 0 1 1 0-2h1V6H6a1 1 0 0 1-1-1m4 1v5h3.5a2.5 2.5 0 0 0 0-5zm0 7v5h5.5a2.5 2.5 0 0 0 0-5z\" clip-rule=\"evenodd\"/>", "hidden": true}, "letter-italic-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8.874 19l6.143-14M6 19h6.33m-.66-14H18\"/>"}, "letter-italic-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 4h-3.3a1 1 0 1 0 0 2h1.8L8.2 18H6a1 1 0 1 0 0 2h6.3a1 1 0 1 0 0-2h-1.9l5.3-12H18a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>", "hidden": true}, "letter-underline-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 19h12M8 5v9a4 4 0 0 0 8 0V5M6 5h4m4 0h4\"/>"}, "letter-underline-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10 6H9v8a3 3 0 1 0 6 0V6h-1a1 1 0 1 1 0-2h4a1 1 0 1 1 0 2h-1v8a5 5 0 0 1-2 4h3a1 1 0 1 1 0 2H6a1 1 0 1 1 0-2h3a5 5 0 0 1-2-4V6H6a1 1 0 0 1 0-2h4a1 1 0 1 1 0 2\" clip-rule=\"evenodd\"/>", "hidden": true}, "life-saver-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m13.46 8.291l3.849-3.849a1.5 1.5 0 0 1 2.122 0l.127.127a1.5 1.5 0 0 1 0 2.122l-3.84 3.838a4 4 0 0 0-2.258-2.238m0 0a4 4 0 0 1 2.263 2.238l3.662-3.662a8.96 8.96 0 0 1 0 10.27l-3.676-3.676m-2.25-5.17l3.678-3.676a8.96 8.96 0 0 0-10.27 0l3.662 3.662a4 4 0 0 0-2.238 2.258L4.615 6.863a8.96 8.96 0 0 0 0 10.27l3.662-3.662a4 4 0 0 0 2.258 2.238l-3.672 3.676a8.96 8.96 0 0 0 10.27 0l-3.662-3.662a4 4 0 0 0 2.238-2.262m0 0l3.849 3.848a1.5 1.5 0 0 1 0 2.122l-.127.126a1.5 1.5 0 0 1-2.122 0l-3.838-3.838a4 4 0 0 0 2.238-2.258m.29-1.461a4 4 0 1 1-8 0a4 4 0 0 1 8 0m-7.718 1.471l-3.84 3.838a1.5 1.5 0 0 0 0 2.122l.128.126a1.5 1.5 0 0 0 2.122 0l3.848-3.848a4 4 0 0 1-2.258-2.238m2.248-5.19L6.69 4.442a1.5 1.5 0 0 0-2.122 0l-.127.127a1.5 1.5 0 0 0 0 2.122l3.849 3.848a4 4 0 0 1 2.238-2.258Z\"/>"}, "life-saver-solid": {"body": "<path fill=\"currentColor\" d=\"m7.4 3.736l3.43 3.429A5 5 0 0 1 12.133 7q.535.016 1.056.147l3.41-3.412a2.3 2.3 0 0 1 .451-.344A9.9 9.9 0 0 0 12.268 2a10 10 0 0 0-5.322 1.392q.249.143.454.344m11.451 1.54l-.127-.127a.5.5 0 0 0-.706 0l-2.932 2.932c.03.023.05.054.078.077q.356.292.651.645c.033.038.077.067.11.107l2.926-2.927a.5.5 0 0 0 0-.707m-2.931 9.81c-.025.03-.058.052-.082.082a5 5 0 0 1-.633.639c-.04.036-.072.083-.115.117l2.927 2.927a.5.5 0 0 0 .707 0l.127-.127a.5.5 0 0 0 0-.707l-2.932-2.931Zm-1.443-4.763a3.04 3.04 0 0 0-1.383-1.1l-.012-.007a3 3 0 0 0-1-.213H12a2.96 2.96 0 0 0-2.122.893c-.285.29-.509.634-.657 1.013l-.009.016a3 3 0 0 0-.21 1a3 3 0 0 0 .488 1.716l.032.04a3.04 3.04 0 0 0 1.384 1.1l.012.007c.319.129.657.2 1 .213c.393.015.784-.05 1.15-.192l.033-.018a3 3 0 0 0 1.676-1.7v-.007a2.9 2.9 0 0 0 0-2.207a3 3 0 0 0-.27-.515c-.007-.012-.02-.025-.03-.039m6.137-3.373a2.5 2.5 0 0 1-.349.447l-3.426 3.426c.112.428.166.869.161 1.311a5 5 0 0 1-.148 1.054l3.413 3.412q.2.202.347.444A9.9 9.9 0 0 0 22 12.269a9.9 9.9 0 0 0-1.386-5.319M16.6 20.264l-3.42-3.421c-.386.1-.782.152-1.18.157h-.135q-.535-.016-1.056-.147L7.4 20.265a2.5 2.5 0 0 1-.444.347A9.9 9.9 0 0 0 11.732 22H12a9.9 9.9 0 0 0 5.044-1.388a2.5 2.5 0 0 1-.444-.348M3.735 16.6l3.426-3.426a4.6 4.6 0 0 1-.013-2.367L3.735 7.4a2.5 2.5 0 0 1-.349-.447a9.89 9.89 0 0 0 0 10.1a2.5 2.5 0 0 1 .35-.453Zm5.101-.758a5 5 0 0 1-.65-.645c-.034-.038-.078-.067-.11-.107L5.15 18.017a.5.5 0 0 0 0 .707l.127.127a.5.5 0 0 0 .706 0l2.932-2.933c-.029-.018-.049-.053-.078-.076Zm-.755-6.928c.03-.037.07-.063.1-.1q.274-.33.6-.609c.046-.04.081-.092.128-.13L5.983 5.149a.5.5 0 0 0-.707 0l-.127.127a.5.5 0 0 0 0 .707z\"/>"}, "lightbulb-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 9a3 3 0 0 1 3-3m-2 15h4m0-3c0-4.1 4-4.9 4-9A6 6 0 1 0 6 9c0 4 4 5 4 9z\"/>"}, "lightbulb-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.05 4.05A7 7 0 0 1 19 9c0 2.407-1.197 3.874-2.186 5.084l-.04.048C15.77 15.362 15 16.34 15 18a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1c0-1.612-.77-2.613-1.78-3.875l-.045-.056C6.193 12.842 5 11.352 5 9a7 7 0 0 1 2.05-4.95M9 21a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1m1.586-13.414A2 2 0 0 1 12 7a1 1 0 1 0 0-2a4 4 0 0 0-4 4a1 1 0 0 0 2 0a2 2 0 0 1 .586-1.414\" clip-rule=\"evenodd\"/>"}, "link-break-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M13.213 9.787a3.39 3.39 0 0 0-4.795 0l-3.425 3.426a3.39 3.39 0 0 0 4.795 4.794l.321-.304m-.321-4.49a3.39 3.39 0 0 0 4.795 0l3.424-3.426a3.39 3.39 0 0 0-4.794-4.795l-1.028.961M19.573 20l-1.787-1.786m0 0L16 16.427m1.786 1.787l1.787-1.787m-1.787 1.787L16 20\"/>"}, "link-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.213 9.787a3.39 3.39 0 0 0-4.795 0l-3.425 3.426a3.39 3.39 0 0 0 4.795 4.794l.321-.304m-.321-4.49a3.39 3.39 0 0 0 4.795 0l3.424-3.426a3.39 3.39 0 0 0-4.794-4.795l-1.028.961\"/>"}, "link-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.2 9.8a3.4 3.4 0 0 0-4.8 0L5 13.2A3.4 3.4 0 0 0 9.8 18l.3-.3m-.3-4.5a3.4 3.4 0 0 0 4.8 0L18 9.8A3.4 3.4 0 0 0 13.2 5l-1 1\"/>", "hidden": true}, "linkedin-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12.51 8.796v1.697a3.74 3.74 0 0 1 3.288-1.684c3.455 0 4.202 2.16 4.202 4.97V19.5h-3.2v-5.072c0-1.21-.244-2.766-2.128-2.766c-1.827 0-2.139 1.317-2.139 2.676V19.5h-3.19V8.796h3.168ZM7.2 6.106a1.61 1.61 0 0 1-.988 1.483a1.595 1.595 0 0 1-1.743-.348A1.607 1.607 0 0 1 5.6 4.5a1.6 1.6 0 0 1 1.6 1.606\" clip-rule=\"evenodd\"/><path d=\"M7.2 8.809H4V19.5h3.2z\"/></g>"}, "list-music-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 15.5V5s3 1 3 4m-7-3H4m9 4H4m4 4H4m13 2.4c0 1.326-1.343 2.4-3 2.4s-3-1.075-3-2.4s1.343-2.4 3-2.4s3 1.075 3 2.4\"/>"}, "list-music-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17.316 4.052a.99.99 0 0 0-.9.14c-.262.19-.416.495-.416.82v8.566a4.6 4.6 0 0 0-2-.464c-1.99 0-4 1.342-4 3.443S12.01 20 14 20s4-1.342 4-3.443V6.801c.538.5 1 1.219 1 2.262c0 .56.448 1.013 1 1.013s1-.453 1-1.013c0-1.905-.956-3.18-1.86-3.942a6.4 6.4 0 0 0-1.636-.998l-.166-.063l-.013-.005l-.005-.002h-.002zM4 5.012c-.552 0-1 .454-1 1.013c0 .56.448 1.013 1 1.013h9c.552 0 1-.453 1-1.013s-.448-1.012-1-1.012H4Zm0 4.051c-.552 0-1 .454-1 1.013c0 .56.448 1.013 1 1.013h9c.552 0 1-.454 1-1.013c0-.56-.448-1.013-1-1.013zm0 4.05c-.552 0-1 .454-1 1.014c0 .559.448 1.012 1 1.012h4c.552 0 1-.453 1-1.012c0-.56-.448-1.013-1-1.013z\" clip-rule=\"evenodd\"/>"}, "list-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 8h10M9 12h10M9 16h10M4.99 8H5m-.02 4h.01m0 4H5\"/>"}, "list-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 8c0-.6.4-1 1-1a1 1 0 1 1 0 2a1 1 0 0 1-1-1m4 0c0-.6.4-1 1-1h10a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m-4 4c0-.6.4-1 1-1a1 1 0 1 1 0 2a1 1 0 0 1-1-1m4 0c0-.6.4-1 1-1h10a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1m-4 4c0-.6.4-1 1-1a1 1 0 1 1 0 2a1 1 0 0 1-1-1m4 0c0-.6.4-1 1-1h10a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>", "hidden": true}, "lock-open-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 14v3m4-6V7a3 3 0 1 1 6 0v4M5 11h10a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1\"/>"}, "lock-open-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15 7a2 2 0 1 1 4 0v4a1 1 0 1 0 2 0V7a4 4 0 0 0-8 0v3H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2zm-5 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/>"}, "lock-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 14v3m-3-6V7a3 3 0 1 1 6 0v4m-8 0h10a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1\"/>"}, "lock-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2zm2-3a2 2 0 1 1 4 0v3h-4zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/>"}, "lock-time-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.5 11H5a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h4.5M7 11V7a3 3 0 0 1 6 0v1.5m2.5 5.5v1.5l1 1m3.5-1a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0\"/>"}, "lock-time-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M10 5a2 2 0 0 0-2 2v3h2.4A7.48 7.48 0 0 0 8 15.5a7.48 7.48 0 0 0 2.4 5.5H5a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1V7a4 4 0 1 1 8 0v1.15a7.5 7.5 0 0 0-1.943.685A1 1 0 0 1 12 8.5V7a2 2 0 0 0-2-2\"/><path d=\"M10 15.5a5.5 5.5 0 1 1 11 0a5.5 5.5 0 0 1-11 0m6.5-1.5a1 1 0 1 0-2 0v1.5a1 1 0 0 0 .293.707l1 1a1 1 0 0 0 1.414-1.414l-.707-.707z\"/></g>"}, "magic-wand-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.9 9.7L20 6.6L17.4 4L4 17.4L6.6 20zm0 0L14.3 7M6 7v2m0 0v2m0-2H4m2 0h2m7 7v2m0 0v2m0-2h-2m2 0h2m3-2\"/>", "hidden": true}, "magic-wand-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M17.4 3c.3 0 .6.1.7.3l2.6 2.6c.4.3.4 1 0 1.4l-2.5 2.5l-4-4l2.5-2.5c.2-.2.5-.3.7-.3m-4.6 4.2l-9.5 9.5a1 1 0 0 0 0 1.4l2.6 2.6c.3.4 1 .4 1.4 0l9.5-9.5zM6 6c.6 0 1 .4 1 1v1h1a1 1 0 0 1 0 2H7v1a1 1 0 1 1-2 0v-1H4a1 1 0 0 1 0-2h1V7c0-.6.4-1 1-1m9 9c.6 0 1 .4 1 1v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0v-1h-1a1 1 0 1 1 0-2h1v-1c0-.6.4-1 1-1\" clip-rule=\"evenodd\"/><path d=\"M19 13h-2v2h2zM13 3h-2v2h2zm-2 2H9v2h2zM9 3H7v2h2zm12 8h-2v2h2zm0 4h-2v2h2z\"/></g>", "hidden": true}, "mail-box-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 16v-5.5A3.5 3.5 0 0 0 7.5 7m3.5 9H4v-5.5A3.5 3.5 0 0 1 7.5 7m3.5 9v4M7.5 7H14m0 0V4h2.5M14 7v3m-3.5 6H20v-6a3 3 0 0 0-3-3m-2 9v4m-8-6.5h1\"/>"}, "mail-box-solid": {"body": "<path fill=\"currentColor\" d=\"M17 6h-2V5h1a1 1 0 1 0 0-2h-2a1 1 0 0 0-1 1v2h-.541A5.97 5.97 0 0 1 14 10v4a1 1 0 1 1-2 0v-4c0-2.206-1.794-4-4-4q-.112.002-.22.028C7.686 6.022 7.596 6 7.5 6A4.505 4.505 0 0 0 3 10.5V16a1 1 0 0 0 1 1h7v3a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-3h5a1 1 0 0 0 1-1v-6c0-2.206-1.794-4-4-4m-9 8.5H7a1 1 0 1 1 0-2h1a1 1 0 1 1 0 2\"/>"}, "map-location-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M12 13a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/><path d=\"M17.8 14a7 7 0 1 0-11.5 0h0l.1.3l.3.3L12 21l5.1-6.2l.6-.7l.1-.2Z\"/></g>", "hidden": true}, "map-pin-alt-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M12 13a3 3 0 1 0 0-6a3 3 0 0 0 0 6\"/><path d=\"M17.8 13.938h-.011a7 7 0 1 0-11.464.144h-.016l.14.171q.15.19.3.371L12 21l5.13-6.248q.291-.314.54-.659z\"/></g>"}, "map-pin-alt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11.906 1.994a8 8 0 0 1 8.09 8.421a8 8 0 0 1-1.297 3.957a1 1 0 0 1-.133.204l-.108.129q-.268.365-.573.699l-5.112 6.224a1 1 0 0 1-1.545 0L5.982 15.26l-.002-.002a18 18 0 0 1-.309-.38l-.133-.163a1 1 0 0 1-.13-.202a7.995 7.995 0 0 1 6.498-12.518ZM15 9.997a3 3 0 1 1-5.999 0a3 3 0 0 1 5.999 0\" clip-rule=\"evenodd\"/>"}, "map-pin-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15a6 6 0 1 0 0-12a6 6 0 0 0 0 12m0 0v6M9.5 9A2.5 2.5 0 0 1 12 6.5\"/>"}, "map-pin-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 9a7 7 0 1 1 8 6.93V21a1 1 0 1 1-2 0v-5.07A7 7 0 0 1 5 9m5.94-1.06A1.5 1.5 0 0 1 12 7.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.398.158-.78.44-1.06\" clip-rule=\"evenodd\"/>"}, "martini-glass-citrus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m10 14l6-6H4zm0 0v6m-3 0h6m.035-12a3.5 3.5 0 1 1 1.015 2\"/>"}, "martini-glass-citrus-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 7h8.028a4.5 4.5 0 1 1 2.076 4.31L11 14.414V19h2a1 1 0 1 1 0 2H7a1 1 0 1 1 0-2h2v-4.586L3.293 8.707A1 1 0 0 1 4 7m12 0h-1.95a2.5 2.5 0 1 1 1.536 2.828l1.121-1.12A1 1 0 0 0 16 7\" clip-rule=\"evenodd\"/>"}, "martini-glass-empty-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m12 12l7-8H5zm0 0v8m-3 0h6\"/>"}, "martini-glass-empty-solid": {"body": "<path fill=\"currentColor\" d=\"M5 3a1 1 0 0 0-.753 1.659L11 12.375V19H9a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-2v-6.624l6.753-7.717A1 1 0 0 0 19 3z\"/>"}, "martini-glass-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m12 12l7-8H5zm0 0v8m-3 0h6M8.55 8h6.95\"/>"}, "martini-glass-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a1 1 0 0 0-.753 1.659L11 12.375V19H9a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-2v-6.624l6.753-7.717A1 1 0 0 0 19 3zm10.046 4H8.954l-1.75-2h9.592z\" clip-rule=\"evenodd\"/>"}, "mastercard-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm5.178 12.137a4.137 4.137 0 1 1 1.036-8.144A6.1 6.1 0 0 0 8.726 12c0 1.531.56 2.931 1.488 4.006a4 4 0 0 1-1.036.131M10.726 12c0-1.183.496-2.252 1.294-3.006A4.13 4.13 0 0 1 13.315 12a4.13 4.13 0 0 1-1.294 3.006A4.13 4.13 0 0 1 10.726 12m4.59 0a6.1 6.1 0 0 1-1.489 4.006a4.137 4.137 0 1 0 0-8.013A6.1 6.1 0 0 1 15.315 12Z\" clip-rule=\"evenodd\"/>"}, "merge-cells-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 18v2H4V4h6v2m4 12v2h6V4h-6v2m-6.495 8.495L10 12m0 0L7.505 9.505M10 12H4.052m12.502 2.554L14 12m0 0l2.554-2.554M14 12h5.832\"/>"}, "merge-or-split-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.505 14.495L21 12m0 0l-2.495-2.495M21 12h-5.948m-9.498 2.554L3 12m0 0l2.554-2.554M3 12h5.832M9 19V5h6v14z\"/>"}, "message-caption-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9h5m3 0h2M7 12h2m3 0h5M5 5h14a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-6.616a1 1 0 0 0-.67.257l-2.88 2.592A.5.5 0 0 1 8 18.477V17a1 1 0 0 0-1-1H5a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1\"/>"}, "message-caption-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 6a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-6.616l-2.88 2.592C8.537 20.461 7 19.776 7 18.477V17H5a2 2 0 0 1-2-2zm4 2a1 1 0 0 0 0 2h5a1 1 0 1 0 0-2zm8 0a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm-8 3a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm5 0a1 1 0 1 0 0 2h5a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "message-dots-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 10.5h.01m-4.01 0h.01M8 10.5h.01M5 5h14a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-6.6a1 1 0 0 0-.69.275l-2.866 2.723A.5.5 0 0 1 8 18.635V17a1 1 0 0 0-1-1H5a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1\"/>"}, "message-dots-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 5.983C3 4.888 3.895 4 5 4h14c1.105 0 2 .888 2 1.983v8.923a1.99 1.99 0 0 1-2 1.983h-6.6l-2.867 2.7c-.955.899-2.533.228-2.533-1.08v-1.62H5c-1.105 0-2-.888-2-1.983zm5.706 3.809a1 1 0 1 0-1.412 1.417a1 1 0 1 0 1.412-1.417m2.585.002a1 1 0 1 1 .003 1.414a1 1 0 0 1-.003-1.414m5.415-.002a1 1 0 1 0-1.412 1.417a1 1 0 1 0 1.412-1.417\" clip-rule=\"evenodd\"/>"}, "messages-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 17h6l3 3v-3h2V9h-2M4 4h11v8H9l-3 3v-3H4z\"/>"}, "messages-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M4 3a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h1v2a1 1 0 0 0 1.707.707L9.414 13H15a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1z\"/><path d=\"M8.023 17.215q.05-.046.098-.094L10.243 15H15a3 3 0 0 0 3-3V8h2a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-1v2a1 1 0 0 1-1.707.707L14.586 18H9a1 1 0 0 1-.977-.785\"/></g>"}, "microphone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9v3a5.006 5.006 0 0 1-5 5h-4a5.006 5.006 0 0 1-5-5V9m7 9v3m-3 0h6M11 3h2a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-2a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3\"/>"}, "microphone-slash-outline": {"body": "<path fill=\"currentColor\" d=\"M19.97 9.012a1 1 0 1 0-2 0zm-1 2.988l1 .001V12zm-8.962 4.98l-.001 1h.001zm-3.52-1.46l.708-.708zM5.029 12h-1v.001zm3.984 7.963a1 1 0 1 0 0 2zm5.975 2a1 1 0 0 0 0-2zM7.017 8.017a1 1 0 1 0 2 0zm6.641 4.862a1 1 0 1 0 .667 1.886zm-7.63-2.87a1 1 0 1 0-2 0zm9.953 5.435a1 1 0 1 0 1 1.731zM12 16.979h1a1 1 0 0 0-1-1zM5.736 4.322a1 1 0 0 0-1.414 1.414zm12.528 15.356a1 1 0 0 0 1.414-1.414zM17.97 9.012V12h2V9.012zm0 2.987a4 4 0 0 1-1.168 2.813l1.415 1.414a6 6 0 0 0 1.753-4.225zm-7.962 3.98a4 4 0 0 1-2.813-1.167l-1.414 1.414a6 6 0 0 0 4.225 1.753zm-2.813-1.167a4 4 0 0 1-1.167-2.813l-2 .002a6 6 0 0 0 1.753 4.225zm3.808-10.775h1.992v-2h-1.992zm1.992 0c1.097 0 1.987.89 1.987 1.988h2a3.99 3.99 0 0 0-3.987-3.988zm1.987 1.988v4.98h2v-4.98zm-5.967 0c0-1.098.89-1.988 1.988-1.988v-2a3.99 3.99 0 0 0-3.988 3.988zm-.004 15.938H12v-2H9.012v2Zm2.988 0h2.987v-2H12zM9.016 8.017V6.025h-2v1.992zm5.967 2.987a1.99 1.99 0 0 1-1.325 1.875l.667 1.886a3.99 3.99 0 0 0 2.658-3.76zM6.03 12v-1.992h-2V12zm10.774 2.812a4 4 0 0 1-.823.632l1.002 1.731a6 6 0 0 0 1.236-.949zM4.322 5.736l13.942 13.942l1.414-1.414L5.736 4.322zM12 15.98h-1.992v2H12zm-1 1v3.984h2V16.98z\"/>"}, "microphone-slash-solid": {"body": "<path fill=\"currentColor\" d=\"m15.506 14.097l.994.995A3.99 3.99 0 0 0 17.975 12V9.011a.996.996 0 0 1 1.992 0v2.99a5.98 5.98 0 0 1-2.054 4.503l1.762 1.762a.996.996 0 1 1-1.408 1.408L4.325 5.733a.996.996 0 0 1 1.408-1.408L7.04 5.632a3.984 3.984 0 0 1 3.964-3.59h1.992c2.2 0 3.983 1.783 3.983 3.983v4.98a3.98 3.98 0 0 1-1.473 3.092M4.033 10.008a.996.996 0 1 1 1.992 0V12a3.99 3.99 0 0 0 3.984 3.984H12c.55 0 .996.446.996.996v2.988h1.992a.996.996 0 0 1 0 1.992H9.012a.996.996 0 0 1 0-1.992h1.992v-1.992h-.997a5.98 5.98 0 0 1-5.974-5.974z\"/>"}, "microphone-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5 8a1 1 0 0 1 1 1v3a4.006 4.006 0 0 0 4 4h4a4.006 4.006 0 0 0 4-4V9a1 1 0 1 1 2 0v3.001A6.006 6.006 0 0 1 14.001 18H13v2h2a1 1 0 1 1 0 2H9a1 1 0 1 1 0-2h2v-2H9.999A6.006 6.006 0 0 1 4 12.001V9a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/><path d=\"M7 6a4 4 0 0 1 4-4h2a4 4 0 0 1 4 4v5a4 4 0 0 1-4 4h-2a4 4 0 0 1-4-4z\"/></g>"}, "microscope-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.643 21h14m-3.35-12a5.42 5.42 0 0 1 1.35 3.591c0 2.987-2.396 5.409-5.352 5.409a5.34 5.34 0 0 1-4.648-2.725m-2-.275h6m.437-4.437L10.643 12m5.809-5.767l2.155-2.155M17.53 3l2.155 2.155M10.643 18v3m4-3v3m.731-15.845l-4.31 4.311l2.155 2.155l4.31-4.31z\"/>"}, "microscope-solid": {"body": "<path fill=\"currentColor\" d=\"m17.965 5.492l.37.37a1 1 0 1 0 1.414-1.414l-2.155-2.155a1 1 0 1 0-1.414 1.414l.37.37l-.74.742l-.371-.37a1 1 0 0 0-1.415 0l-4.31 4.31a1 1 0 0 0 0 1.414l.35.35l-.771.77a1 1 0 1 0 1.414 1.414l.77-.77l.392.392a1 1 0 0 0 1.414 0l2.063-2.063c.415.674.654 1.47.654 2.325C16 15.036 14.042 17 11.648 17a4.3 4.3 0 0 1-2.76-1H11a1 1 0 1 0 0-2H5a1 1 0 1 0 0 2h1.268A6.4 6.4 0 0 0 9 18.418V20H5a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2h-4v-1.964a6.42 6.42 0 0 0 3-5.445c0-1.406-.45-2.709-1.213-3.767l.807-.806a1 1 0 0 0 0-1.414l-.37-.371z\"/>"}, "minimize-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 9h4m0 0V5m0 4L4 4m15 5h-4m0 0V5m0 4l5-5M5 15h4m0 0v4m0-4l-5 5m15-5h-4m0 0v4m0-4l5 5\"/>"}, "minimize-solid": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 9h4V5m-.5 3.5L4 4m15 5h-4V5m.5 3.5L20 4M5 15h4v4m-.5-3.5L4 20m15-5h-4v4m.5-3.5L20 20\"/>", "hidden": true}, "minus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12h14\"/>"}, "missed-call-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m17.09 13.371l1.143 1.144a1.56 1.56 0 0 1 .41 1.852c-.095.207-.472.625-.646.771c-3.118 3.122-6.774 1.706-9.905-1.425s-4.543-6.79-1.41-9.901c.626-.62 1.713-1.142 2.618-.237l1.19 1.192c1.192 1.192.357 1.936-.492 2.804c-.925.925-.656 1.728 0 2.384l1.871 1.873a1.684 1.684 0 0 0 2.382 0c.863-.827 1.696-1.6 2.839-.457M14.874 8.98l2.135-2.135m0 0l2.16-1.907m-2.16 1.907l2.16 2.15m-2.16-2.15l-2.135-1.907\"/>"}, "missed-call-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M14.167 10.687a1 1 0 0 1 0-1.414l1.387-1.387l-1.346-1.203a1 1 0 1 1 1.333-1.491l1.472 1.316l1.494-1.32a1 1 0 1 1 1.324 1.5l-1.36 1.2l1.404 1.4a1 1 0 1 1-1.412 1.416l-1.452-1.446l-1.43 1.429a1 1 0 0 1-1.414 0\" clip-rule=\"evenodd\"/><path d=\"M7.978 5a2.55 2.55 0 0 0-1.926.877C4.233 7.7 3.699 9.751 4.153 11.814c.44 1.995 1.778 3.893 3.456 5.572c1.68 1.679 3.577 3.018 5.57 3.459c2.062.456 4.115-.073 5.94-1.885a2.556 2.556 0 0 0 .001-3.861l-1.21-1.21a2.69 2.69 0 0 0-3.802 0l-.617.618a.806.806 0 0 1-1.14 0l-1.854-1.855a.807.807 0 0 1 0-1.14l.618-.62a2.69 2.69 0 0 0 0-3.803l-1.21-1.211A2.56 2.56 0 0 0 7.978 5\"/></g>"}, "mobile-phone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 15h12M6 6h12m-6 12h.01M7 21h10a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1\"/>"}, "mobile-phone-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2zm12 12V5H7v11zm-5 1a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "mongo-db-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.294 11.804c0-3.966 2.14-6.417 3.533-8.014C11.501 3.02 12 2.447 12 2c0 .447.5 1.019 1.172 1.79c1.394 1.597 3.534 4.048 3.534 8.014c0 4.326-2.75 6.95-4.077 7.765L12.37 22h-.707l-.29-2.43c-1.326-.813-4.079-3.437-4.079-7.766m4.064 6.7L12 9.06l.649 9.446l-.65.75z\" clip-rule=\"evenodd\"/>"}, "moon-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 21a9 9 0 0 1-.5-17.986V3c-.354.966-.5 1.911-.5 3a9 9 0 0 0 9 9c.239 0 .254.018.488 0A9 9 0 0 1 12 21\"/>"}, "moon-plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 5v3m0 0v3m0-3h-3m3 0h3m-3.356 7.091c-1.638 0-2.58-.248-3.708-.957a6.77 6.77 0 0 1-2.563-2.909a6.5 6.5 0 0 1-.501-3.788A6.6 6.6 0 0 1 11.596 4a7.96 7.96 0 0 0-3.668 1.565a7.57 7.57 0 0 0-2.38 3.115a7.3 7.3 0 0 0-.469 3.845a7.4 7.4 0 0 0 1.569 3.563a7.8 7.8 0 0 0 3.192 2.343c1.256.5 2.627.675 3.975.507a8 8 0 0 0 3.712-1.463A7.6 7.6 0 0 0 20 14.427c-1.006.52-1.932.664-3.356.664\"/>"}, "moon-plus-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M17 4a1 1 0 0 1 1 1v2h2a1 1 0 1 1 0 2h-2v2a1 1 0 1 1-2 0V9h-2a1 1 0 1 1 0-2h2V5a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/><path d=\"M12.322 4.687a1 1 0 0 0-.877-1.676a8.96 8.96 0 0 0-4.129 1.763a8.57 8.57 0 0 0-2.694 3.527a8.3 8.3 0 0 0-.532 4.371a8.4 8.4 0 0 0 1.779 4.044A8.8 8.8 0 0 0 9.47 19.36a9.1 9.1 0 0 0 4.468.57a9 9 0 0 0 4.179-1.648a8.6 8.6 0 0 0 2.797-3.45a1 1 0 0 0-1.373-1.294a6.1 6.1 0 0 1-2.812.679h-.002a6.1 6.1 0 0 1-3.26-.931a5.77 5.77 0 0 1-2.185-2.478a5.5 5.5 0 0 1-.424-3.205a5.6 5.6 0 0 1 1.464-2.917\"/></g>"}, "moon-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11.675 2.015a1 1 0 0 0-.403.011C6.09 2.4 2 6.722 2 12c0 5.523 4.477 10 10 10c4.356 0 8.058-2.784 9.43-6.667a1 1 0 0 0-1.02-1.33c-.08.006-.105.005-.127.005h-.001l-.028-.002A5 5 0 0 0 20 14a8 8 0 0 1-8-8c0-.952.121-1.752.404-2.558a1 1 0 0 0 .096-.428V3a1 1 0 0 0-.825-.985\" clip-rule=\"evenodd\"/>"}, "mug-hot-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 13h2a2 2 0 1 1 0 4h-2.5M10 3c0 2.4-3 1.6-3 4m8-4c0 2.4-3 1.6-3 4m-7 4l.64 6.398A4 4 0 0 0 9.62 21h.76a4 4 0 0 0 3.98-3.602L15 11z\"/>"}, "mug-hot-solid": {"body": "<g fill=\"currentColor\"><path d=\"M11 3a1 1 0 1 0-2 0c0 .358-.099.51-.198.616c-.149.158-.368.286-.773.502l-.045.024c-.345.183-.848.451-1.245.874C6.276 5.51 6 6.158 6 7a1 1 0 0 0 2 0c0-.358.099-.51.198-.616c.149-.158.368-.286.773-.502l.045-.024c.345-.183.848-.451 1.245-.874C10.724 4.49 11 3.842 11 3m5 0a1 1 0 1 0-2 0c0 .358-.099.51-.198.616c-.149.158-.368.286-.773.502l-.045.024c-.345.183-.848.451-1.245.874C11.276 5.51 11 6.158 11 7a1 1 0 1 0 2 0c0-.358.099-.51.198-.616c.149-.158.368-.286.773-.502l.045-.024c.345-.183.848-.451 1.245-.874C15.724 4.49 16 3.842 16 3\"/><path fill-rule=\"evenodd\" d=\"M5 10a1 1 0 0 0-.995 1.1l.64 6.398A5 5 0 0 0 9.62 22h.76a5 5 0 0 0 4.9-4H17a3 3 0 1 0 0-6h-1.095l.09-.9A1 1 0 0 0 15 10zm12 6h-1.495l.2-2H17a1 1 0 1 1 0 2\" clip-rule=\"evenodd\"/></g>"}, "mug-saucer-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 8h2a2 2 0 1 1 0 4h-2.5M5 19h11M5 6l.64 6.398A4 4 0 0 0 9.62 16h.76a4 4 0 0 0 3.98-3.602L15 6z\"/>"}, "mug-saucer-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5 5a1 1 0 0 0-.995 1.1l.64 6.398A5 5 0 0 0 9.62 17h.76a5 5 0 0 0 4.9-4H17a3 3 0 1 0 0-6h-1.095l.09-.9A1 1 0 0 0 15 5zm12 6h-1.495l.2-2H17a1 1 0 1 1 0 2\" clip-rule=\"evenodd\"/><path d=\"M5 18a1 1 0 1 0 0 2h11a1 1 0 1 0 0-2z\"/></g>"}, "music-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 16.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m0 0V5c2.5 0 6 2.5 4.5 7\"/>"}, "music-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M9.5 13a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7\"/><path fill-rule=\"evenodd\" d=\"M11 5a1 1 0 0 1 1-1c1.544 0 3.324.754 4.515 2.168c1.235 1.467 1.789 3.584.934 6.148a1 1 0 0 1-1.898-.632c.646-1.936.2-3.319-.566-4.227A4.24 4.24 0 0 0 13 6.158V16.5a1 1 0 1 1-2 0z\" clip-rule=\"evenodd\"/></g>"}, "music-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 18a2 2 0 1 1-4 0a2 2 0 0 1 4 0m0 0V6.333L18 4v11.667M8 10.333L18 8m0 8a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "music-solid": {"body": "<path fill=\"currentColor\" d=\"M18.622 3.217A1 1 0 0 1 19 4v11.667q0 .06-.007.121q.007.105.007.212a3 3 0 1 1-2-2.83V9.26l-8 1.867v6.876a3 3 0 1 1-2-2.832V6.333a1 1 0 0 1 .773-.974l10-2.333a1 1 0 0 1 .842.186z\"/>"}, "newspaper-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7h1v12a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h11.5M7 14h6m-6 3h6m0-10h.5m-.5 3h.5M7 7h3v3H7z\"/>"}, "newspaper-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h11.5q.106 0 .207-.021Q16.85 21 17 21h2a2 2 0 0 0 2-2V7a1 1 0 0 0-1-1h-1a1 1 0 1 0 0 2v11h-2V5a2 2 0 0 0-2-2zm7 4a1 1 0 0 1 1-1h.5a1 1 0 1 1 0 2H13a1 1 0 0 1-1-1m0 3a1 1 0 0 1 1-1h.5a1 1 0 1 1 0 2H13a1 1 0 0 1-1-1m-6 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1m0 3a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1M7 6a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zm1 3V8h1v1z\" clip-rule=\"evenodd\"/>"}, "newspapper-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7h1v12c0 .6-.4 1-1 1h-2a1 1 0 0 1-1-1V5c0-.6-.4-1-1-1H5a1 1 0 0 0-1 1v14c0 .6.4 1 1 1h11.5M7 14h6m-6 3h6m0-10h.5m-.5 3h.5M7 7h3v3H7z\"/>", "hidden": true}, "newspapper-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V7c0-.6-.4-1-1-1h-1a1 1 0 1 0 0 2v11h-2V5a2 2 0 0 0-2-2zm7 4c0-.6.4-1 1-1h.5a1 1 0 1 1 0 2H13a1 1 0 0 1-1-1m0 3c0-.6.4-1 1-1h.5a1 1 0 1 1 0 2H13a1 1 0 0 1-1-1m-6 4c0-.6.4-1 1-1h6a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1m0 3c0-.6.4-1 1-1h6a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1M7 6a1 1 0 0 0-1 1v3c0 .6.4 1 1 1h3c.6 0 1-.4 1-1V7c0-.6-.4-1-1-1zm1 3V8h1v1z\" clip-rule=\"evenodd\"/>", "hidden": true}, "npm-solid": {"body": "<path fill=\"currentColor\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 3.87H4v16h8v-13h5v13h3v-16z\"/>"}, "objects-column-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1zm16 14a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1zM4 13a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1zm16-2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1z\"/>"}, "objects-column-solid": {"body": "<path fill=\"currentColor\" d=\"M5 3a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm14 18a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2zM5 11a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2zm14 2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2z\"/>"}, "open-door-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 18V6h-5v12zm0 0h2M4 18h2.5m3.5-5.5V12M6 6l7-2v16l-7-2z\"/>"}, "open-door-solid": {"body": "<g fill=\"currentColor\"><path d=\"M14 19V5h4a1 1 0 0 1 1 1v11h1a1 1 0 0 1 0 2z\"/><path fill-rule=\"evenodd\" d=\"M12 4.571a1 1 0 0 0-1.275-.961l-5 1.428A1 1 0 0 0 5 6v11H4a1 1 0 0 0 0 2h1.86l4.865 1.39A1 1 0 0 0 12 19.43zM10 11a1 1 0 0 1 1 1v.5a1 1 0 0 1-2 0V12a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/></g>"}, "open-sidebar-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7 10l2 2l-2 2m5-9v14M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1\"/>"}, "open-sidebar-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13 21h6a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-6z\"/><path fill-rule=\"evenodd\" d=\"M11 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6zm-5.707 7.707a1 1 0 0 1 1.414-1.414l2 2a1 1 0 0 1 0 1.414l-2 2a1 1 0 0 1-1.414-1.414L6.586 12z\" clip-rule=\"evenodd\"/></g>"}, "open-sidebar-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m6 10l2 2l-2 2m5-9v14m-7 0h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "open-sidebar-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6zM4.379 9.293a1 1 0 0 0 0 1.414L5.672 12l-1.293 1.293a1 1 0 1 0 1.414 1.414l2-2a1 1 0 0 0 0-1.414l-2-2a1 1 0 0 0-1.414 0\" clip-rule=\"evenodd\"/><path d=\"M12 20h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-8z\"/></g>"}, "ordered-list-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6h8m-8 6h8m-8 6h8M4 16a2 2 0 1 1 3.321 1.5L4 20h5M4 5l2-1v6m-2 0h4\"/>"}, "ordored-list-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6h8m-8 6h8m-8 6h8M4 16a2 2 0 1 1 3.3 1.5L4 20h5M4 5l2-1v6m-2 0h4\"/>", "hidden": true}, "ordored-list-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6.5 3.1c.*******.5.9v5h1a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2h1V5.6l-.6.3A1 1 0 0 1 3.6 4l2-1a1 1 0 0 1 1 0ZM11 6c0-.6.4-1 1-1h8a1 1 0 1 1 0 2h-8a1 1 0 0 1-1-1m0 6c0-.6.4-1 1-1h8a1 1 0 1 1 0 2h-8a1 1 0 0 1-1-1m-4.6 3A1 1 0 0 0 5 16a1 1 0 0 1-2 0a3 3 0 1 1 5 2.3L7 19h2a1 1 0 1 1 0 2H4a1 1 0 0 1-.6-1.8l3.3-2.5a1 1 0 0 0-.3-1.6Zm4.6 3c0-.6.4-1 1-1h8a1 1 0 1 1 0 2h-8a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>", "hidden": true}, "outdent-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 6h12M6 18h12m-5-8h5m-5 4h5M9.5 9v6L6 12z\"/>"}, "outdent-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 6a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m0 12a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1m3.85-9.76A1 1 0 0 1 10.5 9v6a1 1 0 0 1-1.65.76l-3.5-3a1 1 0 0 1 0-1.52zM12 10a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "outgoing-call-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m17.09 13.371l1.143 1.144a1.56 1.56 0 0 1 .41 1.852c-.095.207-.472.625-.646.771c-3.118 3.122-6.774 1.706-9.905-1.425s-4.543-6.79-1.41-9.901c.626-.62 1.713-1.142 2.618-.237l1.19 1.192c1.192 1.192.357 1.936-.492 2.804c-.925.925-.656 1.728 0 2.384l1.871 1.873a1.684 1.684 0 0 0 2.382 0c.863-.827 1.696-1.6 2.839-.457M14 8.981L19.023 4m0 0h-3.03m3.03 0v2.981\"/>"}, "outgoing-call-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6.978 4a2.55 2.55 0 0 0-1.926.877C3.233 6.7 2.699 8.751 3.153 10.814c.44 1.995 1.778 3.893 3.456 5.572c1.68 1.679 3.577 3.018 5.57 3.459c2.062.456 4.115-.073 5.94-1.885a2.556 2.556 0 0 0 .001-3.861l-1.21-1.21a2.69 2.69 0 0 0-3.802 0l-.617.618a.806.806 0 0 1-1.14 0l-1.854-1.855a.807.807 0 0 1 0-1.14l.618-.62a2.69 2.69 0 0 0 0-3.804l-1.21-1.21A2.56 2.56 0 0 0 6.978 4\"/><path fill-rule=\"evenodd\" d=\"M14.993 4a1 1 0 0 1 1-1h3.03a1 1 0 0 1 1 1v2.981a1 1 0 0 1-2 0v-.58l-3.319 3.29a1 1 0 0 1-1.408-1.42L16.594 5h-.602a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/></g>"}, "palette-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 7h.01m3.486 1.513h.01m-6.978 0h.01M6.99 12H7m9 4h2.706a1.96 1.96 0 0 0 1.883-1.325A9 9 0 1 0 3.043 12.89A9.1 9.1 0 0 0 8.2 20.1a8.6 8.6 0 0 0 3.769.9a2.013 2.013 0 0 0 2.03-2v-.857A2.036 2.036 0 0 1 16 16\"/>"}, "palette-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.649 2.577A10.004 10.004 0 0 1 20.344 6.49a10 10 0 0 1 1.2 8.486l-.004.01l-.005.015a2.96 2.96 0 0 1-2.836 2.001h-2.69a1.04 1.04 0 0 0-.95.68c-.047.13-.068.27-.06.409v.916A3.01 3.01 0 0 1 11.96 22a9.6 9.6 0 0 1-4.195-1l.009.005l-.018-.009l.01.004a10.1 10.1 0 0 1-5.716-7.996l-.001-.012a9.99 9.99 0 0 1 6.6-10.415m3.35 3.429a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM8.53 7.518a1 1 0 0 0 0 2h.01a1 1 0 1 0 0-2zm6.968 0a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM6.99 11.004a1 1 0 1 0 0 2H7a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "paper-clip-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 8v8a5 5 0 1 0 10 0V6.5a3.5 3.5 0 1 0-7 0V15a2 2 0 0 0 4 0V8\"/>"}, "paper-plane-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m12 18l-7 3l7-18l7 18zm0 0v-5\"/>"}, "paper-plane-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2a1 1 0 0 1 .932.638l7 18a1 1 0 0 1-1.326 1.281L13 19.517V13a1 1 0 1 0-2 0v6.517l-5.606 2.402a1 1 0 0 1-1.326-1.281l7-18A1 1 0 0 1 12 2\" clip-rule=\"evenodd\"/>"}, "papper-plane-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2c.4 0 .8.3 1 .6l7 18a1 1 0 0 1-1.4 1.3L13 19.5V13a1 1 0 1 0-2 0v6.5L5.4 22A1 1 0 0 1 4 20.6l7-18a1 1 0 0 1 1-.6\" clip-rule=\"evenodd\"/>", "hidden": true}, "paragraph-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5v7m0 7v-7m4-7v14m3-14H8.5A3.5 3.5 0 0 0 5 8.5v0A3.5 3.5 0 0 0 8.5 12H12\"/>"}, "paragraph-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.5 4a4.5 4.5 0 0 0 0 9H11v6a1 1 0 1 0 2 0V6h2v13a1 1 0 1 0 2 0V6h2a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "pause-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 6H8a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1m7 0h-1a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1\"/>"}, "pause-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2zm7 0a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z\" clip-rule=\"evenodd\"/>"}, "pen-nib-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m4.988 19.012l5.41-5.41m2.366-6.424l4.058 4.058l-2.03 5.41L5.3 20L4 18.701l3.355-9.494l5.41-2.029Zm4.626 4.625L12.197 6.61L14.807 4L20 9.194z\"/>"}, "pen-nib-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15.514 3.293a1 1 0 0 0-1.415 0L12.151 5.24l.056.052l6.5 6.5l.052.056L20.707 9.9a1 1 0 0 0 0-1.415l-5.193-5.193ZM7.004 8.27l3.892-1.46l6.293 6.293l-1.46 3.893a1 1 0 0 1-.603.591l-9.494 3.355a1 1 0 0 1-.98-.18l6.452-6.453a1 1 0 0 0-1.414-1.414l-6.453 6.452a1 1 0 0 1-.18-.98l3.355-9.494a1 1 0 0 1 .591-.603Z\" clip-rule=\"evenodd\"/>"}, "pen-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.779 17.779L4.36 19.918L6.5 13.5m4.279 4.279l8.364-8.643a3.027 3.027 0 0 0-2.14-5.165a3.03 3.03 0 0 0-2.14.886L6.5 13.5m4.279 4.279L6.499 13.5m2.14 2.14l6.213-6.504M12.75 7.04L17 11.28\"/>"}, "pen-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14 4.182A4.14 4.14 0 0 1 16.9 3c1.087 0 2.13.425 2.899 1.182A4 4 0 0 1 21 7.037c0 1.068-.43 2.092-1.194 2.849L18.5 11.214l-5.8-5.71l1.287-1.31l.012-.012Zm-2.717 2.763L6.186 12.13l2.175 2.141l5.063-5.218zm-6.25 6.886l-1.98 5.849a.99.99 0 0 0 .245 1.026a1.03 1.03 0 0 0 1.043.242L10.282 19l-5.25-5.168Zm6.954 4.01l5.096-5.186l-2.218-2.183l-5.063 5.218l2.185 2.15Z\" clip-rule=\"evenodd\"/>"}, "pepper-hot-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 11c1.5-3.033 3.102-3.847 4.5-3.5s2.286.99 2.678 2.607c.463 2.116-1.743 4.977-5.838 8.208c-1.206.913-3.494 2.017-5.05 2.7c-.698.293-1.595.053-2.099-.632c-.503-.684-.391-1.616.247-2.2c.393-.445.753-.76 1.112-1.076C8.185 15.62 9.5 14.033 11 11m0 0h3v2h3m.158-5.485l1.259-2.135c.462-.7.156-1.607-.545-2.07c-.7-.462-1.607-.156-2.07.545l-.08.047\"/>"}, "pepper-hot-solid": {"body": "<path fill=\"currentColor\" d=\"M15.108 3.027c.732-.919 2.119-1.426 3.315-.636c1.03.68 1.664 2.159.844 3.43l-1.135 1.925c.49.538.818 1.217 1.017 2.04l.003.011l.002.01c.158.722.087 1.458-.15 2.193H14.5v-2a1 1 0 0 0-1-1h-2.56q.834-1.258 1.77-1.919c.984-.695 2.03-.885 3.031-.636q.362.089.71.214l1.104-1.872l.027-.043a.34.34 0 0 0 .037-.298a.72.72 0 0 0-.298-.386a.34.34 0 0 0-.298-.037a.72.72 0 0 0-.386.298a1 1 0 0 1-.334.315l-.081.047a1 1 0 0 1-1.114-1.656M9.837 11c-1.333 2.577-2.513 3.966-3.953 5.276l-.004.004c-.348.306-.736.646-1.16 1.119c-.955.913-1.147 2.388-.334 3.492c.763 1.037 2.146 1.443 3.292.96l.012-.006c1.55-.679 3.941-1.824 5.254-2.818l.008-.006l.008-.006c2.08-1.642 3.735-3.23 4.817-4.72q.106-.146.208-.295H13.5a1 1 0 0 1-1-1v-2z\"/>"}, "person-chalkboard-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.571 5h7v9h-7m-6-4l-3 4.5m3-4.5v5m0-5h3m0 0h5m-5 0v5m-3 0h3m-3 0v5m3-5v5m6-6l2.5 6m-3-6l-2.5 6m-3-14.5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0\"/>"}, "person-chalkboard-solid": {"body": "<g fill=\"currentColor\"><path d=\"M16 10a1 1 0 0 0-1-1h-3v2h3a1 1 0 0 0 1-1\"/><path d=\"M13 15v-2h2a3 3 0 1 0 0-6h-2.256c.166-.47.256-.974.256-1.5c0-.515-.086-1.01-.245-1.47Q12.872 4 13 4h7a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-2.5l1.923 4.615a1 1 0 0 1-1.846.77L15.75 16l-1.827 4.385a1 1 0 0 1-1.089.601q.164-.465.166-.986v-2.6l1-2.4zM6 5.5a2.5 2.5 0 1 1 5 0a2.5 2.5 0 0 1-5 0\"/><path d=\"M15 11h-4v9a1 1 0 1 1-2 0v-4H8v4a1 1 0 1 1-2 0v-6.697l-1.168 1.752a1 1 0 0 1-1.664-1.11L6.16 9.457A1 1 0 0 1 7.017 9H15a1 1 0 1 1 0 2\"/></g>"}, "phone-hangup-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.693 16.013H7.31a1.685 1.685 0 0 0 1.685-1.684v-.645A1.684 1.684 0 0 1 10.679 12h2.647a1.686 1.686 0 0 1 1.686 1.686v.646c0 .446.178.875.494 1.19c.316.317.693.495 1.14.495h1.685a1.556 1.556 0 0 0 1.597-1.016c.078-.214.107-.776.088-1.002c.014-4.415-3.571-6.003-8-6.004c-4.427 0-8.014 1.585-8.01 5.996c-.02.227.009.79.087 1.003a1.56 1.56 0 0 0 1.6 1.02Z\"/>"}, "phone-hangup-solid": {"body": "<path fill=\"currentColor\" d=\"M12.017 6.995c-2.306 0-4.534.408-6.215 1.507c-1.737 1.135-2.788 2.944-2.797 5.451a5 5 0 0 0 .01.62c.015.193.047.512.138.763a2.56 2.56 0 0 0 2.579 1.677H7.31a2.685 2.685 0 0 0 2.685-2.684v-.645a.684.684 0 0 1 .684-.684h2.647a.686.686 0 0 1 .686.687v.645c0 .712.284 1.395.787 1.898c.478.478 1.101.787 1.847.787h1.647a2.555 2.555 0 0 0 2.575-1.674c.09-.25.123-.57.137-.763c.015-.2.022-.433.01-.617c-.002-2.508-1.049-4.32-2.785-5.458c-1.68-1.1-3.907-1.51-6.213-1.51\"/>"}, "phone-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.427 14.768L17.2 13.542a1.733 1.733 0 0 0-2.45 0l-.613.613a1.73 1.73 0 0 1-2.45 0l-1.838-1.84a1.735 1.735 0 0 1 0-2.452l.612-.613a1.735 1.735 0 0 0 0-2.452L9.237 5.572a1.6 1.6 0 0 0-2.45 0c-3.223 3.2-1.702 6.896 1.519 10.117s6.914 4.745 10.12 1.535a1.6 1.6 0 0 0 0-2.456Z\"/>"}, "phone-solid": {"body": "<path fill=\"currentColor\" d=\"M7.978 4a2.55 2.55 0 0 0-1.926.877C4.233 6.7 3.699 8.751 4.153 10.814c.44 1.995 1.778 3.893 3.456 5.572c1.68 1.679 3.577 3.018 5.57 3.459c2.062.456 4.115-.073 5.94-1.885a2.556 2.556 0 0 0 .001-3.861l-1.21-1.21a2.69 2.69 0 0 0-3.802 0l-.617.618a.806.806 0 0 1-1.14 0l-1.854-1.855a.807.807 0 0 1 0-1.14l.618-.62a2.69 2.69 0 0 0 0-3.803l-1.21-1.211A2.56 2.56 0 0 0 7.978 4\"/>"}, "pie-chart-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13.5 2a7 7 0 0 0-.5 0a1 1 0 0 0-1 1v8c0 .6.4 1 1 1h8c.5 0 1-.4 1-1v-.5A8.5 8.5 0 0 0 13.5 2\"/><path d=\"M11 6a1 1 0 0 0-1-1a8.5 8.5 0 1 0 9 9a1 1 0 0 0-1-1h-7z\"/></g>", "hidden": true}, "pizza-slice-outline": {"body": "<g fill=\"none\"><g clip-path=\"url(#flowbitePizzaSliceOutline0)\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.415 10.762v.011m3.087 3.077v.01m-4.585 1.49v.01m-.828-8.445l-3.69 13l12.952-3.738m-.827-8.435a12.14 12.14 0 0 1 3.517 7.517c.067.741-.462 1.375-1.187 1.53c-1.014.215-1.96-.582-2.092-1.616c-.241-1.9-.944-3.572-2.52-5.148c-1.578-1.578-3.318-2.346-5.253-2.62c-1.022-.144-1.796-1.11-1.539-2.103c.172-.667.768-1.142 1.458-1.085a12.14 12.14 0 0 1 7.616 3.525\"/></g><defs><clipPath id=\"flowbitePizzaSliceOutline0\"/></defs></g>"}, "pizza-slice-solid": {"body": "<g fill=\"currentColor\"><path d=\"M8.988 3.219a13.14 13.14 0 0 1 8.242 3.815a13.14 13.14 0 0 1 3.806 8.134c.118 1.306-.817 2.351-1.975 2.597c-.378.08-.742.077-1.082.005a43 43 0 0 0-.226-2.724c-.267-2.1-1.058-3.982-2.805-5.73c-1.743-1.742-3.687-2.6-5.82-2.902a51 51 0 0 0-2.692-.268a2.55 2.55 0 0 1 .045-1.095c.28-1.083 1.278-1.933 2.507-1.832\"/><path fill-rule=\"evenodd\" d=\"M15.769 15.298c.079.621.148 1.492.197 2.318l-11.29 3.258a1 1 0 0 1-1.24-1.234L6.692 8.172c.89.073 1.79.17 2.156.222c1.737.246 3.273.924 4.686 2.337c1.405 1.405 2.019 2.866 2.235 4.567m-4.355-4.579a1 1 0 1 0-1.998.086v.011a1 1 0 1 0 1.998-.086zm3.087 3.088a1 1 0 1 0-1.998.086v.01a1 1 0 1 0 1.999-.086zm-4.587 1.499a1 1 0 0 0-1.998.086v.01a1 1 0 0 0 1.999-.085z\" clip-rule=\"evenodd\"/></g>"}, "plate-wheat-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 5h1m-1 6h1M3 8h4m8.5 0c-.102.11-1.538-1.157-1.796-1.482c-.33-.415-.782-1.43-.698-1.506s1.116.68 1.374 1.006c.258.324 1.222 1.871 1.12 1.982m0 0c-.102-.11-1.538 1.157-1.796 1.482c-.33.415-.782 1.43-.698 1.506s1.116-.68 1.374-1.005S15.602 8.11 15.5 8m4 0c-.102.11-1.538-1.157-1.796-1.482c-.33-.415-.782-1.43-.698-1.506s1.116.68 1.374 1.006c.258.324 1.222 1.871 1.12 1.982m0 0c-.102-.11-1.538 1.157-1.796 1.482c-.33.415-.782 1.43-.698 1.506s1.116-.68 1.374-1.005S19.602 8.11 19.5 8M11 8h10m-9.994 0c-.102.11-1.539-1.157-1.797-1.482c-.33-.415-.781-1.43-.697-1.506s1.116.68 1.374 1.005S11.107 7.89 11.006 8m0 0c-.102-.11-1.539 1.157-1.797 1.482c-.33.415-.781 1.43-.697 1.506s1.116-.68 1.374-1.005S11.107 8.11 11.006 8M8.599 18.012V19H15.4v-.988c2.99-.667 5.187-2.187 5.6-4.012H3c.413 1.825 2.61 3.345 5.599 4.012\"/>"}, "plate-wheat-solid": {"body": "<g fill=\"currentColor\"><path d=\"M5 4a1 1 0 1 0 0 2h1a1 1 0 1 0 0-2zm5.669 1.395c-.222-.278-.645-.618-.944-.837a6 6 0 0 0-.497-.332a2 2 0 0 0-.286-.14a1.2 1.2 0 0 0-.285-.072a1.03 1.03 0 0 0-.816.256a1.04 1.04 0 0 0-.333.663a1.2 1.2 0 0 0 .006.285c.016.119.048.229.072.305c.052.165.129.352.21.53c.154.339.39.784.63 1.087c.165.207.533.555.88.86c-.347.305-.715.653-.88.86c-.24.303-.476.748-.63 1.088a5 5 0 0 0-.21.529a2 2 0 0 0-.072.305c-.008.056-.02.16-.006.285c.01.09.05.407.333.663c.343.31.738.265.816.256c.13-.016.234-.052.285-.072c.111-.043.214-.098.286-.14c.155-.09.332-.21.497-.332c.299-.219.722-.558.944-.837c.175-.22.51-.74.773-1.186c.082-.139.164-.282.238-.419h1.136a6.3 6.3 0 0 0-.736 1.477a2 2 0 0 0-.072.305c-.008.056-.019.16-.006.285c.01.09.05.407.333.663c.343.31.739.265.816.256c.13-.016.234-.052.285-.072c.112-.043.214-.099.286-.14c.155-.09.332-.21.497-.332c.3-.219.723-.558.944-.837c.176-.221.51-.74.773-1.186c.082-.139.164-.282.238-.419h.642a6.3 6.3 0 0 0-.736 1.477a2 2 0 0 0-.072.305c-.008.056-.019.16-.006.285c.01.09.05.407.334.663c.342.31.738.265.815.256c.13-.016.234-.052.285-.072c.112-.043.214-.099.286-.14c.155-.09.332-.21.497-.332c.3-.219.723-.558.944-.837c.176-.221.51-.74.773-1.186c.082-.139.164-.282.238-.419H21a1 1 0 0 0 0-2h-.826c-.074-.137-.156-.28-.238-.42c-.263-.444-.597-.964-.773-1.185c-.221-.278-.645-.618-.944-.837a6 6 0 0 0-.497-.332a2 2 0 0 0-.286-.14a1.2 1.2 0 0 0-.285-.072a1.03 1.03 0 0 0-.816.256a1.04 1.04 0 0 0-.333.663a1.2 1.2 0 0 0 .006.285c.016.119.048.229.072.305A6.3 6.3 0 0 0 16.816 7h-.642c-.074-.137-.156-.28-.238-.42c-.263-.444-.597-.964-.773-1.185c-.221-.278-.645-.618-.944-.837a6 6 0 0 0-.497-.332a2 2 0 0 0-.286-.14a1.2 1.2 0 0 0-.285-.072a1.03 1.03 0 0 0-.816.256a1.04 1.04 0 0 0-.333.663a1.2 1.2 0 0 0 .006.285c.016.119.048.229.072.305A6.3 6.3 0 0 0 12.816 7H11.68c-.074-.137-.156-.28-.238-.42c-.263-.444-.598-.964-.773-1.185\"/><path d=\"M3 7a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2zm2 3a1 1 0 1 0 0 2h1a1 1 0 1 0 0-2zm-2 3a1 1 0 0 0-.975 1.22c.278 1.232 1.13 2.256 2.23 3.036c.921.654 2.066 1.173 3.344 1.534V19a1 1 0 0 0 1 1H15.4a1 1 0 0 0 1-1v-.21c1.278-.36 2.423-.88 3.344-1.534c1.1-.78 1.952-1.804 2.23-3.035A1 1 0 0 0 21 13z\"/></g>"}, "play-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 18V6l8 6z\"/>"}, "play-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6z\" clip-rule=\"evenodd\"/>"}, "plus-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12h14m-7 7V5\"/>"}, "printer-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.444 18H19a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h2.556M17 11V5a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v6zM7 15h10v4a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1z\"/>"}, "printer-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 3a2 2 0 0 0-2 2v3h12V5a2 2 0 0 0-2-2zm-3 7a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h1v-4a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v4h1a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2zm4 11a1 1 0 0 1-1-1v-4h8v4a1 1 0 0 1-1 1z\" clip-rule=\"evenodd\"/>"}, "profile-card-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 9h3m-3 3h3m-3 3h3m-6 1c-.306-.613-.933-1-1.618-1H7.618c-.685 0-1.312.387-1.618 1M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1m7 5a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "profile-card-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm10 5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1m0 3a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1m0 3a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1m-8-5a3 3 0 1 1 6 0a3 3 0 0 1-6 0m1.942 4a3 3 0 0 0-2.847 2.051l-.044.133l-.004.012c-.042.126-.055.167-.042.195c.**************.038.039c.**************.146.155A1 1 0 0 0 6 17h6a1 1 0 0 0 .811-.415a.7.7 0 0 1 .146-.155c.019-.016.031-.026.038-.04c.014-.027 0-.068-.042-.194l-.004-.012l-.044-.133A3 3 0 0 0 10.059 14z\" clip-rule=\"evenodd\"/>"}, "qr-code-outline": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M4 4h6v6H4zm10 10h6v6h-6zm0-10h6v6h-6zm-4 10h.01v.01H10zm0 4h.01v.01H10zm-3 2h.01v.01H7zm0-4h.01v.01H7zm-3 2h.01v.01H4zm0-4h.01v.01H4z\"/><path d=\"M7 7h.01v.01H7zm10 10h.01v.01H17z\"/></g>"}, "question-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.529 9.988a2.502 2.502 0 1 1 5 .191A2.44 2.44 0 0 1 12 12.582V14m-.01 3.008H12M21 12a9 9 0 1 1-18 0a9 9 0 0 1 18 0\"/>"}, "question-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m9.008-3.018a1.502 1.502 0 0 1 2.522 1.159v.024a1.44 1.44 0 0 1-1.493 1.418a1 1 0 0 0-1.037.999V14a1 1 0 1 0 2 0v-.539a3.44 3.44 0 0 0 2.529-3.256a3.502 3.502 0 0 0-7-.255a1 1 0 0 0 2 .076c.014-.398.187-.774.48-1.044Zm.982 7.026a1 1 0 1 0 0 2H12a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "quote-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 11V8a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1m0 0v2a4 4 0 0 1-4 4H5m14-6V8a1 1 0 0 0-1-1h-3a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1m0 0v2a4 4 0 0 1-4 4h-1\"/>"}, "quote-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 6a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2zm9 0a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2z\" clip-rule=\"evenodd\"/>"}, "react-solid": {"body": "<g fill=\"currentColor\"><path d=\"M21.718 12c0-1.429-1.339-2.681-3.467-3.5c.029-.18.077-.37.1-.545c.217-2.058-.273-3.543-1.379-4.182c-1.235-.714-2.983-.186-4.751 1.239C10.45 3.589 8.7 3.061 7.468 3.773c-1.107.639-1.6 2.124-1.379 4.182c.018.175.067.365.095.545c-2.127.819-3.466 2.071-3.466 3.5s1.339 2.681 3.466 3.5c-.028.18-.077.37-.095.545c-.218 2.058.272 3.543 1.379 4.182c.376.213.803.322 1.235.316a6 6 0 0 0 3.514-1.56a6 6 0 0 0 3.515 1.56a2.44 2.44 0 0 0 1.236-.316c1.106-.639 1.6-2.124 1.379-4.182c-.019-.175-.067-.365-.1-.545c2.132-.819 3.471-2.071 3.471-3.5m-6.01-7.548a1.5 1.5 0 0 1 .76.187c.733.424 1.055 1.593.884 3.212c-.012.106-.043.222-.058.33q-1.263-.365-2.57-.523a16 16 0 0 0-1.747-1.972a4.9 4.9 0 0 1 2.731-1.234m-7.917 8.781c.172.34.335.68.529 1.017s.395.656.6.969a14 14 0 0 1-1.607-.376a14 14 0 0 1 .478-1.61m-.479-4.076a14 14 0 0 1 1.607-.376q-.308.468-.6.969c-.195.335-.357.677-.529 1.017q-.286-.79-.478-1.61M8.3 12a19 19 0 0 1 .888-1.75q.496-.852 1.076-1.65c.619-.061 1.27-.1 1.954-.1q1.025.001 1.952.1a20 20 0 0 1 1.079 1.654q.488.851.887 1.746a19 19 0 0 1-1.953 3.403a19.2 19.2 0 0 1-3.931 0a20 20 0 0 1-1.066-1.653A19 19 0 0 1 8.3 12m7.816 2.25c.2-.337.358-.677.53-1.017q.286.791.478 1.611a15 15 0 0 1-1.607.376c.202-.314.404-.635.597-.97zm.53-3.483c-.172-.34-.335-.68-.53-1.017a20 20 0 0 0-.6-.97q.814.142 1.606.376a14 14 0 0 1-.478 1.611zM12.217 6.34q.6.563 1.13 1.193q-.555-.031-1.129-.033c-.574-.002-.76.013-1.131.033q.53-.63 1.13-1.193m-4.249-1.7a1.5 1.5 0 0 1 .76-.187a4.9 4.9 0 0 1 2.729 1.233A16 16 0 0 0 9.71 7.658q-1.306.158-2.569.524c-.015-.109-.047-.225-.058-.331c-.171-1.619.151-2.787.885-3.211M3.718 12c0-.9.974-1.83 2.645-2.506c.218.857.504 1.695.856 2.506c-.352.811-.638 1.65-.856 2.506C4.692 13.83 3.718 12.9 3.718 12m4.25 7.361c-.734-.423-1.056-1.593-.885-3.212c.011-.106.043-.222.058-.331q1.262.365 2.564.524a16.4 16.4 0 0 0 1.757 1.982c-1.421 1.109-2.714 1.488-3.494 1.037m3.11-2.895q.56.033 1.14.034q.58-.001 1.139-.034a14 14 0 0 1-1.14 1.215a14 14 0 0 1-1.139-1.215m5.39 2.895c-.782.451-2.075.072-3.5-1.038a16 16 0 0 0 1.757-1.981a16.4 16.4 0 0 0 2.565-.523c.***************.058.33c.175 1.619-.148 2.789-.88 3.212m1.6-4.854A16.6 16.6 0 0 0 17.216 12q.529-1.22.856-2.507c1.671.677 2.646 1.607 2.646 2.507s-.975 1.83-2.646 2.507z\"/><path d=\"M12.215 13.773a1.792 1.792 0 1 0-1.786-1.8v.006a1.787 1.787 0 0 0 1.786 1.794\"/></g>"}, "receipt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 8h6m-6 4h6m-6 4h6M6 3v18l2-2l2 2l2-2l2 2l2-2l2 2V3l-2 2l-2-2l-2 2l-2-2l-2 2z\"/>"}, "receipt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.617 2.076a1 1 0 0 1 1.09.217L8 3.586l1.293-1.293a1 1 0 0 1 1.414 0L12 3.586l1.293-1.293a1 1 0 0 1 1.414 0L16 3.586l1.293-1.293A1 1 0 0 1 19 3v18a1 1 0 0 1-1.707.707L16 20.414l-1.293 1.293a1 1 0 0 1-1.414 0L12 20.414l-1.293 1.293a1 1 0 0 1-1.414 0L8 20.414l-1.293 1.293A1 1 0 0 1 5 21V3a1 1 0 0 1 .617-.924M9 7a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "rectangle-list-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 9h6m-6 3h6m-6 3h6M6.996 9h.01m-.01 3h.01m-.01 3h.01M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1\"/>"}, "rectangle-list-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm4.996 2a1 1 0 0 0 0 2h.01a1 1 0 1 0 0-2zM11 8a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2zm-4.004 3a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM11 11a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2zm-4.004 3a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM11 14a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "reddit-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12.008 16.521a3.84 3.84 0 0 0 2.47-.77v.04a.28.28 0 0 0 .005-.396a.28.28 0 0 0-.395-.005a3.3 3.3 0 0 1-2.09.61a3.27 3.27 0 0 1-2.081-.63a.27.27 0 0 0-.38.381a3.84 3.84 0 0 0 2.47.77Z\"/><path fill-rule=\"evenodd\" d=\"M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10m-4.845-1.407A1.463 1.463 0 0 1 18.67 12a1.46 1.46 0 0 1-.808 1.33q.015.22 0 .44c0 2.242-2.61 4.061-5.829 4.061s-5.83-1.821-5.83-4.061a3 3 0 0 1 0-.44a1.458 1.458 0 0 1-.457-2.327a1.46 1.46 0 0 1 2.063-.064a7.16 7.16 0 0 1 3.9-1.23l.738-3.47v-.006a.31.31 0 0 1 .37-.236l2.452.49a1 1 0 1 1-.132.611l-2.14-.45l-.649 3.12a7.1 7.1 0 0 1 3.85 1.23c.259-.246.6-.393.957-.405\" clip-rule=\"evenodd\"/><path d=\"M15.305 13a1 1 0 1 1-2 0a1 1 0 0 1 2 0m-4.625 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0\"/></g>"}, "redo-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 9H8a5 5 0 0 0 0 10h9m4-10l-4-4m4 4l-4 4\"/>"}, "redo-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M4 9h12a5 5 0 0 1 5 5v0a5 5 0 0 1-5 5H7\"/><path stroke-linejoin=\"round\" d=\"M7 5L3 9l4 4\"/></g>", "hidden": true}, "refresh-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4\"/>"}, "reply-all-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.757 6L3.24 10.95a1.05 1.05 0 0 0 0 1.549l5.611 5.088m5.73-3.214v1.615a.948.948 0 0 1-1.524.845l-5.108-4.251a1.1 1.1 0 0 1 0-1.646l5.108-4.251a.95.95 0 0 1 1.524.846v1.7c3.312 0 6 2.979 6 6.654v1.329a.7.7 0 0 1-1.345.353a5.17 5.17 0 0 0-4.652-3.191z\"/>"}, "reply-all-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9.484 6.743c.41-.368.443-1 .077-1.41a.99.99 0 0 0-1.405-.078L2.67 10.203l-.007.006A2.05 2.05 0 0 0 2 11.721a2.06 2.06 0 0 0 .662 1.51l5.584 5.09a.99.99 0 0 0 1.405-.07a1.003 1.003 0 0 0-.07-1.412l-5.577-5.082a.05.05 0 0 1 0-.072zm6.543 9.199v-.42a4.17 4.17 0 0 1 2.715 2.415c.154.382.44.695.806.88a1.683 1.683 0 0 0 2.167-.571c.214-.322.312-.707.279-1.092V15.88c0-3.77-2.526-7.039-5.966-7.573V7.57a1.96 1.96 0 0 0-.994-1.838a1.93 1.93 0 0 0-2.153.184L7.8 10.164l-.012.011l-.011.01a2.1 2.1 0 0 0-.703 1.57a2.1 2.1 0 0 0 .726 1.59l5.08 4.25a1.933 1.933 0 0 0 2.929-.614c.167-.32.242-.68.218-1.04Z\" clip-rule=\"evenodd\"/>"}, "reply-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.5 8.046H11V6.119c0-.921-.9-1.446-1.524-.894l-5.108 4.49a1.2 1.2 0 0 0 0 1.739l5.108 4.49c.624.556 1.524.027 1.524-.893v-1.928h2a3.023 3.023 0 0 1 3 3.046V19a5.593 5.593 0 0 0-1.5-10.954\"/>"}, "reply-solid": {"body": "<path fill=\"currentColor\" d=\"M14.502 7.046h-2.5v-.928a2.12 2.12 0 0 0-1.199-1.954a1.83 1.83 0 0 0-1.984.311L3.71 8.965a2.2 2.2 0 0 0 0 3.24L8.82 16.7a1.83 1.83 0 0 0 1.985.31a2.12 2.12 0 0 0 1.199-1.959v-.928h1a2.025 2.025 0 0 1 1.999 2.047V19a1 1 0 0 0 1.275.961a6.59 6.59 0 0 0 4.662-7.22a6.59 6.59 0 0 0-6.437-5.695Z\"/>"}, "restore-window-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 11.5h13m-13 0V18a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-6.5m-13 0V9a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v2.5M9 5h11a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-1\"/>"}, "rocket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m10.051 8.102l-3.778.322l-1.994 1.994a.94.94 0 0 0 .533 1.6l2.698.316m8.39 1.617l-.322 3.78l-1.994 1.994a.94.94 0 0 1-1.595-.533l-.4-2.652m8.166-11.174a1.37 1.37 0 0 0-1.12-1.12c-1.616-.279-4.906-.623-6.38.853c-1.671 1.672-5.211 8.015-6.31 10.023a.93.93 0 0 0 .162 1.111l.828.835l.833.832a.93.93 0 0 0 1.111.163c2.008-1.102 8.35-4.642 10.021-6.312c1.475-1.478 1.133-4.77.855-6.385m-2.961 3.722a1.88 1.88 0 1 1-3.76 0a1.88 1.88 0 0 1 3.76 0\"/>"}, "rocket-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M20.337 3.664c.213.212.354.486.404.782c.294 1.711.657 5.195-.906 6.76c-1.77 1.768-8.485 5.517-10.611 6.683a.99.99 0 0 1-1.176-.173l-.882-.88l-.877-.884a.99.99 0 0 1-.173-1.177c1.165-2.126 4.913-8.841 6.682-10.611c1.562-1.563 5.046-1.198 6.757-.904c.296.05.57.191.782.404M5.407 7.576l4-.341l-2.69 4.48l-2.857-.334a.996.996 0 0 1-.565-1.694zm11.357 7.02l-.34 4l-2.111 2.113a.996.996 0 0 1-1.69-.565l-.422-2.807zm.84-6.21a1.99 1.99 0 1 1-3.98 0a1.99 1.99 0 0 1 3.98 0\" clip-rule=\"evenodd\"/>"}, "rotate-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M17.7 7.7A7.1 7.1 0 0 0 5 10.8\"/><path d=\"M18 4v4h-4m-7.7 8.3A7.1 7.1 0 0 0 19 13.2\"/><path d=\"M6 20v-4h4\"/></g>", "hidden": true}, "ruler-combined-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 7H7m2 3H7m2 3H7m4 2v2m3-2v2m3-2v2M4 5v14a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-9a1 1 0 0 1-1-1V5a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1\"/>"}, "ruler-combined-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2h-1v3a1 1 0 1 1-2 0v-3h-1v3a1 1 0 1 1-2 0v-3h-1v3a1 1 0 1 1-2 0v-3H7a1 1 0 1 1 0-2h3v-1H7a1 1 0 1 1 0-2h3V8H7a1 1 0 0 1 0-2h3V5a2 2 0 0 0-2-2z\" clip-rule=\"evenodd\"/>"}, "sale-percent-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.891 15.107L15.11 8.89m-5.183-.52h.01m3.089 7.254h.01M14.08 3.902a2.85 2.85 0 0 0 2.176.902a2.845 2.845 0 0 1 2.94 2.94a2.85 2.85 0 0 0 .901 2.176a2.847 2.847 0 0 1 0 4.16a2.85 2.85 0 0 0-.901 2.175a2.843 2.843 0 0 1-2.94 2.94a2.85 2.85 0 0 0-2.176.902a2.847 2.847 0 0 1-4.16 0a2.85 2.85 0 0 0-2.176-.902a2.845 2.845 0 0 1-2.94-2.94a2.85 2.85 0 0 0-.901-2.176a2.85 2.85 0 0 1 0-4.16a2.85 2.85 0 0 0 .901-2.176a2.845 2.845 0 0 1 2.941-2.94a2.85 2.85 0 0 0 2.176-.901a2.847 2.847 0 0 1 4.159 0\"/>"}, "sale-percent-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M20.29 8.567c.133.323.334.613.59.85v.002a3.536 3.536 0 0 1 0 5.166a2.44 2.44 0 0 0-.776 1.868a3.534 3.534 0 0 1-3.651 3.653a2.48 2.48 0 0 0-1.87.776a3.537 3.537 0 0 1-5.164 0a2.44 2.44 0 0 0-1.87-.776a3.533 3.533 0 0 1-3.653-3.654a2.44 2.44 0 0 0-.775-1.868a3.537 3.537 0 0 1 0-5.166a2.44 2.44 0 0 0 .775-1.87a3.55 3.55 0 0 1 1.033-2.62a3.6 3.6 0 0 1 2.62-1.032a2.4 2.4 0 0 0 1.87-.775a3.535 3.535 0 0 1 5.165 0a2.44 2.44 0 0 0 1.869.775a3.53 3.53 0 0 1 3.652 3.652c-.012.35.051.697.184 1.02ZM9.927 7.371a1 1 0 1 0 0 2h.01a1 1 0 0 0 0-2zm5.889 2.226a1 1 0 0 0-1.414-1.415L8.184 14.4a1 1 0 0 0 1.414 1.414zm-2.79 5.028a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "scale-balanced-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.5 21h13M12 21V7m0 0a2 2 0 1 0 0-4a2 2 0 0 0 0 4m2-1.8c3.073.661 2.467 2.8 5 2.8M5 8c3.359 0 2.192-2.115 5.012-2.793M7 9.556V7.75m0 1.806l-1.95 4.393a.773.773 0 0 0 .37.962a.8.8 0 0 0 .362.089h2.436a.79.79 0 0 0 .643-.335a.78.78 0 0 0 .09-.716zm10 0V7.313m0 2.243l-1.95 4.393a.773.773 0 0 0 .37.962a.8.8 0 0 0 .362.089h2.436a.79.79 0 0 0 .643-.335a.78.78 0 0 0 .09-.716z\"/>"}, "scale-balanced-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 4a1 1 0 1 0 0 2a1 1 0 0 0 0-2m-2.952.462c-.483.19-.868.432-1.19.71c-.363.315-.638.677-.831.93l-.106.14c-.21.268-.36.418-.574.527C6.125 6.883 5.74 7 5 7a1 1 0 0 0 0 2q.545 0 1-.067v.41l-1.864 4.2a1.774 1.774 0 0 0 .821 2.255c.255.133.538.202.825.202h2.436a1.786 1.786 0 0 0 1.768-1.558a1.8 1.8 0 0 0-.122-.899L8 9.343V8.028c.2-.188.36-.38.495-.553q.093-.118.168-.217c.185-.24.311-.406.503-.571a2 2 0 0 1 .24-.177A3 3 0 0 0 11 7.829V20H5.5a1 1 0 1 0 0 2h13a1 1 0 1 0 0-2H13V7.83a3 3 0 0 0 1.63-1.387c.206.091.373.19.514.29c.31.219.532.465.811.78l.025.027l.02.023v1.78l-1.864 4.2a1.774 1.774 0 0 0 .821 2.255c.255.133.538.202.825.202h2.436a1.785 1.785 0 0 0 1.768-1.558a1.8 1.8 0 0 0-.122-.899L18 9.343v-.452q.451.108 1 .109a1 1 0 1 0 0-2c-.48 0-.731-.098-.899-.2c-.2-.12-.363-.293-.651-.617l-.024-.026c-.267-.3-.622-.7-1.127-1.057a5.2 5.2 0 0 0-1.355-.678a3.001 3.001 0 0 0-5.896.04\" clip-rule=\"evenodd\"/>"}, "school-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.143 11l5-6l5 6m-10 0h-3v8h16v-8h-3m-10 0h-4l3-4h4.337zm10 0h4l-3-4h-4.338zm-3 2a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/>"}, "school-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12 12a1 1 0 1 0 0 2a1 1 0 0 0 0-2\"/><path fill-rule=\"evenodd\" d=\"M6.651 11.63L12 5.048l5.349 6.584a1 1 0 0 0 .776.369H21v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-7h2.875a1 1 0 0 0 .776-.37M9 13a3 3 0 1 1 6 0a3 3 0 0 1-6 0\" clip-rule=\"evenodd\"/><path d=\"m2.5 10l2.7-3.6A1 1 0 0 1 6 6h2.649l-3.25 4zm12.851-4l3.25 4H21.5l-2.7-3.6A1 1 0 0 0 18 6z\"/></g>"}, "school-check-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.286 11l5-6l5 6m-10 0h-3v8h8m-5-8h-4l3-4h4.337zm10 0h3v1m-3-1h4l-3-4h-4.338zm-2 6l2 2l4-4m-7-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "school-check-alt-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M21.707 14.293a1 1 0 0 1 0 1.414l-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L17 17.586l3.293-3.293a1 1 0 0 1 1.414 0\" clip-rule=\"evenodd\"/><path d=\"M6.651 11.63L12 5.048l5.349 6.584a1 1 0 0 0 .776.369h2.866a3 3 0 0 0-2.112.879l-1.882 1.882a3 3 0 0 0-2.17-.756a3 3 0 1 0-2.655 1.99a3 3 0 0 0 .707 3.126l.878.879H4a1 1 0 0 1-1-1v-7h2.875a1 1 0 0 0 .776-.37\"/><path d=\"M12 12a1 1 0 1 0 0 2a1 1 0 0 0 0-2m-9.5-2l2.7-3.6A1 1 0 0 1 6 6h2.649l-3.25 4zm12.851-4l3.25 4H21.5l-2.7-3.6A1 1 0 0 0 18 6z\"/></g>"}, "school-check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.143 20v-9l-4 1.125V20zm0 0V6.667m0 13.333h3m5-9V6.667m0 4.333l4 1.125V13m-4-2v3m2-6l-6-4l-6 4m5 1h2m-2 3h2m1 6l2 2l4-4\"/>"}, "school-check-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M11.445 3.168a1 1 0 0 1 1.11 0l6 4a1 1 0 0 1-1.11 1.664L16 7.87v8.596l-.232-.233a2.5 2.5 0 0 0-3.536 3.536L13.465 21H8V7.869l-1.445.963a1 1 0 0 1-1.11-1.664zM11 11a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm-1-2a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/><path d=\"M21 13.708v-1.583a1 1 0 0 0-.73-.963L18 10.524v3.94l.232-.232A2.5 2.5 0 0 1 21 13.708M6 10.524l-2.27.638a1 1 0 0 0-.73.963V20a1 1 0 0 0 1 1h2z\"/><path fill-rule=\"evenodd\" d=\"M20.707 15.293a1 1 0 0 1 0 1.414l-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L16 18.586l3.293-3.293a1 1 0 0 1 1.414 0\" clip-rule=\"evenodd\"/></g>"}, "school-exclamation-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.5 19h-13v-8h3m0 0l5-6l5 6m-10 0h-4l3-4h4.337zm13 8h.01m-.01-3v-3m-6.337-6H18.5l2 2.5m-6 3.5a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "school-exclamation-alt-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M19 19a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H20a1 1 0 0 1-1-1m1-7a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/><path d=\"M11 13a1 1 0 1 1 2 0a1 1 0 0 1-2 0\"/><path fill-rule=\"evenodd\" d=\"m12 5.047l-5.349 6.584a1 1 0 0 1-.776.369H3v7a1 1 0 0 0 1 1h13.17a3.01 3.01 0 0 1 .231-2.5A3 3 0 0 1 17 16v-3c0-.499.122-.97.337-1.383zM12 10a3 3 0 1 0 0 6a3 3 0 0 0 0-6\" clip-rule=\"evenodd\"/><path d=\"M20.01 10H18.6l-3.25-4H18a1 1 0 0 1 .8.4l2.7 3.6zm-.016 2A1 1 0 0 0 19 13v3q0 .085.014.166A1 1 0 0 0 20 17h.01a1 1 0 0 0 .99-1v-3a1 1 0 0 0-.994-1zM21 18.858l-.004-.027A1 1 0 0 0 20.01 18H20a1 1 0 0 0 0 2h.006A1 1 0 0 0 21 19zM2.5 10l2.7-3.6A1 1 0 0 1 6 6h2.649l-3.25 4z\"/></g>"}, "school-exclamation-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.071 20v-9l-4 1.125V20zm0 0h8m-8 0V6.667m8 13.333v-9l1.5.422M16.071 20h1m-1 0V6.667m2 1.333l-6-4l-6 4m5 1h2m-2 3h2m7 8h.01m-.01-3v-4\"/>"}, "school-exclamation-solid": {"body": "<g fill=\"currentColor\"><path d=\"m6 10.524l-2.27.638a1 1 0 0 0-.73.963V20a1 1 0 0 0 1 1h2z\"/><path fill-rule=\"evenodd\" d=\"M12.555 3.168a1 1 0 0 0-1.11 0l-6 4a1 1 0 0 0 1.11 1.664L8 7.869V21h8V7.869l1.445.963A1 1 0 0 0 18 9a.999.999 0 0 0 .555-1.832zM10 12a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1m1-4a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm8 12a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H20a1 1 0 0 1-1-1m1-8a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0v-4a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/></g>"}, "school-flag-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.429 11l5-6l5 6m-10 0h-3v8h16v-8h-3m-10 0h-4l3-4h4.337zm10 0V7.5m-3 5.5a2 2 0 1 1-4 0a2 2 0 0 1 4 0m3-8v3h4V5z\"/>"}, "school-flag-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12 12a1 1 0 1 0 0 2a1 1 0 0 0 0-2\"/><path fill-rule=\"evenodd\" d=\"M17 4a1 1 0 0 0-1 1v4.97l-4-4.923l-5.349 6.584a1 1 0 0 1-.776.369H3v7a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-7h-2.875a1 1 0 0 1-.125-.008V9h3a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1zm-8 9a3 3 0 1 1 6 0a3 3 0 0 1-6 0\" clip-rule=\"evenodd\"/><path d=\"M5.2 6.4L2.5 10h2.899l3.25-4H6a1 1 0 0 0-.8.4\"/></g>"}, "school-flag-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 20v-9l-4 1.125V20zm0 0h8m-8 0V6.667M16 20v-9l4 1.125V20zm0 0V7m0 0V4h4v3zM6 8l6-4l4 2.667M11 9h2m-2 3h2\"/>"}, "school-flag-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M15 4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-4v13H8V7.869l-1.445.963a1 1 0 0 1-1.11-1.664l6-4a1 1 0 0 1 1.11 0L15 4.798zm-5 8a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1m1-4a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/><path d=\"M18 9h-.016h.03zM6 10.524l-2.27.638a1 1 0 0 0-.73.963V20a1 1 0 0 0 1 1h2zm14.27.638L18 10.524V21h2a1 1 0 0 0 1-1v-7.875a1 1 0 0 0-.73-.963\"/></g>"}, "school-lock-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.357 11l5-6l3.333 4m-8.333 2h-3v8h7m-4-8h-4l3-4h4.337zm8.331-2L14.02 7h4.337l1.5 2m-8.324 5.898a2 2 0 1 1 1.502-3.708M16.357 15v-2.5a1.5 1.5 0 0 1 3 0V15m-4 0h5a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1\"/>"}, "school-lock-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6.651 11.63L12 5.048l3.01 3.704a4.5 4.5 0 0 0-1.496 1.659a3 3 0 1 0-2.51 5.421L11 16v2c0 .729.195 1.412.535 2H4a1 1 0 0 1-1-1v-7h2.875a1 1 0 0 0 .776-.37\"/><path fill-rule=\"evenodd\" d=\"M16.102 10.427A2.5 2.5 0 0 0 15 12.5V14q-.087 0-.173.007a2 2 0 0 0-1.82 1.82v.006Q13 15.915 13 16v2a2 2 0 0 0 2 2h5.008A2 2 0 0 0 22 18v-2a2 2 0 0 0-2-2v-1.5q0-.257-.05-.5a2.5 2.5 0 0 0-3.848-1.573M17.5 12a.5.5 0 0 0-.5.5V14h1v-1.5a.5.5 0 0 0-.5-.5\" clip-rule=\"evenodd\"/><path d=\"M21.242 10a4.5 4.5 0 0 0-4.244-1.972L15.351 6H18a1 1 0 0 1 .8.4l2.7 3.6zm-8.33 2.588a1 1 0 1 0-1.324 1.323a4 4 0 0 1 1.323-1.323M5.2 6.4L2.5 10h2.899l3.25-4H6a1 1 0 0 0-.8.4\"/></g>"}, "school-lock-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.214 20v-9l-4 1.125V20zm0 0V6.667m0 13.333h2m6-11V6.667m2 1.333l-6-4l-6 4m5 1h2m-2 3h1m3 4v-2.5a1.5 1.5 0 0 1 3 0V16m-4 0h5a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1\"/>"}, "school-lock-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M11.445 3.168a1 1 0 0 1 1.11 0l6 4a1 1 0 0 1-1.11 1.664L16 7.87v1.66a4 4 0 0 0-2.667 1.526A1 1 0 0 0 13 11h-2a1 1 0 1 0 0 2h1.53q-.03.245-.03.5v.337a3.5 3.5 0 0 0-2 3.163v2c0 .744.232 1.433.627 2H8V7.869l-1.445.963a1 1 0 0 1-1.11-1.664zM10 9a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M16.5 11a2.5 2.5 0 0 1 1.59.57c.556.46.91 1.153.91 1.93V15a2 2 0 0 1 2 2v2a2 2 0 0 1-1.991 2H14a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2v-1.5a2.5 2.5 0 0 1 1.996-2.45H16q.243-.05.5-.05m0 2a.5.5 0 0 0-.5.5V15h1v-1.5a.5.5 0 0 0-.5-.5\" clip-rule=\"evenodd\"/><path d=\"M3.73 11.162L6 10.524V21H4a1 1 0 0 1-1-1v-7.875a1 1 0 0 1 .73-.963\"/></g>"}, "school-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 20v-9l-4 1.125V20zm0 0h8m-8 0V6.667M16 20v-9l4 1.125V20zm0 0V6.667M18 8l-6-4l-6 4m5 1h2m-2 3h2\"/>"}, "school-solid": {"body": "<g fill=\"currentColor\"><path d=\"m6 10.524l-2.27.638a1 1 0 0 0-.73.963V20a1 1 0 0 0 1 1h2zm12 0l2.27.638a1 1 0 0 1 .73.963V20a1 1 0 0 1-1 1h-2z\"/><path fill-rule=\"evenodd\" d=\"M12.555 3.168a1 1 0 0 0-1.11 0l-6 4a1 1 0 0 0 1.11 1.664L8 7.869V21h8V7.869l1.445.963A1 1 0 0 0 18 9a.999.999 0 0 0 .555-1.832zM10 12a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1m1-4a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/></g>"}, "school-xmark-alt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m16.976 15.464l1.767 1.768m0 0L20.511 19m-1.768-1.768L16.976 19m1.767-1.768l1.768-1.768M7.214 11l5-6l5 6m-10 0h-3v8h9.08m-6.08-8h-4l3-4h4.338zm10 0h3v1.557m-3-1.557h4l-3-4h-4.337zm-3 2a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "school-xmark-alt-solid": {"body": "<g fill=\"currentColor\"><path d=\"M6.651 11.63L12 5.048l5.349 6.584a1 1 0 0 0 .776.369H21v1.065a2.5 2.5 0 0 0-2.47.632a2.5 2.5 0 0 0-3.64.109a3 3 0 1 0-.563 1.088c-.19.813.032 1.704.667 2.338A2.5 2.5 0 0 0 14.47 20H4a1 1 0 0 1-1-1v-7h2.875a1 1 0 0 0 .776-.37\"/><path d=\"M17.469 14.757a1 1 0 0 0-1.415 1.414l1.061 1.061l-1.06 1.06a1 1 0 0 0 1.413 1.415l1.061-1.06l1.06 1.06a.997.997 0 0 0 1.415 0a1 1 0 0 0 0-1.414l-1.06-1.061L21 16.175l.004-.004a1 1 0 0 0 0-1.414L21 14.753a1 1 0 0 0-1.41.004l-1.06 1.06zM12 12a1 1 0 1 0 0 2a1 1 0 0 0 0-2m-9.5-2l2.7-3.6A1 1 0 0 1 6 6h2.649l-3.25 4zm12.851-4l3.25 4H21.5l-2.7-3.6A1 1 0 0 0 18 6z\"/></g>"}, "school-xmark-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.071 20v-9l-4 1.125V20zm0 0V6.667m0 13.333h4m4-9V6.667m0 4.333l4 1.125V13m-4-2v2m2-5l-6-4l-6 4m5 1h2m-2 3h2m3.015 4.409l1.768 1.768m0 0l1.768 1.767m-1.768-1.767l-1.768 1.767m1.768-1.767l1.768-1.768\"/>"}, "school-xmark-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M15.308 15.702a1 1 0 0 1 1.414 0l1.06 1.06l1.061-1.06a1 1 0 0 1 1.415 1.414l-1.061 1.06l1.06 1.061a1 1 0 0 1-1.414 1.414l-1.06-1.06l-1.061 1.06a1 1 0 0 1-1.414-1.414l1.06-1.06l-1.06-1.061a1 1 0 0 1 0-1.414\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M11.445 3.168a1 1 0 0 1 1.11 0l6 4a1 1 0 0 1-1.11 1.664L16 7.87v6.04A2.5 2.5 0 0 0 13.748 21H8V7.869l-1.445.963a1 1 0 0 1-1.11-1.664zM11 11a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm-1-2a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/><path d=\"M21 14.371v-2.246a1 1 0 0 0-.73-.963L18 10.524v3.923a2.5 2.5 0 0 1 3-.076m-3 2.174l.843-.843a1 1 0 0 1 1.415 1.414l-1.061 1.06l1.06 1.061a1 1 0 0 1-1.414 1.414L18 19.808zm-2-1.136a1 1 0 0 0-.692 1.707l.692.692zm0 3.136v2.4a1 1 0 0 1-.692-1.707zM6 10.524l-2.27.638a1 1 0 0 0-.73.963V20a1 1 0 0 0 1 1h2z\"/></g>"}, "search-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m21 21l-3.5-3.5M17 10a7 7 0 1 1-14 0a7 7 0 0 1 14 0Z\"/>"}, "search-solid": {"body": "<g fill=\"currentColor\"><path d=\"M10 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16\"/><path fill-rule=\"evenodd\" d=\"M21.707 21.707a1 1 0 0 1-1.414 0l-3.5-3.5a1 1 0 0 1 1.414-1.414l3.5 3.5a1 1 0 0 1 0 1.414\" clip-rule=\"evenodd\"/></g>"}, "seedling-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.839 12.454s1.25-3.088-.216-5.29s-5.036-3.055-5.225-2.92c-.189.134-1.118 3.325.348 5.527s5.093 2.683 5.093 2.683m0 0C10.339 13.454 12 15 12 18v2c0-2-.43-3.419 2.07-5.919m0 0s-.49-2.789 1.12-4.358s4.49-1.547 4.67-1.284s.432 2.844-1.08 4.318c-1.61 1.569-4.71 1.324-4.71 1.324\"/>"}, "seedling-solid": {"body": "<path fill=\"currentColor\" d=\"m10.972 12.217l-.035.116l.128.186c.327.473.812 1.172 1.214 2.09q.32-.43.744-.884l-.002-.032a8 8 0 0 1 .01-1.29c.087-.998.409-2.37 1.461-3.396c1.008-.982 2.356-1.424 3.405-1.599a7 7 0 0 1 1.43-.099c.195.01.393.03.57.07c.**************.311.103c.*************.477.392c.************.22.556c.032.15.056.321.072.504c.03.367.03.828-.03 1.329c-.118.983-.487 2.252-1.47 3.21c-1.035 1.01-2.473 1.38-3.52 1.525a10.5 10.5 0 0 1-1.477.097c-.93.999-1.268 1.732-1.407 2.351a4 4 0 0 0-.074.451L13 18v2a1 1 0 1 1-2 0q-.001-.3-.013-.633c-.014-.472-.03-.987.012-1.514c-.036-1.879-.756-2.94-1.402-3.893a17 17 0 0 1-.396-.604l-.166-.035a12 12 0 0 1-1.534-.443c-1.156-.42-2.676-1.184-3.587-2.553c-.906-1.36-1.04-2.963-.96-4.16c.04-.61.139-1.15.244-1.56c.053-.203.11-.387.169-.538c.028-.074.064-.157.107-.237c.021-.04.052-.093.093-.15a1.07 1.07 0 0 1 .677-.43c.086-.015.16-.019.211-.02c.102-.002.202.007.285.018c.17.021.373.061.59.115c.436.108.997.289 1.596.546c1.17.502 2.638 1.363 3.53 2.701c.924 1.389.96 2.996.817 4.147a9.4 9.4 0 0 1-.302 1.46\"/>"}, "server-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 12a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1M5 12h14M5 12a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1m-2 3h.01M14 15h.01M17 9h.01M14 9h.01\"/>"}, "server-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 5a2 2 0 0 0-2 2v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V7a2 2 0 0 0-2-2zm9 2a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm3 0a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zM3 17v-3a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m11-2a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm3 0a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "shapes-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.071 14v6h6v-6zm4.5-4h7l-3.5-6zm8 10a3.5 3.5 0 1 0 0-7a3.5 3.5 0 0 0 0 7Z\"/>"}, "shapes-solid": {"body": "<path fill=\"currentColor\" d=\"M12.864 3.496a1 1 0 0 0-1.728 0l-3.5 6A1 1 0 0 0 8.5 11h7a1 1 0 0 0 .864-1.504zM4 13a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zm12.5-1a4.5 4.5 0 1 0 0 9a4.5 4.5 0 0 0 0-9\"/>"}, "share-all-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m15.141 6l5.518 4.95a1.05 1.05 0 0 1 0 1.549l-5.612 5.088m-6.154-3.214v1.615a.95.95 0 0 0 1.525.845l5.108-4.251a1.1 1.1 0 0 0 0-1.646l-5.108-4.251a.95.95 0 0 0-1.525.846v1.7c-3.312 0-6 2.979-6 6.654v1.329a.7.7 0 0 0 1.344.353a5.17 5.17 0 0 1 4.652-3.191z\"/>"}, "share-all-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14.516 6.743c-.41-.368-.443-1-.077-1.41a.99.99 0 0 1 1.405-.078l5.487 4.948l.007.006A2.05 2.05 0 0 1 22 11.721a2.06 2.06 0 0 1-.662 1.51l-5.584 5.09a.99.99 0 0 1-1.404-.07a1.003 1.003 0 0 1 .068-1.412l5.578-5.082a.05.05 0 0 0 .015-.036a.05.05 0 0 0-.015-.036zm-6.543 9.199v-.42a4.17 4.17 0 0 0-2.715 2.415c-.154.382-.44.695-.806.88a1.683 1.683 0 0 1-2.167-.571a1.7 1.7 0 0 1-.279-1.092V15.88c0-3.77 2.526-7.039 5.967-7.573V7.57a1.96 1.96 0 0 1 .993-1.838a1.93 1.93 0 0 1 2.153.184l5.08 4.248l.012.011l.011.01a2.1 2.1 0 0 1 .703 1.57a2.1 2.1 0 0 1-.726 1.59l-5.08 4.25a1.933 1.933 0 0 1-2.929-.614a1.96 1.96 0 0 1-.217-1.04Z\" clip-rule=\"evenodd\"/>"}, "share-nodes-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.926 10.898L15 7.727m-7.074 5.39L15 16.29M8 12a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0Zm12 5.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0Zm0-11a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0Z\"/>"}, "share-nodes-solid": {"body": "<path fill=\"currentColor\" d=\"M17.5 3a3.5 3.5 0 0 0-3.456 4.06L8.143 9.704a3.5 3.5 0 1 0-.01 4.6l5.91 2.65a3.5 3.5 0 1 0 .863-1.805l-5.94-2.662a3.5 3.5 0 0 0 .002-.961l5.948-2.667A3.5 3.5 0 1 0 17.5 3\"/>"}, "shield-check-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.5 11.5L11 13l4-3.5M12 20a16.4 16.4 0 0 1-5.092-5.804A16.7 16.7 0 0 1 5 6.666L12 4l7 2.667a16.7 16.7 0 0 1-1.908 7.529A16.4 16.4 0 0 1 12 20\"/>"}, "shield-check-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M11.644 3.066a1 1 0 0 1 .712 0l7 2.666A1 1 0 0 1 20 6.68a17.7 17.7 0 0 1-2.023 7.98a17.4 17.4 0 0 1-5.402 6.158a1 1 0 0 1-1.15 0a17.4 17.4 0 0 1-5.403-6.157A17.7 17.7 0 0 1 4 6.68a1 1 0 0 1 .644-.949zm4.014 7.187a1 1 0 0 0-1.316-1.506l-3.296 2.884l-.839-.838a1 1 0 0 0-1.414 1.414l1.5 1.5a1 1 0 0 0 1.366.046l4-3.5Z\" clip-rule=\"evenodd\"/>"}, "shield-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 20a16.4 16.4 0 0 1-5.092-5.804A16.7 16.7 0 0 1 5 6.666L12 4l7 2.667a16.7 16.7 0 0 1-1.908 7.529A16.4 16.4 0 0 1 12 20\"/>"}, "shield-solid": {"body": "<path fill=\"currentColor\" d=\"M12.356 3.066a1 1 0 0 0-.712 0l-7 2.666A1 1 0 0 0 4 6.68a17.7 17.7 0 0 0 2.022 7.98a17.4 17.4 0 0 0 5.403 6.158a1 1 0 0 0 1.15 0a17.4 17.4 0 0 0 5.402-6.157A17.7 17.7 0 0 0 20 6.68a1 1 0 0 0-.644-.949z\"/>"}, "shopping-bag-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 10V6a3 3 0 0 1 3-3v0a3 3 0 0 1 3 3v4m3-2l.917 11.923A1 1 0 0 1 17.92 21H6.08a1 1 0 0 1-.997-1.077L6 8z\"/>"}, "shopping-bag-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14 7h-4v3a1 1 0 0 1-2 0V7H6a1 1 0 0 0-.997.923l-.917 11.924A2 2 0 0 0 6.08 22h11.84a2 2 0 0 0 1.994-2.153l-.917-11.924A1 1 0 0 0 18 7h-2v3a1 1 0 1 1-2 0zm-2-3a2 2 0 0 0-2 2v1H8V6a4 4 0 0 1 8 0v1h-2V6a2 2 0 0 0-2-2\" clip-rule=\"evenodd\"/>"}, "shrimp-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.524 12.47H8a3 3 0 0 1-3-3V8h5m3.524 4.47a2.265 2.265 0 1 1 0 4.53H11a2 2 0 0 0-2 2v1h6.5m-1.976-7.53L15.5 7.764M10 8c1.846-1.251 3.077-1.408 5.5-.236M10 8v4.47L8 14m7.5-6.236c3.18-.245 5.556 2.362 4.754 5.45a51 51 0 0 1-.359 1.31c.017.812-.006 1.488-.063 2.088C19.632 18.702 17.6 20 15.5 20m0 0l-1.204-3.135M15.668 14h4.375M10 4H6a2 2 0 1 0 0 4\"/>"}, "shrimp-solid": {"body": "<path fill=\"currentColor\" d=\"M6 5a1 1 0 0 0-.001 2H9v7.494l-.392.3a1 1 0 0 1-1.402-1.403A4 4 0 0 1 4 9.471V8.235A3 3 0 0 1 6 3h4a1 1 0 1 1 0 2zm5 8.47h.979l2.992-7.03c-.88-.34-1.69-.512-2.496-.465c-.52.03-1.004.15-1.475.337zm5.974-6.628L14.352 13h6.972c.548-3.073-1.48-5.643-4.35-6.158M20.9 15h-6.138a1.3 1.3 0 0 1-.109.306l2.388 5.493c1.89-.497 3.576-1.902 3.786-4.092c.05-.515.074-1.075.073-1.707m-5.952 6l-2.174-5H11a3 3 0 0 0-3 3v2z\"/>"}, "shuffle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.484 9.166L15 7h5m0 0l-3-3m3 3l-3 3M4 17h4l1.577-2.253M4 7h4l7 10h5m0 0l-3 3m3-3l-3-3\"/>"}, "shuffle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M16.3 3.3a1 1 0 0 1 1.4 0l3 3c.4.4.4 1 0 1.4l-3 3a1 1 0 0 1-1.4-1.4L17.6 8h-2l-1.3 1.7a1 1 0 1 1-1.6-1.1l1.5-2.2c.2-.2.5-.4.8-.4h2.6l-1.3-1.3a1 1 0 0 1 0-1.4M3 7c0-.6.4-1 1-1h4c.3 0 .6.2.8.4l6.7 9.6h2l-1.2-1.3a1 1 0 0 1 1.4-1.4l3 3c.4.4.4 1 0 1.4l-3 3a1 1 0 0 1-1.4-1.4l1.3-1.3H15a1 1 0 0 1-.8-.4L7.5 8H4a1 1 0 0 1-1-1m7.2 7c.*******.2 1.3l-1.6 2.3a1 1 0 0 1-.8.4H4a1 1 0 1 1 0-2h3.5l1.3-1.8a1 1 0 0 1 1.4-.3Z\" clip-rule=\"evenodd\"/>", "hidden": true}, "sort-horizontal-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16h13M4 16l4-4m-4 4l4 4M20 8H7m13 0l-4 4m4-4l-4-4\"/>"}, "sort-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 20V10m0 10l-3-3m3 3l3-3m5-13v10m0-10l3 3m-3-3l-3 3\"/>"}, "sort-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M8 19.5V10m8-5.5V14\"/><path stroke-linejoin=\"round\" d=\"m5 17l3 3l3-3m8-10l-3-3l-3 3\"/></g>", "hidden": true}, "split-cells-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 18v2h6V4H4v2m16 12v2h-6V4h6v2M6.495 14.495L4 12m0 0l2.495-2.495M4 12h5.948m7.498 2.554L20 12m0 0l-2.554-2.554M20 12h-5.832\"/>"}, "stackoverflow-solid": {"body": "<g fill=\"currentColor\"><path d=\"M17 20v-5h2v6.988H3V15h1.98v5z\"/><path d=\"m6.84 14.522l8.73 1.825l.369-1.755l-8.73-1.825zm1.155-4.323l8.083 3.764l.739-1.617l-8.083-3.787zm3.372-5.481L10.235 6.08l6.859 5.704l1.132-1.362zM15.57 17H6.655v2h8.915zM12.861 3.111l6.193 6.415l1.414-1.415l-6.43-6.177z\"/></g>"}, "star-half-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"m12.25 20.825l4.247-2.436a1 1 0 0 0 .503-.867V4.03c0-.405-2.062 3.38-2.8 4.747a1 1 0 0 1-.807.523l-4.87.367c-.903.068-1.258 1.208-.55 1.776l3.576 2.878a1 1 0 0 1 .343 1.025l-1.11 4.366c-.217.856.701 1.553 1.468 1.113Z\"/>"}, "star-half-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13 4.024v-.005c0-.053.002-.353-.217-.632a1.01 1.01 0 0 0-1.176-.315c-.192.076-.315.193-.35.225c-.052.05-.094.1-.122.134a4 4 0 0 0-.31.457c-.207.343-.484.84-.773 1.375a169 169 0 0 0-1.606 3.074h-.002l-4.599.367c-1.775.14-2.495 2.339-1.143 3.488L6.17 15.14l-1.06 4.406c-.412 1.72 1.472 3.078 2.992 2.157l3.94-2.388c.592-.359.958-.996.958-1.692v-13.6Zm-2.002 0v.025z\" clip-rule=\"evenodd\"/>"}, "star-half-stroke-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M12 4.392v14.832M8.476 9.38l-4.553.36c-.888.07-1.248 1.165-.572 1.737l3.47 2.934a.98.98 0 0 1 .322.98l-1.06 4.388c-.206.855.736 1.531 1.497 1.073l3.898-2.351c.32-.193.723-.193 1.044 0l3.898 2.351c.76.458 1.703-.218 1.497-1.073l-1.06-4.388a.98.98 0 0 1 .322-.98l3.47-2.934c.676-.572.316-1.667-.572-1.737l-4.553-.36a1 1 0 0 1-.845-.606l-1.754-4.165c-.342-.812-1.508-.812-1.85 0L9.321 8.774a1 1 0 0 1-.845.606Z\"/>"}, "star-half-stroke-solid": {"body": "<path fill=\"currentColor\" d=\"m13.001 19.927l2.896 1.773c1.52.93 3.405-.442 2.992-2.179l-1.06-4.452l3.468-2.978c1.353-1.162.633-3.382-1.142-3.525L15.603 8.2l-1.754-4.226A1.97 1.97 0 0 0 13 3zM10.999 3c-.36.205-.663.53-.848.974L8.397 8.2l-4.552.366c-1.775.143-2.495 2.363-1.142 3.525l3.468 2.978l-1.06 4.452c-.413 1.737 1.472 3.11 2.992 2.178l2.896-1.773z\"/>"}, "star-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M11.083 5.104c.35-.8 1.485-.8 1.834 0l1.752 4.022a1 1 0 0 0 .84.597l4.463.342c.9.069 1.255 1.2.556 1.771l-3.33 2.723a1 1 0 0 0-.337 1.016l1.03 4.119c.214.858-.71 1.552-1.474 1.106l-3.913-2.281a1 1 0 0 0-1.008 0L7.583 20.8c-.764.446-1.688-.248-1.474-1.106l1.03-4.119A1 1 0 0 0 6.8 14.56l-3.33-2.723c-.698-.571-.342-1.702.557-1.771l4.462-.342a1 1 0 0 0 .84-.597z\"/>"}, "star-solid": {"body": "<path fill=\"currentColor\" d=\"M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937l-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39l3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36z\"/>"}, "stop-outline": {"body": "<rect width=\"12\" height=\"12\" x=\"6\" y=\"6\" fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" rx=\"1\"/>"}, "stop-solid": {"body": "<path fill=\"currentColor\" d=\"M7 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z\"/>"}, "store-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 12c.263 0 .524-.06.767-.175a2 2 0 0 0 .65-.491c.186-.21.333-.46.433-.734s.15-.568.15-.864a2.4 2.4 0 0 0 .586 1.591c.375.422.884.659 1.414.659s1.04-.237 1.414-.659A2.4 2.4 0 0 0 12 9.736a2.4 2.4 0 0 0 .586 1.591c.375.422.884.659 1.414.659s1.04-.237 1.414-.659A2.4 2.4 0 0 0 16 9.736c0 .295.052.588.152.861s.248.521.434.73a2 2 0 0 0 .649.488a1.8 1.8 0 0 0 1.53 0a2 2 0 0 0 .65-.488c.185-.209.332-.457.433-.73s.152-.566.152-.861c0-.974-1.108-3.85-1.618-5.121A.98.98 0 0 0 17.466 4H6.456a.99.99 0 0 0-.93.645C5.045 5.962 4 8.905 4 9.736c.023.59.241 1.148.611 1.567c.37.418.865.667 1.389.697m0 0c.328 0 .651-.091.94-.266A2.1 2.1 0 0 0 7.66 11h.681a2.1 2.1 0 0 0 .718.734c.29.175.613.266.942.266s.651-.091.94-.266c.29-.174.537-.427.719-.734h.681a2.1 2.1 0 0 0 .719.734c.289.175.612.266.94.266c.329 0 .652-.091.942-.266c.29-.174.536-.427.718-.734h.681c.183.307.43.56.719.734c.29.174.613.266.941.266a1.8 1.8 0 0 0 1.06-.351M6 12a1.77 1.77 0 0 1-1.163-.476M5 12v7a1 1 0 0 0 1 1h2v-5h3v5h7a1 1 0 0 0 1-1v-7m-5 3v2h2v-2z\"/>"}, "store-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.535 7.677c.313-.98.687-2.023.926-2.677H17.46c.253.63.646 1.64.977 2.61c.166.487.312.953.416 1.347c.11.42.148.675.148.779c0 .18-.032.355-.09.515c-.06.161-.144.3-.243.412q-.152.166-.324.245a.8.8 0 0 1-.686 0a1 1 0 0 1-.324-.245q-.152-.169-.242-.412a1.5 1.5 0 0 1-.091-.515a1 1 0 1 0-2 0a1.4 1.4 0 0 1-.333.927a.9.9 0 0 1-.667.323a.9.9 0 0 1-.667-.323A1.4 1.4 0 0 1 13 9.736a1 1 0 1 0-2 0a1.4 1.4 0 0 1-.333.927a.9.9 0 0 1-.667.323a.9.9 0 0 1-.667-.323A1.4 1.4 0 0 1 9 9.74v-.008a1 1 0 0 0-2 .003v.008a1.5 1.5 0 0 1-.18.712a1.2 1.2 0 0 1-.146.209l-.007.007a1 1 0 0 1-.325.248a.8.8 0 0 1-.316.08a.97.97 0 0 1-.563-.256a1 1 0 0 1-.102-.103A1.52 1.52 0 0 1 5 9.724v-.006a3 3 0 0 1 .029-.207q.035-.198.11-.49c.098-.385.237-.85.395-1.344ZM4 12.112a3.52 3.52 0 0 1-1-2.376c0-.349.098-.8.202-1.208c.112-.441.264-.95.428-1.46c.327-1.024.715-2.104.958-2.767A1.985 1.985 0 0 1 6.456 3h11.01c.803 0 1.539.481 1.844 1.243c.258.641.67 1.697 1.019 2.72a22 22 0 0 1 .457 1.487c.114.433.214.903.214 1.286c0 .412-.072.821-.214 1.207A3.3 3.3 0 0 1 20 12.16V19a2 2 0 0 1-2 2h-6a1 1 0 0 1-1-1v-4H8v4a1 1 0 0 1-1 1H6a2 2 0 0 1-2-2zM13 15a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1z\" clip-rule=\"evenodd\"/>"}, "stroopwafel-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 12a9 9 0 0 1-9 9m9-9a9 9 0 0 0-9-9m9 9h-5m-4 9a9 9 0 0 1-9-9m9 9v-5m-9-4a9 9 0 0 1 9-9m-9 9h5m4-9v5M8 3.936V8m0 0v4m0-4H3.936M8 8h4m-4 4v4m0-4h4m-4 4v4.065M8 16H3.936M8 16h4m0-8v4m0-4h4m-4 4v4m0-4h4m-4 4h4m0-12.064V8m0 0v4m0-4h4.065M16 12v4m0 0v4.065M16 16h4.065\"/>"}, "stroopwafel-solid": {"body": "<path fill=\"currentColor\" d=\"M11 2.05a10 10 0 0 0-2 .408V7h2zM7 3.337A10.05 10.05 0 0 0 3.338 7H7zM2.458 9a10 10 0 0 0-.409 2H7V9zm-.409 4a10 10 0 0 0 .409 2H7v-2zm1.289 4A10.05 10.05 0 0 0 7 20.662V17zM9 21.542q.963.304 2 .409V17H9zm4 .409a10 10 0 0 0 2-.409V17h-2zm4-1.289A10.05 10.05 0 0 0 20.662 17H17zM21.542 15q.303-.963.409-2H17v2zm.409-4a10 10 0 0 0-.409-2H17v2zm-1.289-4A10.05 10.05 0 0 0 17 3.338V7zM15 2.458a10 10 0 0 0-2-.409V7h2zM9 9h2v2H9zm0 4h2v2H9zm4 0v2h2v-2zm0-2h2V9h-2z\"/>"}, "subscript-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 20h-4v-.5c1.099-1.033 3.75-2.5 3.75-3.5v-1a1 1 0 0 0-1-1H17a1 1 0 0 0-1 1M4 4l9.122 11.393m0-11.393L4 15.393\"/>"}, "sun-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5V3m0 18v-2M7.05 7.05L5.636 5.636m12.728 12.728L16.95 16.95M5 12H3m18 0h-2M7.05 16.95l-1.414 1.414M18.364 5.636L16.95 7.05M16 12a4 4 0 1 1-8 0a4 4 0 0 1 8 0\"/>"}, "sun-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13 3a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0zM6.343 4.929A1 1 0 0 0 4.93 6.343l1.414 1.414a1 1 0 0 0 1.414-1.414zm12.728 1.414a1 1 0 0 0-1.414-1.414l-1.414 1.414a1 1 0 0 0 1.414 1.414zM12 7a5 5 0 1 0 0 10a5 5 0 0 0 0-10m-9 4a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm16 0a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zM7.757 17.657a1 1 0 1 0-1.414-1.414l-1.414 1.414a1 1 0 1 0 1.414 1.414zm9.9-1.414a1 1 0 0 0-1.414 1.414l1.414 1.414a1 1 0 0 0 1.414-1.414zM13 19a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "superscript-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 10h-4v-.5C17.099 8.467 19.75 7 19.75 6V5a1 1 0 0 0-1-1H17a1 1 0 0 0-1 1M4 7.303l9.122 11.394m0-11.394L4 18.697\"/>"}, "swatchbook-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.111 20A3.11 3.11 0 0 1 4 16.889v-12C4 4.398 4.398 4 4.889 4h4.444a.89.89 0 0 1 .89.889v12A3.11 3.11 0 0 1 7.11 20Zm0 0h12a.89.89 0 0 0 .889-.889v-4.444a.89.89 0 0 0-.889-.89h-4.389a.9.9 0 0 0-.62.253l-3.767 3.665a1 1 0 0 0-.146.185c-.868 1.433-1.581 1.858-3.078 2.12Zm0-3.556h.009m7.933-10.927l3.143 3.143a.89.89 0 0 1 0 1.257l-7.974 7.974v-8.8l3.574-3.574a.89.89 0 0 1 1.257 0Z\"/>"}, "swatchbook-solid": {"body": "<path fill=\"currentColor\" d=\"M20 14h-2.722L11 20.278a5.5 5.5 0 0 1-.9.722H20a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1M9 3H4a1 1 0 0 0-1 1v13.5a3.5 3.5 0 1 0 7 0V4a1 1 0 0 0-1-1M6.5 18.5a1 1 0 1 1 0-2a1 1 0 0 1 0 2M19.132 7.9L15.6 4.368a1 1 0 0 0-1.414 0L12 6.55v9.9l7.132-7.132a1 1 0 0 0 0-1.418\"/>"}, "t-shirt-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5h-.167a4 4 0 0 0-2.4.8L3.5 8l2 3.5L8 10v9h8v-9l2.5 1.5l2-3.5l-2.933-2.2a4 4 0 0 0-2.4-.8H15M9 5c0 1.5 1.5 3 3 3s3-1.5 3-3M9 5h6\"/>"}, "t-shirt-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.833 5a5 5 0 0 1 3-1h6.334a5 5 0 0 1 3 1L21.1 7.2a1 1 0 0 1 .268 1.296l-2 3.5a1 1 0 0 1-1.382.361l-.986-.59V19a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-7.234l-.985.591a1 1 0 0 1-1.383-.36l-2-3.5A1 1 0 0 1 2.9 7.2zM14 5h-4c0 .425.223.933.645 1.355c.422.423.93.645 1.355.645s.933-.222 1.355-.645C13.778 5.933 14 5.425 14 5\" clip-rule=\"evenodd\"/>"}, "table-column-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M3 11h18m-9 0v8m-8 0h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z\"/>"}, "table-column-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 8v6h7v-6zm16 6h-7v-6h7z\" clip-rule=\"evenodd\"/>"}, "table-row-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M3 11h18M3 15h18m-9-4v8m-8 0h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z\"/>"}, "table-row-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm2 8v-2h7v2zm0 2v2h7v-2zm9 2h7v-2h-7zm7-4v-2h-7v2z\" clip-rule=\"evenodd\"/>"}, "tablet-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 18h2M5.875 3h12.25c.483 0 .875.448.875 1v16c0 .552-.392 1-.875 1H5.875C5.392 21 5 20.552 5 20V4c0-.552.392-1 .875-1\"/>"}, "tablet-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4c0-.975.718-2 1.875-2h12.25C19.282 2 20 3.025 20 4v16c0 .975-.718 2-1.875 2H5.875C4.718 22 4 20.975 4 20zm7 13a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "tag-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.583 8.445h.01M10.86 19.71l-6.573-6.63a.993.993 0 0 1 0-1.4l7.329-7.394A.98.98 0 0 1 12.31 4l5.734.007A1.97 1.97 0 0 1 20 5.983v5.5a1 1 0 0 1-.316.727l-7.44 7.5a.974.974 0 0 1-1.384.001Z\"/>"}, "tag-solid": {"body": "<path fill=\"currentColor\" d=\"M18.045 3.007L12.31 3a1.97 1.97 0 0 0-1.4.585l-7.33 7.394a2 2 0 0 0 0 2.805l6.573 6.631a1.96 1.96 0 0 0 1.4.585a1.97 1.97 0 0 0 1.4-.585l7.409-7.477A2 2 0 0 0 21 11.479v-5.5a2.97 2.97 0 0 0-2.955-2.972m-2.452 6.438a1 1 0 1 1 0-2a1 1 0 0 1 0 2\"/>"}, "tailwind-solid": {"body": "<path fill=\"currentColor\" d=\"M11.782 5.72a4.773 4.773 0 0 0-4.8 4.173a3.43 3.43 0 0 1 2.741-1.687c1.689 0 2.974 1.972 3.758 2.587a5.73 5.73 0 0 0 5.382.935c2-.638 2.934-2.865 3.137-3.921c-.969 1.379-2.44 2.207-4.259 1.231c-1.253-.673-2.19-3.438-5.959-3.318M6.8 11.979A4.77 4.77 0 0 0 2 16.151a3.43 3.43 0 0 1 2.745-1.687c1.689 0 2.974 1.972 3.758 2.587a5.73 5.73 0 0 0 5.382.935c2-.638 2.933-2.865 3.137-3.921c-.97 1.379-2.44 2.208-4.259 1.231c-1.253-.673-2.19-3.443-5.963-3.317\"/>"}, "teddy-bear-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7.5 15a2.5 2.5 0 1 0 0 5a2.5 2.5 0 0 0 0-5m0 0l1.677-4.17m6.698-3.827C17.012 7.003 18 6.105 18 5s-.922-2-2.059-2c-.896 0-1.685.743-1.968 1.52M8.127 6.995C7.002 6.995 6 6.105 6 5s.912-2 2.038-2c.934 0 1.706.678 1.947 1.52M14 18h-4m6-3l-1.458-3.891m-6.635 2.879l-.449.193a1.868 1.868 0 1 1-1.103-3.568L8.5 10m7.106 3.988l.413.144a1.865 1.865 0 1 0 1.172-3.54l-1.746-.558M12 9.062v-.01m-1.378-1.554v-.01m3.034.01v-.01M16 8a4 4 0 1 1-8 0a4 4 0 0 1 8 0m3 9.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "teddy-bear-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M10.451 3.244C9.911 2.514 9.052 2 8.038 2C6.378 2 5 3.326 5 5c0 1.315.88 2.36 2.004 2.787a5 5 0 0 0 .177 1.55L6.08 9.65a2.868 2.868 0 0 0-.802 5.145A3.5 3.5 0 1 0 10.663 19h2.674a3.5 3.5 0 1 0 5.11-4.409a2.865 2.865 0 0 0-.954-4.953l-.696-.223a5 5 0 0 0 .2-1.588l-.001-.031C18.128 7.367 19 6.311 19 5c0-1.684-1.397-3-3.059-3c-1.005 0-1.841.554-2.384 1.247A5 5 0 0 0 12 3c-.54 0-1.061.086-1.549.244M8.685 4.257c-.49.435-.895.964-1.184 1.56C7.193 5.624 7 5.304 7 5c0-.535.447-1 1.038-1a1 1 0 0 1 .647.257M17 5c0 .31-.19.63-.497.824a5 5 0 0 0-1.174-1.555c.197-.169.423-.269.612-.269C16.553 4 17 4.475 17 5m-2 12.5a1.5 1.5 0 1 0 3 0a1.5 1.5 0 0 0-3 0m-9 0a1.5 1.5 0 1 0 3 0a1.5 1.5 0 0 0-3 0m5.043-10.012a.5.5 0 0 0-1 0v.01a.5.5 0 0 0 1 0zm3.023.01a.5.5 0 0 0-1 0v.01a.5.5 0 1 0 1 0zM13 9.052a1 1 0 1 0-2 0v.01a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "terminal-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m8 9l3 3l-3 3m5 0h3M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "terminal-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 4a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1zm4.293 5.707a1 1 0 0 1 1.414-1.414l3 3a1 1 0 0 1 0 1.414l-3 3a1 1 0 0 1-1.414-1.414L9.586 12zM13 14a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "text-size-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 6.2V5h11v1.2M8 5v14m-3 0h6m2-6.8V11h8v1.2M17 11v8m-1.5 0h3\"/>"}, "text-size-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 5c0-.6.4-1 1-1h11c.6 0 1 .4 1 1v1.2a1 1 0 1 1-2 0V6H9v12h2a1 1 0 1 1 0 2H5a1 1 0 1 1 0-2h2V6H4v.2a1 1 0 1 1-2 0zm10 6c0-.6.4-1 1-1h8c.6 0 1 .4 1 1v1.2a1 1 0 1 1-2 0V12h-2v6h.5a1 1 0 1 1 0 2h-3a1 1 0 1 1 0-2h.5v-6h-2v.2a1 1 0 1 1-2 0z\" clip-rule=\"evenodd\"/>", "hidden": true}, "text-slash-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 6.2V5h12v1.2M7 19h6m.2-14l-1.677 6.523M9.6 19l1.029-4M5 5l6.523 6.523M19 19l-7.477-7.477\"/>"}, "text-slash-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M13.2 4H7a1 1 0 0 0-1 .6l-.3-.3a1 1 0 0 0-1.4 1.4l14 14a1 1 0 0 0 1.4-1.4l-7-7L14 6h4v.2a1 1 0 1 0 2 0V5c0-.6-.4-1-1-1zM11 9.6L12 6H8v.5l3 3Zm-.1 4.4c.*******.7 1.2l-.7 2.8H13a1 1 0 1 1 0 2H7a1 1 0 0 1 0-2h1.8l.9-3.2a1 1 0 0 1 1.2-.8\" clip-rule=\"evenodd\"/>", "hidden": true}, "text-underline-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 19h14M7.6 16l4.298-10.93a.11.11 0 0 1 .204 0L16.4 16m-8.8 0H6.5m1.1 0h1.65m7.15 0h-1.65m1.65 0h1.1m-8.333-4h5.66\"/>"}, "theatre-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m7.533 11.862l.01-.003m5.581 7.143c-.5.515-.92.847-1.06.89c-.48.145-5.43-1.28-6.238-3.33c-.81-2.051-1.831-5.816-1.89-6.22c-.06-.404 1.56-1.724 3.597-2.61m1.989 8.055c-.227.262-.39.56-.556.847M13.5 12c.5.5 1 1.049 2 1.049S17 12.5 17.5 12m-4-4h.01m3.99 0h.01M10.5 5.5c0-.29 2.5-1.5 5-1.5s5 1.136 5 1.5V12c0 1.966-4.291 5-5 5c-.743 0-5-3.034-5-5z\"/>"}, "theatre-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"m7.5 6.495l-.007.002c-1.37.416-2.572 1.225-3.405 1.893a14 14 0 0 0-1.284 1.165l-.077.081l-.022.024l-.007.007l-.002.003l-.001.002a1 1 0 0 0-.216.961l1.89 6.22c.281.925.907 1.632 1.595 2.158c.69.528 1.497.917 2.254 1.198c.76.284 1.508.471 2.103.58c.297.054.568.09.793.107q.17.014.329.012c.081-.002.243-.008.411-.06c.168-.05.306-.135.374-.18a4 4 0 0 0 .267-.191c.177-.14.382-.32.6-.531l-.23-.115a14 14 0 0 1-2.068-1.307c-.49-.373-1.01-.824-1.49-1.35a1 1 0 0 1-1.707-1.04l.06-.103c.108-.19.237-.417.407-.65A5.4 5.4 0 0 1 7.5 13zm-1.778 4.551a1 1 0 0 0 .581 1.914l.01-.003a1 1 0 1 0-.581-1.914z\"/><path d=\"M15 4c-1.431 0-2.817.425-3.809.822a14 14 0 0 0-1.567.741l-.097.055l-.028.017l-.009.005l-.003.001l-.001.002A1 1 0 0 0 9 6.5V13c0 .967.393 1.826.899 2.53s1.165 1.312 1.807 1.8c.646.493 1.307.89 1.845 1.166c.268.139.517.252.728.334c.*************.31.107c.***************.411.063s.332-.041.41-.063q.153-.044.311-.107c.21-.082.46-.195.728-.334a12.7 12.7 0 0 0 1.845-1.165c.642-.49 1.301-1.097 1.807-1.801S21 13.967 21 13V6.5c0-.351-.185-.677-.486-.858h-.001l-.003-.002l-.009-.005l-.028-.017l-.097-.055q-.125-.07-.348-.187a14 14 0 0 0-1.22-.554C17.819 4.425 16.433 4 15 4m5 2.5l.514-.858zM13 7a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm4 0a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2zm-4.707 4.293a1 1 0 0 0 0 1.414l.084.085c.477.483 1.243 1.257 2.623 1.257s2.146-.774 2.623-1.257l.084-.085a1 1 0 0 0-1.414-1.414c-.53.53-.757.756-1.293.756s-.764-.227-1.293-.756a1 1 0 0 0-1.414 0\"/></g>"}, "thumbs-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 13c-.889.086-1.416.543-2.156 1.057a22.3 22.3 0 0 0-3.958 5.084a1.6 1.6 0 0 1-.582.628a1.55 1.55 0 0 1-1.466.087a1.6 1.6 0 0 1-.537-.406a1.67 1.67 0 0 1-.384-1.279l1.389-4.114M17 13h3V6.5A1.5 1.5 0 0 0 18.5 5v0A1.5 1.5 0 0 0 17 6.5zm-6.5 1H5.585c-.286 0-.372-.014-.626-.15a1.8 1.8 0 0 1-.637-.572a1.87 1.87 0 0 1-.215-1.673l2.098-6.4C6.462 4.48 6.632 4 7.88 4c2.302 0 4.79.943 6.67 1.475\"/>"}, "thumbs-down-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.97 14.316H5.004c-.322 0-.64-.08-.925-.232a2 2 0 0 1-.717-.645a2.1 2.1 0 0 1-.242-1.883l2.36-7.201C5.769 3.54 5.96 3 7.365 3c2.072 0 4.276.678 6.156 1.256c.473.145.925.284 1.35.404h.114v9.862a25.5 25.5 0 0 0-4.238 5.514c-.197.376-.516.67-.901.83a1.74 1.74 0 0 1-1.21.048a1.8 1.8 0 0 1-.96-.757a1.87 1.87 0 0 1-.269-1.211l1.562-4.63ZM19.822 14H17V6a2 2 0 1 1 4 0v6.823c0 .65-.527 1.177-1.177 1.177Z\" clip-rule=\"evenodd\"/>"}, "thumbs-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 11c.889-.086 1.416-.543 2.156-1.057a22.3 22.3 0 0 0 3.958-5.084a1.6 1.6 0 0 1 .582-.628a1.55 1.55 0 0 1 1.466-.087c.205.095.388.233.537.406a1.64 1.64 0 0 1 .384 1.279l-1.388 4.114M7 11H4v6.5A1.5 1.5 0 0 0 5.5 19v0A1.5 1.5 0 0 0 7 17.5zm6.5-1h4.915c.286 0 .372.014.626.15s.472.332.637.572a1.87 1.87 0 0 1 .215 1.673l-2.098 6.4C17.538 19.52 17.368 20 16.12 20c-2.303 0-4.79-.943-6.67-1.475\"/>"}, "thumbs-up-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M15.03 9.684h3.965c.322 0 .64.08.925.232s.532.374.717.645a2.11 2.11 0 0 1 .242 1.883l-2.36 7.201c-.288.814-.48 1.355-1.884 1.355c-2.072 0-4.276-.677-6.157-1.256c-.472-.145-.924-.284-1.348-.404h-.115V9.478a25.5 25.5 0 0 0 4.238-5.514a1.8 1.8 0 0 1 .901-.83a1.74 1.74 0 0 1 1.21-.048c.396.13.736.397.96.757c.225.36.32.788.269 1.211zM4.177 10H7v8a2 2 0 1 1-4 0v-6.823C3 10.527 3.527 10 4.176 10Z\" clip-rule=\"evenodd\"/>"}, "thumbtack-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 20v-4M7 4h10M9 5v5c0 .552-.47 1.005-.948 1.279c-1.435.822-2.602 3.245-2.257 4.365c.078.254.354.356.62.356h11.17c.266 0 .542-.102.62-.356c.345-1.12-.822-3.543-2.256-4.365c-.48-.274-.949-.727-.949-1.279V5a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1Z\"/>"}, "thumbtack-solid": {"body": "<path fill=\"currentColor\" d=\"M8 5v4.997a.3.3 0 0 1-.068.113c-.08.098-.213.207-.378.301c-.947.543-1.713 1.54-2.191 2.488A6.2 6.2 0 0 0 4.82 14.4c-.1.48-.138 1.031.018 1.539C5.12 16.846 6.02 17 6.414 17H11v3a1 1 0 1 0 2 0v-3h4.586c.395 0 1.295-.154 1.575-1.061c.156-.508.118-1.059.017-1.539a6.2 6.2 0 0 0-.541-1.5c-.479-.95-1.244-1.946-2.191-2.489a1.4 1.4 0 0 1-.378-.301a.3.3 0 0 1-.068-.113V5h1a1 1 0 1 0 0-2H7a1 1 0 1 0 0 2z\"/>"}, "ticket-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.5 12A2.5 2.5 0 0 1 21 9.5V7a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v2.5a2.5 2.5 0 0 1 0 5V17a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-2.5a2.5 2.5 0 0 1-2.5-2.5\"/>"}, "ticket-solid": {"body": "<path fill=\"currentColor\" d=\"M4 5a2 2 0 0 0-2 2v2.5a1 1 0 0 0 1 1a1.5 1.5 0 1 1 0 3a1 1 0 0 0-1 1V17a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2.5a1 1 0 0 0-1-1a1.5 1.5 0 1 1 0-3a1 1 0 0 0 1-1V7a2 2 0 0 0-2-2z\"/>"}, "toggle-header-cell-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h10M3 15v-4m0 4h9m-9-4V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v3M3 11h11m-2-.208V19m3-4h1.99M21 15a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "toggle-header-column-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 5v14m6-8h-6m6 4h-6m-9-3h1.99M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1m8-7a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "toggle-header-row-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 15v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-3M3 15V6a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v9M3 15h18M8 15v4m4-4v4m4-4v4m-7-9h1.99M15 10a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/>"}, "tools-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"m7.582 8.96l2.232 2.232l-1.618 1.618a3.523 3.523 0 0 0-3.697 5.12a1 1 0 0 0 .936.53a1 1 0 0 0 .53.936a3.522 3.522 0 0 0 5.12-3.697l4.81-4.811a3.523 3.523 0 0 0 3.697-5.12a1 1 0 0 0-.935-.53a1 1 0 0 0-.531-.936a3.523 3.523 0 0 0-5.12 3.697l-1.778 1.778l-2.232-2.231zm1.31-3.14a1 1 0 0 0-.536-.761L6.374 4.046a1 1 0 0 0-1.162.183l-.863.863a1 1 0 0 0-.183 1.162l1.013 1.982a1 1 0 0 0 .762.537l1.112.144a1 1 0 0 0 .836-.285l.863-.863a1 1 0 0 0 .285-.835zm10.553 10.585l-3.119-3.119a2 2 0 0 0-2.828 0l-.172.173a2 2 0 0 0 0 2.828l3.118 3.119a2 2 0 0 0 2.829 0l.172-.172a2 2 0 0 0 0-2.829Z\"/>"}, "tracking-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5 19h4m6 0h4m-6.963-4.384V8.634L17 5.94m-4.93 2.662L7.042 5.94M12 2.997l5.033 2.906v5.812L12 14.62l-5.033-2.906V5.903zM14 19a2 2 0 1 1-4 0a2 2 0 0 1 4 0Z\"/>"}, "tracking-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M9.166 19.986A1 1 0 0 1 9 20H5a1 1 0 1 1 0-2h4q.085 0 .166.014a3.001 3.001 0 0 1 5.668 0A1 1 0 0 1 15 18h4a1 1 0 1 1 0 2h-4q-.084 0-.166-.014a3.001 3.001 0 0 1-5.668 0M11 19a1 1 0 1 1 2 0a1 1 0 0 1-2 0\" clip-rule=\"evenodd\"/><path d=\"M11.5 2.131a1 1 0 0 1 1 0l4.601 2.657q-.09.028-.179.075L12.08 7.475L6.946 4.76zM5.967 6.505v5.21a1 1 0 0 0 .5.866l4.57 2.638V9.186zm7.07 8.671l4.496-2.595a1 1 0 0 0 .5-.866v-5.2a1 1 0 0 1-.161.108l-4.835 2.608z\"/></g>"}, "trash-bin-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1M6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1z\"/>"}, "trash-bin-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414M10 6h4V4h-4zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0z\" clip-rule=\"evenodd\"/>"}, "truck-clock-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h6l2 4m-8-4v8H9m4-8V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v9h2m14 0h2v-4m0 0h-5M8 8.667V10l1.5 1.5m10 5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-10 0a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "truck-clock-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2.586 4.586A2 2 0 0 1 4 4h8a2 2 0 0 1 2 2h5a1 1 0 0 1 .894.553l2 4c.07.139.106.292.106.447v4a1 1 0 0 1-1 1h-.535a3.5 3.5 0 1 1-6.93 0h-3.07a3.5 3.5 0 1 1-6.93 0H3a1 1 0 0 1-1-1V6a2 2 0 0 1 .586-1.414M18.208 15.61a1.497 1.497 0 0 0-2.416 0a1.5 1.5 0 1 0 2.416 0m-10 0a1.498 1.498 0 0 0-2.416 0a1.5 1.5 0 1 0 2.416 0m5.79-7.612v2.02h5.396l-1-2.02zM9 8.667a1 1 0 1 0-2 0V10a1 1 0 0 0 .293.707l1.5 1.5a1 1 0 0 0 1.414-1.414L9 9.586z\" clip-rule=\"evenodd\"/>"}, "truck-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h6l2 4m-8-4v8m0-8V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v9h2m8 0H9m4 0h2m4 0h2v-4m0 0h-5m3.5 5.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-10 0a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "truck-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v9a1 1 0 0 0 1 1h.535a3.5 3.5 0 1 0 6.93 0h3.07a3.5 3.5 0 1 0 6.93 0H21a1 1 0 0 0 1-1v-4a1 1 0 0 0-.106-.447l-2-4A1 1 0 0 0 19 6h-5a2 2 0 0 0-2-2zm14.192 11.59l.016.02a1.5 1.5 0 1 1-.016-.021Zm-10 0l.016.02a1.5 1.5 0 1 1-.016-.021Zm5.806-5.572v-2.02h4.396l1 2.02z\" clip-rule=\"evenodd\"/>"}, "twitter-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M22 5.892a8.2 8.2 0 0 1-2.355.635a4.07 4.07 0 0 0 1.8-2.235a8.3 8.3 0 0 1-2.605.981A4.13 4.13 0 0 0 15.85 4a4.07 4.07 0 0 0-4.1 4.038q0 .466.105.919A11.7 11.7 0 0 1 3.4 4.734a4.006 4.006 0 0 0 1.268 5.392a4.2 4.2 0 0 1-1.859-.5v.05A4.06 4.06 0 0 0 6.1 13.635a4.2 4.2 0 0 1-1.856.07a4.11 4.11 0 0 0 3.831 2.807A8.36 8.36 0 0 1 2 18.184A11.73 11.73 0 0 0 8.291 20A11.5 11.5 0 0 0 19.964 8.5c0-.177 0-.349-.012-.523A8.1 8.1 0 0 0 22 5.892\" clip-rule=\"evenodd\"/>"}, "undo-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 9h13a5 5 0 0 1 0 10H7M3 9l4-4M3 9l4 4\"/>"}, "undo-solid": {"body": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"><path d=\"M20 9H8a5 5 0 0 0-5 5v0a5 5 0 0 0 5 5h9\"/><path stroke-linejoin=\"round\" d=\"m17 5l4 4l-4 4\"/></g>", "hidden": true}, "upload-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5v9m-5 0H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2M8 9l4-5l4 5m1 8h.01\"/>"}, "upload-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 3a1 1 0 0 1 .78.375l4 5a1 1 0 1 1-1.56 1.25L13 6.85V14a1 1 0 1 1-2 0V6.85L8.78 9.626a1 1 0 1 1-1.56-1.25l4-5A1 1 0 0 1 12 3M9 14v-1H5a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-4v1a3 3 0 1 1-6 0m8 2a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "user-add-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12h4m-2 2v-4M4 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1m8-10a3 3 0 1 1-6 0a3 3 0 0 1 6 0\"/>"}, "user-add-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M9 4a4 4 0 1 0 0 8a4 4 0 0 0 0-8m-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4zm8-1a1 1 0 0 1 1-1h1v-1a1 1 0 1 1 2 0v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0v-1h-1a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "user-circle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 21a9 9 0 1 0 0-18a9 9 0 0 0 0 18m0 0a8.95 8.95 0 0 0 4.951-1.488A3.987 3.987 0 0 0 13 16h-2a3.987 3.987 0 0 0-3.951 3.512A8.95 8.95 0 0 0 12 21m3-11a3 3 0 1 1-6 0a3 3 0 0 1 6 0\"/>"}, "user-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 20a7.97 7.97 0 0 1-5.002-1.756l.002.001v-.683c0-1.794 1.492-3.25 3.333-3.25h3.334c1.84 0 3.333 1.456 3.333 3.25v.683A7.97 7.97 0 0 1 12 20M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10c0 5.5-4.44 9.963-9.932 10h-.138C6.438 21.962 2 17.5 2 12m10-5c-1.84 0-3.333 1.455-3.333 3.25S10.159 13.5 12 13.5c1.84 0 3.333-1.455 3.333-3.25S13.841 7 12 7\" clip-rule=\"evenodd\"/>"}, "user-edit-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"square\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 19H5a1 1 0 0 1-1-1v-1a3 3 0 0 1 3-3h1m4-6a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm7.441 1.559a1.907 1.907 0 0 1 0 2.698l-6.069 6.069L10 19l.674-3.372l6.07-6.07a1.907 1.907 0 0 1 2.697 0Z\"/>"}, "user-edit-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 8a4 4 0 1 1 7.796 1.263l-2.533 2.534A4 4 0 0 1 5 8m4.06 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h2.172a3 3 0 0 1-.114-1.588l.674-3.372a3 3 0 0 1 .82-1.533zm9.032-5a2.9 2.9 0 0 0-2.056.852L9.967 14.92a1 1 0 0 0-.273.51l-.675 3.373a1 1 0 0 0 1.177 1.177l3.372-.675a1 1 0 0 0 .511-.273l6.07-6.07a2.91 2.91 0 0 0-.944-4.742A2.9 2.9 0 0 0 18.092 8\" clip-rule=\"evenodd\"/>"}, "user-graduate-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.614 7.2c.348.49.6 1.154.6 1.8a3 3 0 1 1-5.4-1.8M6.214 6v4m0-4l6-3l6 3l-6 2l-2.4-.8M6.214 6l3.6 1.2m-3.6 12.6v-2.15c0-1.683 1.273-3.308 2.951-3.649l3.05 2.935l3-2.945c1.702.32 3 1.96 3 3.659v2.15c0 .631-.538 1.143-1.2 1.143h-9.6c-.663 0-1.2-.512-1.2-1.143\"/>"}, "user-graduate-solid": {"body": "<g fill=\"currentColor\"><path d=\"M12.447 2.106a1 1 0 0 0-.894 0L5.905 4.93l.377.11l.034.011L12 6.946l5.68-1.894l.393-.133zM5 10V6.748l.7.206l1.3.433V10a1 1 0 1 1-2 0m3-1c0-.424.066-.833.188-1.216l3.496 1.165a1 1 0 0 0 .632 0l3.496-1.165A4 4 0 1 1 8 9\"/><path d=\"M14.3 13.277a1 1 0 0 1 .884-.27C17.38 13.42 19 15.489 19 17.65v2.15c0 1.23-1.031 2.143-2.2 2.143H7.2c-1.168 0-2.2-.914-2.2-2.143v-2.15c0-2.141 1.59-4.19 3.752-4.629a1 1 0 0 1 .892.26l2.35 2.26z\"/></g>"}, "user-headset-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.079 6.839a3 3 0 0 0-4.255.1M13 20h1.083A3.916 3.916 0 0 0 18 16.083V9A6 6 0 1 0 6 9v7m7 4v-1a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1m-7-4v-6H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2zm12-6h1a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-1z\"/>"}, "user-headset-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 2a7 7 0 0 0-7 7a3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h1a1 1 0 0 0 1-1V9a5 5 0 1 1 10 0v7.083A2.92 2.92 0 0 1 14.083 19H14a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h1a2 2 0 0 0 1.732-1h.351a4.92 4.92 0 0 0 4.83-4H19a3 3 0 0 0 3-3v-2a3 3 0 0 0-3-3a7 7 0 0 0-7-7m1.45 3.275a4 4 0 0 0-4.352.976a1 1 0 0 0 1.452 1.376a2 2 0 0 1 2.836-.067a1 1 0 1 0 1.386-1.442a4 4 0 0 0-1.321-.843Z\" clip-rule=\"evenodd\"/>"}, "user-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M7 17v1a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-4a3 3 0 0 0-3 3Zm8-9a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/>"}, "user-remove-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12h4M4 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1m8-10a3 3 0 1 1-6 0a3 3 0 0 1 6 0\"/>"}, "user-remove-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5 8a4 4 0 1 1 8 0a4 4 0 0 1-8 0m-2 9a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2zm13-6a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2z\" clip-rule=\"evenodd\"/>"}, "user-settings-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"square\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19H5a1 1 0 0 1-1-1v-1a3 3 0 0 1 3-3h2m10 1a3 3 0 0 1-3 3m3-3a3 3 0 0 0-3-3m3 3h1m-4 3a3 3 0 0 1-3-3m3 3v1m-3-4a3 3 0 0 1 3-3m-3 3h-1m4-3v-1m-2.121 1.879l-.707-.707m5.656 5.656l-.707-.707m-4.242 0l-.707.707m5.656-5.656l-.707.707M12 8a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/>"}, "user-settings-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M17 10v1.126c.367.095.714.24 1.032.428l.796-.797l1.415 1.415l-.797.796c.188.318.333.665.428 1.032H21v2h-1.126c-.095.367-.24.714-.428 1.032l.797.796l-1.415 1.415l-.796-.797a4 4 0 0 1-1.032.428V20h-2v-1.126a4 4 0 0 1-1.032-.428l-.796.797l-1.415-1.415l.797-.796A4 4 0 0 1 12.126 16H11v-2h1.126c.095-.367.24-.714.428-1.032l-.797-.796l1.415-1.415l.796.797A4 4 0 0 1 15 11.126V10zm.406 3.578l.016.016c.354.358.574.85.578 1.392v.028a2 2 0 0 1-3.409 1.406l-.01-.012a2 2 0 0 1 2.826-2.83ZM5 8a4 4 0 1 1 7.938.703a7.03 7.03 0 0 0-3.235 3.235A4 4 0 0 1 5 8m4.29 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h6.101A6.98 6.98 0 0 1 9 15c0-.695.101-1.366.29-2\" clip-rule=\"evenodd\"/>"}, "user-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 4a4 4 0 1 0 0 8a4 4 0 0 0 0-8m-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4z\" clip-rule=\"evenodd\"/>"}, "users-group-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M4.5 17H4a1 1 0 0 1-1-1a3 3 0 0 1 3-3h1m0-3.05A2.5 2.5 0 1 1 9 5.5M19.5 17h.5a1 1 0 0 0 1-1a3 3 0 0 0-3-3h-1m0-3.05a2.5 2.5 0 1 0-2-4.45m.5 13.5h-7a1 1 0 0 1-1-1a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3a1 1 0 0 1-1 1Zm-1-9.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0Z\"/>"}, "users-group-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M12 6a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7m-1.5 8a4 4 0 0 0-4 4a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2a4 4 0 0 0-4-4zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293a3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2a4 4 0 0 0-4-4h-1.1a5.5 5.5 0 0 1-.471.762A6 6 0 0 1 19.5 18M4 7.5a3.5 3.5 0 0 1 5.477-2.889a5.5 5.5 0 0 0-2.796 6.293A3.5 3.5 0 0 1 4 7.5M7.1 12H6a4 4 0 0 0-4 4a2 2 0 0 0 2 2h.5a6 6 0 0 1 3.071-5.238A5.5 5.5 0 0 1 7.1 12\" clip-rule=\"evenodd\"/>"}, "users-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 19h4a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-2m-2.236-4a3 3 0 1 0 0-4M3 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z\"/>"}, "users-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8 4a4 4 0 1 0 0 8a4 4 0 0 0 0-8m-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4zm7.25-2.095c.478-.86.75-1.85.75-2.905a6 6 0 0 0-.75-2.906a4 4 0 1 1 0 5.811M15.466 20c.34-.588.535-1.271.535-2v-1a5.98 5.98 0 0 0-1.528-4H18a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2z\" clip-rule=\"evenodd\"/>"}, "video-camera-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 6H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1m7 11l-6-2V9l6-2z\"/>"}, "video-camera-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M14 7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2zm2 9.387l4.684 1.562A1 1 0 0 0 22 17V7a1 1 0 0 0-1.316-.949L16 7.613z\" clip-rule=\"evenodd\"/>"}, "visa-solid": {"body": "<g fill=\"currentColor\"><path fill=\"none\" d=\"M17.4 12.6h1l-.3-1.4v-.4l-.2.4z\"/><path fill-rule=\"evenodd\" d=\"M2 6.3c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2zm12.5 3.2c.4 0 .8 0 1.1.2l-.1 1h-.1a2 2 0 0 0-1-.3c-.5 0-.7.3-.7.5s.2.3.7.5c.7.4 1 .8 1 1.3c0 1-.8 1.7-2.2 1.7c-.6 0-1.1-.2-1.4-.3l.2-1h.1c.******* 1.2.3c.4 0 .8-.2.8-.5c0-.2-.2-.3-.7-.6c-.5-.2-1.1-.6-1.1-1.3c0-.9 1-1.5 2.2-1.5m3.5 0h1l1 4.8h-1.2l-.2-.7H17l-.3.7h-1.3l1.9-4.4c.1-.3.3-.3.7-.3Zm-6.2 0h-1.3l-.8 4.8H11zm-4.5 3.3l-.1-.7l-.5-2.2c0-.3-.3-.3-.6-.4h-2v.1l1.2.5l.1.2l1.1 4H8l2-4.7H8.7l-1.3 3.2Z\" clip-rule=\"evenodd\"/></g>"}, "volume-down-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.5 8.43A5 5 0 0 1 19 12a4.98 4.98 0 0 1-1.43 3.5M14 6.135v11.73a1 1 0 0 1-1.64.768L8 15H6a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l4.36-3.633a1 1 0 0 1 1.64.768\"/>"}, "volume-down-solid": {"body": "<g fill=\"currentColor\"><path d=\"M15 6.037c0-1.724-1.978-2.665-3.28-1.562L7.638 7.933H6c-1.105 0-2 .91-2 2.034v4.066c0 1.123.895 2.034 2 2.034h1.638l4.082 3.458c1.302 1.104 3.28.162 3.28-1.562z\"/><path fill-rule=\"evenodd\" d=\"M16.786 7.658a.99.99 0 0 1 1.414-.014A6.14 6.14 0 0 1 20 12c0 1.662-.655 3.17-1.715 4.27a.99.99 0 0 1-1.414.014a1.03 1.03 0 0 1-.014-1.437A4.1 4.1 0 0 0 18 12a4.1 4.1 0 0 0-1.2-2.904a1.03 1.03 0 0 1-.014-1.438\" clip-rule=\"evenodd\"/></g>"}, "volume-mute-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.5 8.43A5 5 0 0 1 17 12c0 1.126-.5 2.5-1.5 3.5m2.864-9.864A8.97 8.97 0 0 1 21 12c0 2.023-.5 4.5-2.5 6M7.8 7.5l2.56-2.133a1 1 0 0 1 1.64.768V12m0 4.5v1.365a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1m1-4l14 14\"/>"}, "volume-mute-solid": {"body": "<path fill=\"currentColor\" d=\"M5.707 4.293a1 1 0 0 0-1.414 1.414l14 14a1 1 0 0 0 1.414-1.414l-.004-.005C21.57 16.498 22 13.938 22 12a9.97 9.97 0 0 0-2.929-7.071a1 1 0 1 0-1.414 1.414A7.97 7.97 0 0 1 20 12c0 1.752-.403 3.636-1.712 4.873l-1.433-1.433C17.616 14.37 18 13.107 18 12c0-1.678-.69-3.197-1.8-4.285a1 1 0 1 0-1.4 1.428A4 4 0 0 1 16 12c0 .606-.195 1.335-.59 1.996L13 11.586V6.135c0-1.696-1.978-2.622-3.28-1.536L7.698 6.284l-1.99-1.991ZM4 8h.586L13 16.414v1.451c0 1.696-1.978 2.622-3.28 1.536L5.638 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2\"/>"}, "volume-up-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.5 8.43A5 5 0 0 1 17 12a4.98 4.98 0 0 1-1.43 3.5m2.794 2.864A8.97 8.97 0 0 0 21 12a8.97 8.97 0 0 0-2.636-6.364M12 6.135v11.73a1 1 0 0 1-1.64.768L6 15H4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h2l4.36-3.633a1 1 0 0 1 1.64.768\"/>"}, "volume-up-solid": {"body": "<g fill=\"currentColor\"><path d=\"M13 6.037c0-1.724-1.978-2.665-3.28-1.562L5.638 7.933H4c-1.105 0-2 .91-2 2.034v4.066c0 1.123.895 2.034 2 2.034h1.638l4.082 3.458c1.302 1.104 3.28.162 3.28-1.562z\"/><path fill-rule=\"evenodd\" d=\"M14.786 7.658a.99.99 0 0 1 1.414-.014A6.14 6.14 0 0 1 18 12c0 1.662-.655 3.17-1.715 4.27a.99.99 0 0 1-1.414.014a1.03 1.03 0 0 1-.014-1.437A4.1 4.1 0 0 0 16 12a4.1 4.1 0 0 0-1.2-2.904a1.03 1.03 0 0 1-.014-1.438\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M17.657 4.811a.99.99 0 0 1 1.414 0A10.22 10.22 0 0 1 22 12c0 2.807-1.12 5.35-2.929 7.189a.99.99 0 0 1-1.414 0a1.03 1.03 0 0 1 0-1.438A8.17 8.17 0 0 0 20 12a8.17 8.17 0 0 0-2.343-5.751a1.03 1.03 0 0 1 0-1.438\" clip-rule=\"evenodd\"/></g>"}, "vue-solid": {"body": "<path fill=\"currentColor\" d=\"M14.5 3L12 7.156L9.857 3H2l10 18L22 3zM4.486 4.5h2.4L12 13.8l5.107-9.3h2.4L12 18.021z\"/>"}, "wallet-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8H5m12 0a1 1 0 0 1 1 1v2.6M17 8l-4-4M5 8a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.6M5 8l4-4l4 4m6 4h-4a2 2 0 1 0 0 4h4a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1\"/>"}, "wallet-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M12 14a3 3 0 0 1 3-3h4a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-4a3 3 0 0 1-3-3m3-1a1 1 0 1 0 0 2h4v-2z\"/><path d=\"M12.293 3.293a1 1 0 0 1 1.414 0L16.414 6h-2.828l-1.293-1.293a1 1 0 0 1 0-1.414M12.414 6L9.707 3.293a1 1 0 0 0-1.414 0L5.586 6zM4.586 7l-.056.055A2 2 0 0 0 3 9v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2h-4a5 5 0 0 1 0-10h4a2 2 0 0 0-1.53-1.945L17.414 7z\"/></g>"}, "wand-magic-sparkles-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16.872 9.687L20 6.56L17.44 4L4 17.44L6.56 20L16.873 9.687Zm0 0l-2.56-2.56M6 7v2m0 0v2m0-2H4m2 0h2m7 7v2m0 0v2m0-2h-2m2 0h2M8 4h.01v.01H8zm2 2h.01v.01H10zm2-2h.01v.01H12zm8 8h.01v.01H20zm-2 2h.01v.01H18zm2 2h.01v.01H20z\"/>"}, "wand-magic-sparkles-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M17.44 3a1 1 0 0 1 .707.293l2.56 2.56a1 1 0 0 1 0 1.414L18.194 9.78L14.22 5.806l2.513-2.513A1 1 0 0 1 17.44 3m-4.634 4.22l-9.513 9.513a1 1 0 0 0 0 1.414l2.56 2.56a1 1 0 0 0 1.414 0l9.513-9.513zM6 6a1 1 0 0 1 1 1v1h1a1 1 0 0 1 0 2H7v1a1 1 0 1 1-2 0v-1H4a1 1 0 0 1 0-2h1V7a1 1 0 0 1 1-1m9 9a1 1 0 0 1 1 1v1h1a1 1 0 1 1 0 2h-1v1a1 1 0 1 1-2 0v-1h-1a1 1 0 1 1 0-2h1v-1a1 1 0 0 1 1-1\" clip-rule=\"evenodd\"/><path d=\"M19 13h-2v2h2zM13 3h-2v2h2zm-2 2H9v2h2zM9 3H7v2h2zm12 8h-2v2h2zm0 4h-2v2h2z\"/></g>"}, "water-bottle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 6v2s-3 1-3 3.25s1 2.25 1 3s-1 1.125-1 2.25V19c0 .938 1 2 2.5 2s2-.937 2-.937s.5.937 2 .937s2.5-1.062 2.5-2v-2.5c0-1.125-1-1.5-1-2.25s1-.75 1-3S14 8 14 8V6m-3 0h-1V3h5v3h-1m-3 0h3m-5.956 6h8.912M8 17h9\"/>"}, "water-bottle-solid": {"body": "<path fill=\"currentColor\" d=\"M9 3a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1zm-.127 5c-.792.573-1.762 1.556-1.864 3H17.99c-.102-1.444-1.072-2.427-1.864-3zm8.882 5H7.245c.151.455.353.78.544 1.04l.129.175l.047.064c-.037.059-.083.118-.157.215l-.103.134c-.226.297-.544.74-.66 1.372h10.91c-.116-.631-.434-1.075-.66-1.372l-.103-.134a3 3 0 0 1-.157-.215l.047-.064l.129-.175c.191-.26.393-.585.544-1.04M18 18H7v1c0 1.611 1.577 3 3.5 3c.87 0 1.529-.247 2-.555c.471.308 1.13.555 2 .555c1.923 0 3.5-1.389 3.5-3z\"/>"}, "whatsapp-solid": {"body": "<g fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M12 4a8 8 0 0 0-6.895 12.06l.569.718l-.697 2.359l2.32-.648l.379.243A8 8 0 1 0 12 4M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10a9.96 9.96 0 0 1-5.016-1.347l-4.948 1.382l1.426-4.829l-.006-.007l-.033-.055A9.96 9.96 0 0 1 2 12\" clip-rule=\"evenodd\"/><path d=\"M16.735 13.492c-.038-.018-1.497-.736-1.756-.83a1 1 0 0 0-.34-.075c-.196 0-.362.098-.49.291c-.146.217-.587.732-.723.886c-.018.02-.042.045-.057.045c-.013 0-.239-.093-.307-.123c-1.564-.68-2.751-2.313-2.914-2.589c-.023-.04-.024-.057-.024-.057c.005-.021.058-.074.085-.101c.08-.079.166-.182.249-.283l.117-.14c.121-.14.175-.25.237-.375l.033-.066a.68.68 0 0 0-.02-.64c-.034-.069-.65-1.555-.715-1.711c-.158-.377-.366-.552-.655-.552c-.027 0 0 0-.112.005c-.137.005-.883.104-1.213.311c-.35.22-.94.924-.94 2.16c0 1.112.705 2.162 1.008 2.561l.041.06c1.161 1.695 2.608 2.951 4.074 3.537c1.412.564 2.081.63 2.461.63c.16 0 .288-.013.4-.024l.072-.007c.488-.043 1.56-.599 1.804-1.276c.192-.534.243-1.117.115-1.329c-.088-.144-.239-.216-.43-.308\"/></g>"}, "wheat-exclamation-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.54 15.46c-.155.05-.83-1.917-.898-2.362c-.088-.569.031-1.77.15-1.796c.12-.027.687 1.24.756 1.686c.07.444.147 2.421-.008 2.472m0 0c-.008-.163 2.067-.305 2.514-.257c.572.062 1.701.488 1.696.61c-.005.123-1.376.343-1.824.294c-.447-.048-2.377-.484-2.386-.647m3.715-3.715L4 20m8.255-8.255l6.604-6.604m-6.604 6.604c-.155.05-.83-1.917-.898-2.362c-.089-.569.03-1.77.15-1.796s.687 1.241.756 1.686c.07.445.147 2.422-.008 2.472m0 0c-.008-.163 2.066-.305 2.514-.256c.572.062 1.701.488 1.696.61c-.005.123-1.377.342-1.824.294s-2.378-.485-2.386-.648M20 8.953l-1.271 1.271M15.047 4l-1.271 1.271M19.99 20H20m-.01-3v-3M5.27 18.73c-.156.05-.83-1.917-.899-2.361c-.088-.57.031-1.77.151-1.797s.687 1.242.756 1.686c.069.445.147 2.422-.008 2.472m0 0c-.009-.163 2.066-.304 2.513-.256c.573.062 1.702.488 1.696.61c-.005.123-1.376.343-1.824.294c-.447-.048-2.377-.484-2.385-.647\"/>"}, "wheat-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.54 15.46c-.155.05-.83-1.917-.898-2.362c-.088-.569.031-1.77.15-1.796c.12-.027.687 1.24.756 1.686c.07.444.147 2.421-.008 2.472m0 0c-.008-.163 2.067-.305 2.514-.257c.572.062 1.701.488 1.696.61c-.005.123-1.376.343-1.824.294c-.447-.048-2.377-.484-2.386-.647m3.715-3.715L4 20m8.255-8.255l6.604-6.604m-6.604 6.604c-.155.05-.83-1.917-.898-2.362c-.089-.569.03-1.77.15-1.796s.687 1.241.756 1.686c.07.445.147 2.422-.008 2.472m0 0c-.008-.163 2.066-.305 2.514-.256c.572.062 1.701.488 1.696.61c-.005.123-1.377.342-1.824.294s-2.378-.485-2.386-.648M20 8.953l-1.271 1.271M15.047 4l-1.271 1.271M5.27 18.731c-.156.05-.83-1.918-.899-2.362c-.088-.57.031-1.77.151-1.797s.687 1.242.756 1.686c.069.445.147 2.422-.008 2.472m0 0c-.009-.164 2.066-.305 2.513-.257c.573.062 1.702.488 1.696.61c-.005.123-1.376.343-1.824.294c-.447-.048-2.377-.484-2.385-.647\"/>"}, "whiskey-glass-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 14h10M6 7l.847 10.166A2 2 0 0 0 8.84 19h6.32a2 2 0 0 0 1.993-1.834L18 7z\"/>"}, "whiskey-glass-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M6 6a1 1 0 0 0-.997 1.083l.848 10.166A3 3 0 0 0 8.84 20h6.319a3 3 0 0 0 2.99-2.75l.847-10.167A1 1 0 0 0 18 6zm1.503 7l-.416-5h9.826l-.417 5z\" clip-rule=\"evenodd\"/>"}, "window-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 8h.01M9 8h.01M12 8h.01M4 11h16M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1\"/>"}, "window-restore-solid": {"body": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M8 5a1 1 0 0 1 1-1h11a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-1a1 1 0 1 1 0-2h1V6H9a1 1 0 0 1-1-1\"/><path d=\"M4 7a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2zm0 11v-5.5h11V18z\"/></g>"}, "window-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm16 7H4v7h16zM5 8a1 1 0 0 1 1-1h.01a1 1 0 0 1 0 2H6a1 1 0 0 1-1-1m4-1a1 1 0 0 0 0 2h.01a1 1 0 0 0 0-2zm2 1a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1\" clip-rule=\"evenodd\"/>"}, "windows-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3.005 12L3 6.408l6.8-.923v6.517H3.005ZM11 5.32L19.997 4v8H11zM20.067 13l-.069 8l-9.065-1.275L11 13zM9.8 19.58l-6.795-.931V13H9.8z\" clip-rule=\"evenodd\"/>"}, "wine-bottle-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 8h4m-3-5a1 1 0 0 0-1 1v6c-2 1-2 2.5-2 4.5V20a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-5.5c0-2 0-3.5-2-4.5V4a1 1 0 0 0-1-1zm5 10h-4v5h4z\"/>"}, "wine-bottle-solid": {"body": "<g fill=\"currentColor\"><path d=\"M9 4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v3H9zm0 5v.42c-.878.565-1.412 1.28-1.699 2.139C7 12.465 7 13.482 7 14.41V20a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h-5a1 1 0 0 1-1-1v-5a1 1 0 0 1 1-1h4.821a5 5 0 0 0-.122-.441c-.287-.86-.821-1.574-1.699-2.14V9z\"/><path d=\"M17 14h-4v3h4z\"/></g>"}, "wine-glass-empty-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 14c2.761 0 5-2.668 5-5.43c0-1.385-1.12-4.507-1.5-5.57h-7C8.152 4.021 7 7.172 7 8.57C7 11.333 9.239 14 12 14m0 0v7m-3 0h6\"/>"}, "wine-glass-empty-solid": {"body": "<path fill=\"currentColor\" d=\"M8.5 2a1 1 0 0 0-.947.677l-.128.373a60 60 0 0 0-.762 2.303C6.338 6.422 6 7.699 6 8.57c0 2.884 2.092 5.783 5 6.335V20H9a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-2v-5.094c2.908-.552 5-3.451 5-6.335c0-.863-.328-2.127-.65-3.195a56 56 0 0 0-.907-2.71l-.002-.003A1 1 0 0 0 15.5 2z\"/>"}, "wine-glass-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 14c2.761 0 5-2.668 5-5.43c0-1.385-1.12-4.507-1.5-5.57h-7C8.152 4.021 7 7.172 7 8.57C7 11.333 9.239 14 12 14m0 0v7m0 0H9m3 0h3M7 9h9\"/>"}, "wine-glass-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M8.5 2a1 1 0 0 0-.947.677l-.128.373a60 60 0 0 0-.762 2.303C6.338 6.422 6 7.699 6 8.57q0 .152.008.304a1 1 0 0 0 .035.415c.311 2.632 2.291 5.11 4.957 5.616V20H9a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-2v-5.094c2.908-.552 5-3.451 5-6.335c0-.863-.328-2.127-.65-3.195a56 56 0 0 0-.907-2.71l-.002-.003A1 1 0 0 0 15.5 2zm-.443 6h7.887c-.088-.542-.273-1.266-.509-2.047A47 47 0 0 0 14.794 4H9.212c-.185.536-.42 1.223-.636 1.935c-.24.788-.429 1.519-.519 2.065\" clip-rule=\"evenodd\"/>"}, "x-circle-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M2 12a10 10 0 1 1 20 0a10 10 0 0 1-20 0m7.7-3.7a1 1 0 0 0-1.4 1.4l2.3 2.3l-2.3 2.3a1 1 0 1 0 1.4 1.4l2.3-2.3l2.3 2.3a1 1 0 0 0 1.4-1.4L13.4 12l2.3-2.3a1 1 0 0 0-1.4-1.4L12 10.6z\" clip-rule=\"evenodd\"/>", "hidden": true}, "x-company-solid": {"body": "<path fill=\"currentColor\" d=\"M13.8 10.5L20.7 2h-3l-5.3 6.5L7.7 2H1l7.8 11l-7.3 9h3l5.7-7l5.1 7H22zm-2.4 3l-1.4-2l-5.6-7.9h2.3l4.5 6.3l1.4 2l6 8.5h-2.3l-4.9-7Z\"/>", "hidden": true}, "x-solid": {"body": "<path fill=\"currentColor\" d=\"M13.795 10.533L20.68 2h-3.073l-5.255 6.517L7.69 2H1l7.806 10.91L1.47 22h3.074l5.705-7.07L15.31 22H22zm-2.38 2.95L9.97 11.464L4.36 3.627h2.31l4.528 6.317l1.443 2.02l6.018 8.409h-2.31z\"/>"}, "youtube-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M21.7 8.037a4.26 4.26 0 0 0-.789-1.964a2.84 2.84 0 0 0-1.984-.839c-2.767-.2-6.926-.2-6.926-.2s-4.157 0-6.928.2a2.84 2.84 0 0 0-1.983.839a4.2 4.2 0 0 0-.79 1.965a30 30 0 0 0-.2 3.206v1.5a30 30 0 0 0 .2 3.206c.094.712.364 1.39.784 1.972c.604.536 1.38.837 2.187.848c1.583.151 6.731.2 6.731.2s4.161 0 6.928-.2a2.84 2.84 0 0 0 1.985-.84a4.3 4.3 0 0 0 .787-1.965a30 30 0 0 0 .2-3.206v-1.516a31 31 0 0 0-.202-3.206m-11.692 6.554v-5.62l5.4 2.819z\" clip-rule=\"evenodd\"/>"}, "zoom-in-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m21 21l-3.5-3.5M10 7v6m-3-3h6m4 0a7 7 0 1 1-14 0a7 7 0 0 1 14 0Z\"/>"}, "zoom-in-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M21.707 21.707a1 1 0 0 1-1.414 0l-3.5-3.5a1 1 0 0 1 1.414-1.414l3.5 3.5a1 1 0 0 1 0 1.414M2 10a8 8 0 1 1 16 0a8 8 0 0 1-16 0m9-3a1 1 0 1 0-2 0v2H7a1 1 0 0 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2z\" clip-rule=\"evenodd\"/>"}, "zoom-out-outline": {"body": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m21 21l-3.5-3.5M7 10h6m4 0a7 7 0 1 1-14 0a7 7 0 0 1 14 0Z\"/>"}, "zoom-out-solid": {"body": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M21.707 21.707a1 1 0 0 1-1.414 0l-3.5-3.5a1 1 0 0 1 1.414-1.414l3.5 3.5a1 1 0 0 1 0 1.414M2 10a8 8 0 1 1 16 0a8 8 0 0 1-16 0m4 0a1 1 0 0 0 1 1h6a1 1 0 1 0 0-2H7a1 1 0 0 0-1 1\" clip-rule=\"evenodd\"/>"}}, "aliases": {"adress-book-outline": {"parent": "address-book-outline"}, "adress-book-solid": {"parent": "address-book-solid"}, "angle-down-solid": {"parent": "angle-down-outline"}, "angle-left-solid": {"parent": "angle-left-outline"}, "angle-right-solid": {"parent": "angle-right-outline"}, "angle-top-solid": {"parent": "angle-up-outline"}, "bars-from-left-solid": {"parent": "bars-from-left-outline"}, "bars-solid": {"parent": "bars-outline"}, "chart-mixed-solid": {"parent": "chart-mixed-outline"}, "chevron-double-down-solid": {"parent": "chevron-double-down-outline"}, "chevron-double-left-solid": {"parent": "chevron-double-left-outline"}, "chevron-double-right-solid": {"parent": "chevron-double-right-outline"}, "chevron-double-up-solid": {"parent": "chevron-double-up-outline"}, "chevron-down-solid": {"parent": "chevron-down-outline"}, "chevron-left-solid": {"parent": "chevron-left-outline"}, "chevron-right-solid": {"parent": "chevron-right-outline"}, "chevron-sort-solid": {"parent": "chevron-sort-outline"}, "chevron-up-solid": {"parent": "chevron-up-outline"}, "circle-check-solid": {"parent": "check-circle-solid"}, "compress-solid": {"parent": "compress-outline"}, "dna-solid": {"parent": "truck-solid"}, "face-laughz-solid": {"parent": "face-laugh-solid"}, "grid-24x24px-outline": {"parent": "pause-outline"}, "minus-solid": {"parent": "minus-outline"}, "paper-clip-solid": {"parent": "paper-clip-outline"}, "papper-plane-outline": {"parent": "paper-plane-outline"}, "plus-solid": {"parent": "plus-outline"}, "x-circle-outline": {"parent": "close-circle-outline"}, "x-outline": {"parent": "close-outline"}}, "categories": {"Arrows": ["angle-down-outline", "angle-left-outline", "angle-right-outline", "angle-up-outline", "arrow-down-outline", "arrow-down-to-bracket-outline", "arrow-left-outline", "arrow-left-to-bracket-outline", "arrow-right-alt-outline", "arrow-right-alt-solid", "arrow-right-outline", "arrow-right-to-bracket-outline", "arrow-sort-letters-outline", "arrow-up-down-outline", "arrow-up-from-bracket-outline", "arrow-up-outline", "arrow-up-right-down-left-outline", "arrows-repeat-count-outline", "arrows-repeat-outline", "caret-down-outline", "caret-down-solid", "caret-left-outline", "caret-left-solid", "caret-right-outline", "caret-right-solid", "caret-sort-outline", "caret-sort-solid", "caret-up-outline", "caret-up-solid", "chevron-double-down-outline", "chevron-double-left-outline", "chevron-double-right-outline", "chevron-double-up-outline", "chevron-down-outline", "chevron-left-outline", "chevron-right-outline", "chevron-sort-outline", "chevron-up-outline", "compress-outline", "expand-outline", "forward-outline", "forward-solid", "minimize-outline", "redo-outline", "refresh-outline", "reply-all-outline", "reply-all-solid", "reply-outline", "reply-solid", "share-all-outline", "share-all-solid", "sort-horizontal-outline", "sort-outline", "undo-outline"], "Brands": ["apple-solid", "aws-solid", "bitcoin-solid", "css-solid", "discord-solid", "dribbble-solid", "dropbox-solid", "facebook-solid", "flowbite-solid", "github-solid", "gitlab-solid", "google-solid", "html-solid", "instagram-solid", "laravel-solid", "linkedin-solid", "mastercard-solid", "mongo-db-solid", "npm-solid", "react-solid", "reddit-solid", "stackoverflow-solid", "tailwind-solid", "twitter-solid", "visa-solid", "vue-solid", "whatsapp-solid", "windows-solid", "x-solid", "youtube-solid"], "E-commerce": ["cart-outline", "cart-plus-alt-outline", "cart-plus-alt-solid", "cart-plus-outline", "cart-plus-solid", "cart-solid", "cash-outline", "cash-register-outline", "cash-register-solid", "cash-solid", "credit-card-outline", "credit-card-plus-alt-outline", "credit-card-plus-alt-solid", "credit-card-plus-outline", "credit-card-plus-solid", "credit-card-solid", "dollar-outline", "euro-outline", "filter-dollar-outline", "filter-dollar-solid", "receipt-outline", "receipt-solid", "sale-percent-outline", "sale-percent-solid", "scale-balanced-outline", "scale-balanced-solid", "shopping-bag-outline", "shopping-bag-solid", "store-outline", "store-solid", "tag-outline", "tag-solid", "user-headset-outline", "user-headset-solid", "wallet-outline", "wallet-solid"], "Education": ["book-open-reader-outline", "book-open-reader-solid", "chalkboard-outline", "chalkboard-solid", "chalkboard-user-outline", "chalkboard-user-solid", "graduation-cap-outline", "graduation-cap-solid", "laptop-code-outline", "laptop-code-solid", "laptop-file-outline", "laptop-file-solid", "microscope-outline", "microscope-solid", "person-chalkboard-outline", "person-chalkboard-solid", "school-alt-outline", "school-alt-solid", "school-check-alt-outline", "school-check-alt-solid", "school-check-outline", "school-check-solid", "school-exclamation-alt-outline", "school-exclamation-alt-solid", "school-exclamation-outline", "school-exclamation-solid", "school-flag-alt-outline", "school-flag-alt-solid", "school-flag-outline", "school-flag-solid", "school-lock-alt-outline", "school-lock-alt-solid", "school-lock-outline", "school-lock-solid", "school-outline", "school-solid", "school-xmark-alt-outline", "school-xmark-alt-solid", "school-xmark-outline", "school-xmark-solid", "shapes-outline", "shapes-solid", "theatre-outline", "theatre-solid", "user-graduate-outline", "user-graduate-solid"], "Emoji": ["face-explode-outline", "face-explode-solid", "face-grin-outline", "face-grin-solid", "face-grin-stars-outline", "face-grin-stars-solid", "face-laugh-outline", "face-laugh-solid", "thumbs-down-outline", "thumbs-down-solid", "thumbs-up-outline", "thumbs-up-solid"], "Files Folders": ["archive-arrow-down-outline", "archive-arrow-down-solid", "clipboard-check-outline", "clipboard-check-solid", "clipboard-clean-outline", "clipboard-clean-solid", "clipboard-list-outline", "clipboard-list-solid", "clipboard-outline", "clipboard-solid", "file-chart-bar-outline", "file-chart-bar-solid", "file-check-outline", "file-check-solid", "file-circle-plus-outline", "file-circle-plus-solid", "file-clone-outline", "file-clone-solid", "file-code-outline", "file-code-solid", "file-copy-alt-outline", "file-copy-alt-solid", "file-copy-outline", "file-copy-solid", "file-csv-outline", "file-csv-solid", "file-doc-outline", "file-doc-solid", "file-export-outline", "file-export-solid", "file-image-outline", "file-image-solid", "file-import-outline", "file-import-solid", "file-invoice-outline", "file-invoice-solid", "file-lines-outline", "file-lines-solid", "file-music-outline", "file-music-solid", "file-outline", "file-paste-outline", "file-paste-solid", "file-pdf-outline", "file-pdf-solid", "file-pen-outline", "file-pen-solid", "file-ppt-outline", "file-ppt-solid", "file-search-outline", "file-search-solid", "file-shield-outline", "file-shield-solid", "file-solid", "file-video-outline", "file-video-solid", "file-word-outline", "file-word-solid", "file-zip-outline", "file-zip-solid", "folder-arrow-right-outline", "folder-arrow-right-solid", "folder-duplicate-outline", "folder-duplicate-solid", "folder-open-outline", "folder-open-solid", "folder-outline", "folder-plus-outline", "folder-plus-solid", "folder-solid"], "Food Beverage": ["apple-full-outline", "apple-full-solid", "bacon-outline", "bacon-solid", "beer-mug-empty-outline", "beer-mug-empty-solid", "bone-outline", "bone-solid", "bowl-food-outline", "bowl-food-solid", "bowl-rice-outline", "bowl-rice-solid", "bread-slice-outline", "bread-slice-solid", "burger-outline", "burger-solid", "cake-candles-outline", "cake-candles-solid", "candy-cane-outline", "candy-cane-solid", "carrot-outline", "carrot-solid", "champagne-glasses-outline", "champagne-glasses-solid", "cheese-outline", "cheese-solid", "cloud-meatball-outline", "cloud-meatball-solid", "cookie-outline", "cookie-solid", "cube-solid", "cubes-stacked-outline", "cubes-stacked-solid", "droplet-bottle-alt-outline", "droplet-bottle-alt-solid", "droplet-bottle-outline", "droplet-bottle-solid", "drumstick-bite-outline", "drumstick-bite-solid", "egg-outline", "egg-solid", "fish-alt-outline", "fish-alt-solid", "fish-outline", "fish-solid", "flask-outline", "flask-solid", "glass-water-droplet-outline", "glass-water-droplet-solid", "glass-water-outline", "glass-water-solid", "hotdog-outline", "hotdog-solid", "icecream-alt-outline", "icecream-alt-solid", "icecream-outline", "icecream-solid", "jar-outline", "jar-solid", "jar-wheat-outline", "jar-wheat-solid", "lemon-outline", "lemon-solid", "martini-glass-citrus-outline", "martini-glass-citrus-solid", "martini-glass-empty-outline", "martini-glass-empty-solid", "martini-glass-outline", "martini-glass-solid", "mug-hot-outline", "mug-hot-solid", "mug-saucer-outline", "mug-saucer-solid", "pepper-hot-outline", "pepper-hot-solid", "pizza-slice-outline", "pizza-slice-solid", "plate-wheat-outline", "plate-wheat-solid", "seedling-outline", "seedling-solid", "shrimp-outline", "shrimp-solid", "stroopwafel-outline", "stroopwafel-solid", "water-bottle-outline", "water-bottle-solid", "wheat-exclamation-outline", "wheat-outline", "whiskey-glass-outline", "whiskey-glass-solid", "wine-bottle-outline", "wine-bottle-solid", "wine-glass-empty-outline", "wine-glass-empty-solid", "wine-glass-outline", "wine-glass-solid"], "General": ["add-column-after-outline", "add-column-before-outline", "adjustments-horizontal-outline", "adjustments-horizontal-solid", "adjustments-vertical-outline", "adjustments-vertical-solid", "annotation-outline", "annotation-solid", "api-key-outline", "archive-outline", "archive-solid", "arrow-up-right-from-square-outline", "arrow-up-right-from-square-solid", "atom-outline", "award-outline", "award-solid", "badge-check-outline", "badge-check-solid", "ban-outline", "barcode-outline", "bars-from-left-outline", "bars-outline", "battery-outline", "battery-solid", "bed-outline", "bed-solid", "bell-active-alt-outline", "bell-active-alt-solid", "bell-active-outline", "bell-active-solid", "bell-outline", "bell-ring-outline", "bell-ring-solid", "bell-solid", "blender-phone-outline", "blender-phone-solid", "book-open-outline", "book-open-solid", "book-outline", "book-solid", "bookmark-outline", "bookmark-solid", "booth-curtain-outline", "booth-curtain-solid", "brain-outline", "brain-solid", "briefcase-outline", "briefcase-solid", "bug-outline", "bug-solid", "building-outline", "building-solid", "bullhorn-outline", "bullhorn-solid", "calendar-edit-outline", "calendar-edit-solid", "calendar-month-outline", "calendar-month-solid", "calendar-plus-outline", "calendar-plus-solid", "calendar-week-outline", "calendar-week-solid", "cell-attributes-outline", "chart-line-down-outline", "chart-line-up-outline", "chart-mixed-dollar-outline", "chart-mixed-dollar-solid", "chart-mixed-outline", "chart-outline", "chart-pie-outline", "chart-pie-solid", "check-circle-outline", "check-circle-solid", "check-outline", "check-plus-circle-outline", "check-plus-circle-solid", "circle-minus-outline", "circle-minus-solid", "circle-plus-outline", "circle-plus-solid", "clock-arrow-outline", "clock-outline", "clock-solid", "close-circle-outline", "close-circle-solid", "close-outline", "close-sidebar-alt-outline", "close-sidebar-alt-solid", "close-sidebar-outline", "close-sidebar-solid", "cloud-arrow-up-outline", "cloud-arrow-up-solid", "code-branch-outline", "code-branch-solid", "code-fork-outline", "code-fork-solid", "code-merge-outline", "code-merge-solid", "code-outline", "code-pull-request-outline", "code-pull-request-solid", "cog-outline", "cog-solid", "column-outline", "column-solid", "command-outline", "database-outline", "database-solid", "delete-column-outline", "delete-row-outline", "delete-table-outline", "desktop-pc-outline", "desktop-pc-solid", "dna-outline", "dots-horizontal-outline", "dots-vertical-outline", "download-outline", "download-solid", "draw-square-outline", "draw-square-solid", "edit-outline", "edit-solid", "envelope-open-outline", "envelope-open-solid", "envelope-outline", "envelope-solid", "exclamation-circle-outline", "exclamation-circle-solid", "eye-outline", "eye-slash-outline", "eye-slash-solid", "eye-solid", "filter-outline", "filter-solid", "fingerprint-outline", "fire-outline", "fire-solid", "fix-tables-outline", "flag-outline", "flag-solid", "floppy-disk-alt-outline", "floppy-disk-alt-solid", "floppy-disk-outline", "floppy-disk-solid", "gift-box-outline", "gift-box-solid", "globe-outline", "globe-solid", "go-to-next-cell-outline", "go-to-prev-cell-outline", "grid-outline", "grid-plus-outline", "grid-plus-solid", "grid-solid", "hammer-outline", "hammer-solid", "heart-outline", "heart-solid", "home-outline", "home-solid", "hourglass-outline", "hourglass-solid", "image-outline", "image-solid", "inbox-full-outline", "inbox-full-solid", "inbox-outline", "inbox-solid", "info-circle-outline", "info-circle-solid", "insert-row-after-outline", "insert-row-before-outline", "insert-table-alt-outline", "insert-table-outline", "keyboard-outline", "keyboard-solid", "label-outline", "label-solid", "landmark-outline", "landmark-solid", "layers-outline", "layers-solid", "life-saver-outline", "life-saver-solid", "lightbulb-outline", "lightbulb-solid", "link-break-outline", "link-outline", "lock-open-outline", "lock-open-solid", "lock-outline", "lock-solid", "lock-time-outline", "lock-time-solid", "mail-box-outline", "mail-box-solid", "map-pin-alt-outline", "map-pin-alt-solid", "map-pin-outline", "map-pin-solid", "merge-cells-outline", "merge-or-split-outline", "message-caption-outline", "message-caption-solid", "message-dots-outline", "message-dots-solid", "messages-outline", "messages-solid", "minus-outline", "mobile-phone-outline", "mobile-phone-solid", "newspaper-outline", "newspaper-solid", "objects-column-outline", "objects-column-solid", "open-door-outline", "open-door-solid", "open-sidebar-alt-outline", "open-sidebar-alt-solid", "open-sidebar-outline", "open-sidebar-solid", "palette-outline", "palette-solid", "paper-clip-outline", "paper-plane-outline", "paper-plane-solid", "pen-nib-outline", "pen-nib-solid", "pen-outline", "pen-solid", "phone-hangup-outline", "phone-hangup-solid", "phone-outline", "phone-solid", "plus-outline", "printer-outline", "printer-solid", "qr-code-outline", "question-circle-outline", "question-circle-solid", "restore-window-outline", "rocket-outline", "rocket-solid", "ruler-combined-outline", "ruler-combined-solid", "search-outline", "search-solid", "server-outline", "server-solid", "share-nodes-outline", "share-nodes-solid", "shield-check-outline", "shield-check-solid", "shield-outline", "shield-solid", "split-cells-outline", "star-half-outline", "star-half-solid", "star-half-stroke-outline", "star-half-stroke-solid", "star-outline", "star-solid", "swatchbook-outline", "swatchbook-solid", "t-shirt-outline", "t-shirt-solid", "table-column-outline", "table-column-solid", "table-row-outline", "table-row-solid", "tablet-outline", "tablet-solid", "teddy-bear-outline", "teddy-bear-solid", "terminal-outline", "terminal-solid", "thumbtack-outline", "thumbtack-solid", "ticket-outline", "ticket-solid", "toggle-header-cell-outline", "toggle-header-column-outline", "toggle-header-row-outline", "tools-outline", "tracking-outline", "tracking-solid", "trash-bin-outline", "trash-bin-solid", "truck-clock-outline", "truck-clock-solid", "truck-outline", "truck-solid", "upload-outline", "upload-solid", "volume-down-outline", "volume-down-solid", "volume-up-outline", "volume-up-solid", "wand-magic-sparkles-outline", "wand-magic-sparkles-solid", "window-outline", "window-restore-solid", "window-solid", "zoom-in-outline", "zoom-in-solid", "zoom-out-outline", "zoom-out-solid"], "Media": ["backward-step-outline", "backward-step-solid", "camera-photo-outline", "camera-photo-solid", "caption-outline", "caption-solid", "circle-pause-outline", "circle-pause-solid", "clapperboard-play-outline", "clapperboard-play-solid", "computer-speaker-outline", "computer-speaker-solid", "forward-step-outline", "forward-step-solid", "headphones-outline", "headphones-solid", "incoming-call-outline", "incoming-call-solid", "list-music-outline", "list-music-solid", "microphone-outline", "microphone-slash-outline", "microphone-slash-solid", "microphone-solid", "missed-call-outline", "missed-call-solid", "music-alt-outline", "music-alt-solid", "music-outline", "music-solid", "outgoing-call-outline", "outgoing-call-solid", "pause-outline", "pause-solid", "play-outline", "play-solid", "rectangle-list-outline", "rectangle-list-solid", "shuffle-outline", "stop-outline", "stop-solid", "video-camera-outline", "video-camera-solid", "volume-mute-outline", "volume-mute-solid"], "Text": ["align-center-outline", "align-justify-outline", "align-left-outline", "align-right-outline", "font-color-alt-solid", "font-color-outline", "font-family-outline", "font-highlight-outline", "horizontal-lines-outline", "indent-outline", "indent-solid", "language-outline", "letter-bold-outline", "letter-italic-outline", "letter-underline-outline", "list-outline", "ordered-list-outline", "outdent-outline", "outdent-solid", "paragraph-outline", "paragraph-solid", "quote-outline", "quote-solid", "subscript-outline", "superscript-outline", "text-size-outline", "text-slash-outline", "text-underline-outline"], "User": ["address-book-outline", "address-book-solid", "profile-card-outline", "profile-card-solid", "user-add-outline", "user-add-solid", "user-circle-outline", "user-circle-solid", "user-edit-outline", "user-edit-solid", "user-outline", "user-remove-outline", "user-remove-solid", "user-settings-outline", "user-settings-solid", "user-solid", "users-group-outline", "users-group-solid", "users-outline", "users-solid"], "Weather": ["moon-outline", "moon-plus-outline", "moon-plus-solid", "moon-solid", "sun-outline", "sun-solid"]}, "suffixes": {"outline": "Outline", "solid": "Solid"}, "width": 24, "height": 24}