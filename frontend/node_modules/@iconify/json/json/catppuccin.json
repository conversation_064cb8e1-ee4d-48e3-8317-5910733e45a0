{"prefix": "<PERSON><PERSON><PERSON><PERSON>", "info": {"name": "Cat<PERSON>uccin Icons", "total": 560, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/catppuccin/vscode-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/catppuccin/vscode-icons/blob/main/LICENSE"}, "samples": ["folder", "nuxt", "vscode", "amber", "stackblitz", "vercel"], "height": 16, "category": "Programming", "palette": true}, "lastModified": **********, "icons": {"adobe-ae": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 4.389v7.222a2.89 2.89 0 0 1-2.889 2.889H4.39a2.89 2.89 0 0 1-2.89-2.889V4.39A2.89 2.89 0 0 1 4.389 1.5h7.222A2.89 2.89 0 0 1 14.5 4.389\"/><path d=\"M9.499 8.667V7.89a1.5 1.555 0 0 1 1.499-1.555v0a1.5 1.555 0 0 1 1.5 1.555v.777Zm0 0v.778A1.5 1.555 0 0 0 10.998 11h1.125M3.5 11l.843-2.333M8 11l-.843-2.333m-2.812 0L5.75 4.78l1.406 3.888m-2.812 0h2.812\"/></g>"}, "adobe-ai": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 4.389v7.222a2.89 2.89 0 0 1-2.889 2.889H4.39a2.89 2.89 0 0 1-2.89-2.889V4.39A2.89 2.89 0 0 1 4.389 1.5h7.222A2.89 2.89 0 0 1 14.5 4.389M11 8v3m0-5.007V6\"/><path d=\"m4.5 11l.844-2.25M9 11l-.844-2.25m-2.812 0L6.75 5l1.406 3.75m-2.812 0h2.812\"/></g>"}, "adobe-id": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 4.389v7.222a2.89 2.89 0 0 1-2.889 2.889H4.39a2.89 2.89 0 0 1-2.89-2.889V4.39A2.89 2.89 0 0 1 4.389 1.5h7.222A2.89 2.89 0 0 1 14.5 4.389M5.5 5v6\"/><path d=\"M10.5 8v2.571a.44.44 0 0 1-.45.429H9c-.828 0-1.5-.64-1.5-1.429v0C7.5 8.782 8.172 8 9 8Zm0 0V6\"/></g>"}, "adobe-ps": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 4.389v7.222a2.89 2.89 0 0 1-2.889 2.889H4.39a2.89 2.89 0 0 1-2.89-2.889V4.39A2.89 2.89 0 0 1 4.389 1.5h7.222A2.89 2.89 0 0 1 14.5 4.389\"/><path d=\"M4.5 11V8m0 0V5h1.4a1.4 1.5 0 0 1 1.4 1.5v0A1.4 1.5 0 0 1 5.9 8Zm7-.75c-.214-.46-.653-.75-1.133-.75H10.1a1.05 1.125 0 0 0-1.05 1.125v0A1.05 1.125 0 0 0 10.1 8.75h.35a1.05 1.125 0 0 1 1.05 1.125v0A1.05 1.125 0 0 1 10.45 11h-.267a1.266 1.357 0 0 1-1.133-.75v0\"/></g>"}, "adobe-xd": {"body": "<g fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 4.389v7.222a2.89 2.89 0 0 1-2.889 2.889H4.39a2.89 2.89 0 0 1-2.89-2.889V4.39A2.89 2.89 0 0 1 4.389 1.5h7.222A2.89 2.89 0 0 1 14.5 4.389M4.483 5l3.024 6m-3.024 0l3.024-6\"/><path d=\"M11.501 8.143v2.428a.375.429 0 0 1-.375.429h-.875A1.25 1.429 0 0 1 9 9.571v0a1.25 1.429 0 0 1 1.25-1.428zm0 0V6\"/></g>"}, "adonis": {"body": "<g fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 1.5c5.2 0 6.5 1.3 6.5 6.5s-1.3 6.5-6.5 6.5S1.5 13.2 1.5 8S2.8 1.5 8 1.5\"/><path d=\"M8 10.89c1.08 0 1.44.36 2.27.66a1 1 0 0 0 1.28-.55a.87.87 0 0 0-.03-.71l-2.68-5.4a.96.96 0 0 0-1.68 0l-2.68 5.4c-.23.47-.01 1.02.5 1.24c.23.1.5.12.75.02c.83-.3 1.19-.66 2.27-.66\"/></g>"}, "alex": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 1.5h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2\"/><path d=\"M5.5 12.5h5m-5-2h5m-3.75-7H8c.79 0 1.5.71 1.5 1.5v3.5h-2c-.79 0-1-1-1-1.5s.21-1.5 1-1.5h2\"/></g>"}, "amber": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m4 5.5l4-4l4 4v5l-4 4l-4-4z\"/>"}, "android": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 6.5v5m13-5v5m-11-6h9V12c0 .83-.67 1.5-1.5 1.5H5A1.5 1.5 0 0 1 3.5 12Zm9 0c0-2.49-2.01-4-4.5-4s-4.5 1.51-4.5 4m1-5l1 1.5m6-1.5l-1 1.5m-5 11.5v2m5-2v2\"/>"}, "angular": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "angular-component": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "angular-directive": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#c6a0f6\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "angular-guard": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "angular-pipe": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8bd5ca\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "angular-service": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z\"/><path stroke=\"#cad3f5\" d=\"m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635\"/></g>"}, "ansible-lint": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 10.25L7 2.75l3.25 7.5L5.5 6.5m7.868 1.81A6.5 6.5 0 1 0 7 13.5\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 11.9l2.5 3.6l4.5-6\"/>"}, "antlr": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.5 8a6.5 6.5 0 1 0 13 0a6.5 6.5 0 0 0-13 0\"/><path d=\"m8 9.5l3 1l-3-6l-3 6\"/></g>"}, "apache": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M2.5 14.5a24.3 24.3 0 0 1 1.63-4.36c.21-.42.45-.84.7-1.26c2.75-4.61 6.63-6.8 8.67-7.38c-.8 3.52-3.91 10.46-10.15 10.4\"/><path stroke=\"#f5a97f\" d=\"M6.14 6.96C8.7 3.64 11.76 1.99 13.5 1.5a18.5 18.5 0 0 1-2.27 5.46\"/><path stroke=\"#ed8796\" d=\"M6.5 9.5h-2m1.25-2h2.71\"/></g>"}, "api-blueprint": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.5 12.5a2 2 0 1 1-4 0a2 2 0 0 1 4 0m9 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0m-4.5-9a2 2 0 1 1-4 0a2 2 0 0 1 4 0m-6.5 7l3-5.5m3 0l3 5.5\"/>"}, "apollo": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.5 3a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5M5 10.5l3-6l3.01 6m-5-2H8\"/><path d=\"M14.12 5.81a6.5 6.5 0 0 1-3.1 7.94a6.5 6.5 0 0 1-8.3-1.95a6.5 6.5 0 0 1 .78-8.49a6.5 6.5 0 0 1 8.5-.43\"/></g>"}, "apple": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.456 11.367c-1.428 2.76-2.57 4.133-3.612 4.133q-.729-.001-1.427-.446a2.18 2.18 0 0 0-2.246-.047c-.562.325-1.1.493-1.608.493C4.03 15.5 1.5 10.95 1.5 8.45c0-2.664 1.45-4.898 3.656-4.898c1.038 0 1.934.648 2.687.945c.32.128.68.119.991-.025c.61-.283 1.416-.92 2.416-.92c1.222 0 2.284.786 3.168 1.923a.383.383 0 0 1-.08.548c-.988.714-1.463 1.516-1.463 2.428c0 .913.475 1.714 1.463 2.428a.38.38 0 0 1 .118.488M8.5 3S8.347 1.394 10 1\"/>"}, "asciidoc": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3 15l1.714-4M5.78 8.5L9 1l6 14M1 8.5h7M1 11h6\"/>"}, "assembly": {"body": "<rect width=\"8\" height=\"13.001\" x=\"4\" y=\"1.499\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"2.286\" ry=\"2.286\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4 4.5H1.5M4 8H1.5M4 11.5H1.5m13-7H12M14.5 8H12m2.5 3.5H12\"/>"}, "astro": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.561 1.61L13.5 10.5c-.995-.517-2.395-1.298-3.5-1.493L8 3.5L6 9.007c-1.11.194-2.5.974-3.5 1.493l3.163-8.891c.121-.398.182-.597.302-.745A1 1 0 0 1 6.36.57c.176-.07.384-.07.8-.07h1.902c.416 0 .625 0 .801.071a1 1 0 0 1 .396.293c.12.148.18.347.301.746\"/><path stroke=\"#c6a0f6\" d=\"M10.411 11.266c-.43.365-1.292.613-2.283.613c-1.217 0-2.237-.374-2.508-.879a2.8 2.8 0 0 0-.119.831s-.063 1.038.666 1.76c0-.374.307-.679.686-.679c.648 0 .648.561.647 1.016v.04c0 .691.426 1.282 1.032 1.532a1.4 1.4 0 0 1-.141-.61c0-.658.39-.903.845-1.188c.36-.227.761-.479 1.038-.985a1.84 1.84 0 0 0 .137-1.451\"/></g>"}, "astro-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M8.591 8.591c-.466-.204-.932-.371-1.35-.445l-1.798-4.95l-1.797 4.95C2.649 8.32 1.398 9.022.5 9.488l2.843-7.992c.109-.357.163-.536.27-.669A.9.9 0 0 1 3.97.563C4.13.5 4.315.5 4.69.5h1.709c.374 0 .561 0 .72.064a.9.9 0 0 1 .355.263c.107.133.162.312.271.67L9.729 7.5\"/><path stroke=\"#c6a0f6\" d=\"M6.561 11.145a5 5 0 0 1-1.003.1c-1.094 0-2.01-.336-2.254-.79c-.087.26-.106.558-.106.748c0 0-.058.933.598 1.582c0-.337.276-.61.616-.61c.583 0 .582.503.582.912v.037c0 .62.383 1.152.928 1.376a1.24 1.24 0 0 1-.127-.548c0-.592.35-.812.759-1.068\"/><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.752-4l1.75 3l-1.75 3h-3.5l-1.75-3l1.75-3z\"/></g>"}, "audio": {"body": "<g fill=\"none\" stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M5.5 12.5a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2a2 2 0 0 1 2 2m9-2a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2a2 2 0 0 1 2 2\"/><path d=\"M5.5 12.5V5c0-.54.44-1.21 1.35-1.5l6.3-2c.9 0 1.35.88 1.35 1.5v7.58m-9-3.08l9-3\"/></g>"}, "autohotkey": {"body": "<rect width=\"13\" height=\"13\" x=\"1.5\" y=\"1.5\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"1\" ry=\"1\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.536\" d=\"m4.5 11.83l3.263-7.66l3.737 7.66m-5.356-1.69H9.56\" transform=\"matrix(.3911 0 0 .39765 1.107 8.405)\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.556\" d=\"m4.5 11.83l-.007-7.66m6.136-.006l-.019 7.666M4.987 8.21h4.452\" transform=\"matrix(.39109 0 0 .39152 5.183 8.371)\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.556\" d=\"m4.5 11.83l-.007-7.66m2.03 4.013l3.081 3.697M4.488 9.277L9.65 4.305\" transform=\"matrix(.39109 0 0 .39152 9.085 8.358)\"/>"}, "azure-pipelines": {"body": "<path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 12v2.5H4m2.5-9l2-4h6v6l-4 3v4h-4L6 14l1.5-1.5L6 11l-1.5 1.5l-1-1L5 10L3.5 8.5L2 10l-.5-.5v-4zm4 1a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "babel": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.42 4.02c1.1-.68 2.32-1.53 3.63-1.95c1.16-.38 2.43-.73 3.62-.5c.6.13 1.27.41 1.57.93c.3.5.3 1.18.05 1.68c-.97 1.88-5.26 3.6-5.26 3.6m1.2-5.92L2.47 14.5s6.14-2.13 8.23-4.43c.44-.48.96-1.17.75-1.78c-.18-.53-.9-.75-1.45-.85c-1.56-.26-4.67.92-4.67.92\"/>"}, "bash": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2 15.5c-.7 0-1.5-.8-1.5-1.5V5c0-.7.8-1.5 1.5-1.5h9c.7 0 1.5.8 1.5 1.5v9c0 .7-.8 1.5-1.5 1.5z\"/><path d=\"m1.2 3.8l3.04-2.5S5.17.5 5.7.5h8.4c.66 0 1.4.73 1.4 1.4v7.73a2.7 2.7 0 0 1-.7 1.75l-2.68 3.51\"/><path d=\"M6 8.75c0-.69-.54-1.25-1.2-1.25h-.6c-.66 0-1.2.56-1.2 1.25S3.54 10 4.2 10h.6c.66 0 1.2.56 1.2 1.25s-.54 1.25-1.2 1.25h-.6c-.66 0-1.2-.56-1.2-1.25M4.5 6.5v1m0 5v1\"/></g>"}, "batch": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2 15.5c-.7 0-1.5-.8-1.5-1.5V5c0-.7.8-1.5 1.5-1.5h9c.7 0 1.5.8 1.5 1.5v9c0 .7-.8 1.5-1.5 1.5z\"/><path d=\"m1.2 3.8l3.04-2.5S5.17.5 5.7.5h8.4c.66 0 1.4.73 1.4 1.4v7.73a2.7 2.7 0 0 1-.7 1.75l-2.68 3.51M3 8.5l3 2l-3 2\"/></g>"}, "bazel": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 15.5L15.5 8V4L12 .5l-4 4l-4-4L.5 4v4zM.5 4L8 11.5L15.5 4M8 15.5v-4m3.5.5V8L8 4.5L4.5 8v4\"/>"}, "benchmark": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.562 13.625c.636-1.1.938-2.387.938-3.75a7.5 7.5 0 1 0-13.997 3.75m5.56 0l3.75-4.688\"/>"}, "binary": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 1.5h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2\"/><path d=\"M10.5 9.5h1v3.05M6 9.5h1c.28 0 .5.22.5.5v2a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5v-2c0-.28.22-.5.5-.5m4-6h1c.28 0 .5.22.5.5v2a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V4c0-.28.22-.5.5-.5m-4.5 0h1v3.05\"/></g>"}, "biome": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.538 12.543C4.37 11.165 5.328 10.297 8 11l.5-2.5c-1.897-.447-4.05-.218-5.58.991a6.38 6.38 0 0 0-2.42 5h15L8 1.51L5 6.5\"/>"}, "bitbucket": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m13.5 14.5l2-13H.5l2 13zm1-9h-9l.5 5h4l.47-4.75\"/>"}, "blitz": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.43 7.5h-2.36c-.9 0-1.74-.41-2.28-1.12L5.47 3.5l1.5-3zm-8.96 1h2.35c.9 0 1.74.41 2.28 1.12l2.33 2.88l-1.5 3z\"/>"}, "bower": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#91d7e3\" d=\"M10.77 3.5c.13-.28.27-.53.45-.75c.67-.83 1.75-1.25 3.25-1.25c-.5 2.17-1 3.5-1.5 4s-1.33.83-2.5 1c-.03-.45-.05-.79-.05-1\"/><path stroke=\"#eed49f\" d=\"M6.47 4.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m2 5.5c.67.17 1.17.08 1.5-.25q1.005.255 1.5-.75c.83-.17 1.17-.67 1-1.5\"/><path stroke=\"#a6da95\" d=\"m8.47 6.5l7 1c.17 1-.08 2.17-.75 2.5c0 .67-.58 1-1.75 1c-.33.33-.67.5-1 .5s-.67-.17-1-.5c-.33.33-.67.58-1 .75s-.75.08-1.25-.25\"/><path stroke=\"#eed49f\" d=\"m8.72 11.5l.75 2c.17.5 0 .83-.5 1c-.43.14-.75-.25-1.25-.25s-.56.65-.75.75c-.67.33-1.17.17-1.5-.5l-.5-1.5c-.33.33-.75.5-1.25.5c-.55 0-2.03-1.2-2.79-3.5\"/><path stroke=\"#ee99a0\" d=\"M.93 10a9.5 9.5 0 0 1-.46-3c0-4.25 3.5-5.5 5.5-5.5c1.33 0 2.5.67 3.5 2l2.5 1l-3.5 2M.97 10c1.33.33 2.33.17 3-.5q3.495.495 4.5-3\"/></g>"}, "browserslist": {"body": "<g fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.51 7.22a4.23 4.23 0 0 1-1.5-4.56a5.42 5.42 0 0 0-3.3.81A4.34 4.34 0 0 1 7.3 1.5a5.47 5.47 0 0 0-2.25 2.6a4.3 4.3 0 0 1-1.5-1.4a5.4 5.4 0 0 0-.36 3.37c-.62.07-1.24 0-2.01-.28a5.5 5.5 0 0 0 1.66 3c-.47.42-1.01.72-1.82.94c.75.8 1.87 1.3 3.05 1.5a4.3 4.3 0 0 1-.96 1.78a5.45 5.45 0 0 0 3.37-.53c.22.59.31 1.2.24 2.02c1.53-.66 3.03-2.61 3.28-4.91c1.27-.21 2.06-.64 2.7-.98\"/><path d=\"M14.52 8a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-4.5 1.34c0-.6-.36-1.27-.45-1.89c-.06-.4-.15-.8-.09-1.52c0-.66.62-2.1 1.16-2.99\"/></g>"}, "bun": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f4dbd6\" d=\"M.5 8.51c0 5.25 5.5 6 7.5 6s7.5-.75 7.5-6c0-4-4.5-6-7.5-7c-3 1-7.5 3-7.5 7\"/><path stroke=\"#ee99a0\" d=\"M6.5 10.51h3c-.33.67-.83 1-1.5 1s-1.17-.33-1.5-1\"/><path stroke=\"#cad3f5\" d=\"M5 8.51a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m6 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1\"/></g>"}, "bun-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M15 11.51c.28 0 .5.23.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.27.22-.5.5-.5zm-4 0v-1.5a1.5 1.5 0 1 1 3 0v1.5\"/><path stroke=\"#f4dbd6\" d=\"M14.5 7.01c0-3.69-4.2-5.57-7-6.5c-2.8.93-7 2.81-7 6.5c0 4.85 5.13 5.5 7 5.5\"/><path stroke=\"#ee99a0\" d=\"M5.5 8.51h4c-.67.67-1.33 1-2 1s-1.33-.33-2-1\"/><path stroke=\"#cad3f5\" d=\"M5 6.51a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m5 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1\"/></g>"}, "c": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.056 12.952a7.05 7.05 0 0 0 9.944 0l-1.79-1.783a4.513 4.513 0 0 1-6.364 0a4.47 4.47 0 0 1 0-6.338a4.513 4.513 0 0 1 6.364 0l.895-.891l.895-.892a7.05 7.05 0 0 0-9.944 0a6.98 6.98 0 0 0 0 9.904\" clip-rule=\"evenodd\"/>"}, "c-header": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.056 12.952a7.05 7.05 0 0 0 9.944 0l-1.79-1.783a4.513 4.513 0 0 1-6.364 0a4.47 4.47 0 0 1 0-6.338a4.513 4.513 0 0 1 6.364 0l.895-.891l.895-.892a7.05 7.05 0 0 0-9.944 0a6.98 6.98 0 0 0 0 9.904\" clip-rule=\"evenodd\"/>"}, "cabal": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M5 1.5L9 8l6.5-1L11 .5z\"/><path stroke=\"#b7bdf8\" d=\"m9 10l6.5-1l-4 5.5l-6.5 1z\"/><path stroke=\"#8aadf4\" d=\"m2.1 5.075l1.694 1.476l1.901-.495l-.43 2.483l1.073 2.267l-1.96.058l-1.237 1.896l-.78-2.447L.52 9.217L2 7.647z\"/></g>"}, "caddy": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M7.5 11.5h3v-4h-5v2m4-2V6a1.5 1.5 0 0 0-3 0v1.5\"/><path stroke=\"#91d7e3\" d=\"M14.05 5.76c.9 2.5.22 5.3-1.75 7.07A6.45 6.45 0 0 1 5.5 14m-2.78-2.2A6.52 6.52 0 0 1 4 2.85a6.45 6.45 0 0 1 8.65.64M14.5 4.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-9 8.5A1.5 1.5 0 0 1 4 14.5A1.5 1.5 0 0 1 2.5 13A1.5 1.5 0 0 1 4 11.5A1.5 1.5 0 0 1 5.5 13m0-1.5l2-2m5-4l-1 1\"/></g>"}, "capacitor": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m14.36 9.2l-1.52 1.5c-.08.09-.14.15-.27.03c-1.5-1.52-6.4-6.4-7.32-7.31c-.09-.1-.08-.15 0-.24l1.62-1.6c.1-.1.16-.1.25 0L9.64 4.1c.07.07.13.16.25.17l2.68-2.69c.1-.1.17-.11.27 0l1.56 1.54c.14.13.13.2 0 .33l-2.52 2.5c-.17.17-.15.25.01.4l2.47 2.47c.16.15.15.23 0 .37zM3.44 5.28c-.11-.11-.18-.11-.29 0c-.5.52-1.02 1.03-1.53 1.53c-.15.14-.13.22 0 .35L4.1 9.64c.17.16.16.25 0 .41c-.84.82-1.66 1.66-2.5 2.49c-.12.12-.15.2 0 .33c.51.5 1.02 1.02 1.52 1.54c.12.12.19.12.3 0c.58-.59 2.4-2.4 2.75-2.71c1.36 1.37 2.27 2.27 2.7 2.72c.1.09.17.1.26 0l1.6-1.61c.1-.09.1-.15 0-.23L3.45 5.29Z\"/>"}, "cargo": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke=\"#f5a97f\" stroke-linejoin=\"round\"><path stroke-linecap=\"round\" d=\"M1.5 7L8 10.5L14.5 7m-10-2.5l3.503 2L14.498 3M4.5 5.5v7m3.5-6V14\"/><path stroke-linecap=\"round\" d=\"M8.003 3L11.5 4.5v8\"/><path stroke-linecap=\"square\" d=\"M1.5 6.984V11l6.503 3.5L14.5 11V3L11 1.5l-6.5 3v1z\"/></g>"}, "cargo-lock": {"body": "<g fill=\"none\" fill-rule=\"evenodd\"><path stroke=\"#8087a2\" d=\"M14.998 11.5a.5.5 0 0 1 .5.5v2.999a.5.5 0 0 1-.5.5H10a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5zM11 11.5V10a1.5 1.5 0 0 1 3 0v1.5\"/><path stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m.5 6l6 2.996l6-3.498m-9-2.001l3 2.003L12.498 2M3.5 4v7m3-5.502V12.5\"/><path stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.499 2.5L9.5 4v3\"/><path stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.498 5.5V2L9.538.5L3.5 3.5v.997l-3 1.501v3.54l6 2.962\"/></g>"}, "certificate": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"M8 9a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/><path stroke=\"#ed8796\" d=\"M9.86 2.5h.89c.97 0 1.75.78 1.75 1.75v.9m0 3.72v.88c0 .97-.78 1.75-1.75 1.75m-5.5 0c-.97 0-1.75-.78-1.75-1.75v-.88m0-3.73v-.89c0-.97.78-1.75 1.75-1.75h.89\"/><path stroke=\"#ed8796\" d=\"m12.5 5.14l.63.62c.68.69.68 1.8 0 2.48l-.64.63M10.5 11.5V15L8 13l-2.5 2v-3.5m-2-2.64l-.63-.62a1.75 1.75 0 0 1 0-2.48l.63-.62M6.13 2.5l.63-.63c.69-.68 1.8-.68 2.48 0l.62.63\"/></g>"}, "changelog": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.71 10.96a6.5 6.5 0 1 0-.69-3.53M2 8l1.5-1.5M2 8L.5 6.5m8 2v-4m0 4h3\"/>"}, "circle-ci": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.67 9.5a6.5 6.5 0 1 0 0-3H4.3a4 4 0 1 1 0 3z\"/><path d=\"M8.5 8a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "clojure": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M14.17 10.03A6.5 6.5 0 0 1 1.81 6.02\"/><path stroke=\"#8aadf4\" d=\"M1.87 5.85A6.5 6.5 0 0 1 14.22 9.9\"/><path stroke=\"#a6da95\" d=\"M6.36 4.9a3.5 3.5 0 1 0 3.41 6.12\"/><path stroke=\"#8aadf4\" d=\"M9.77 11.02a3.5 3.5 0 0 0-3.03-6.29\"/><path stroke=\"#cad3f5\" d=\"M8 7.5s-1.66 2.48-1.5 3.65\"/><path stroke=\"#cad3f5\" d=\"M1.81 6.02C2.47 5 3.83 4.49 5 4.46c4.06 0 3 5.56 5.03 6.86c1.21.52 3.5-.21 4.15-1.32\"/></g>"}, "cmake": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"m6 11.5l-3.5 3H13Z\"/><path stroke=\"#ed8796\" d=\"m9 1.5l5.5 11l-4.5-2z\"/><path stroke=\"#8aadf4\" d=\"m1.5 12.5l6-5l-.5-6Z\"/></g>"}, "cobol": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.74 2.24c.32-1.32 2.2-1.32 2.52 0a1.3 1.3 0 0 0 1.93.8c1.15-.7 2.48.62 1.77 1.77a1.3 1.3 0 0 0 .8 1.93c1.32.32 1.32 2.2 0 2.52a1.3 1.3 0 0 0-.8 1.93c.7 1.15-.62 2.48-1.77 1.77a1.3 1.3 0 0 0-1.93.8c-.32 1.32-2.2 1.32-2.52 0a1.3 1.3 0 0 0-1.93-.8c-1.15.7-2.48-.62-1.77-1.77a1.3 1.3 0 0 0-.8-1.93c-1.32-.32-1.32-2.2 0-2.52a1.3 1.3 0 0 0 .8-1.93c-.7-1.15.62-2.48 1.77-1.77a1.3 1.3 0 0 0 1.93-.8M10 6.5a2.5 2.5 0 1 0 0 3\"/>"}, "code-climate": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m7.5 13.5l2-1.94L5 7L.55 11.56l1.98 1.94l2.49-2.45zm6-3l2-1.86l-4-4.14l-3 3l2 2l1-1z\" transform=\"matrix(1.00302 0 0 1 -.05 0)\"/>"}, "code-of-conduct": {"body": "<g fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M13.42 8.93L8.01 14.5L2.59 8.93a3.85 3.85 0 0 1-.93-3.8A3.55 3.55 0 0 1 8 4.01a3.55 3.55 0 0 1 6.34 1.14c.4 1.34.05 2.8-.92 3.79\"/><path d=\"M8 4L5.72 6.53a.77.77 0 0 0 0 1.06l.4.4c.5.52 1.21.55 1.7.02l.74-.8a2.25 2.25 0 0 1 3.25 0l1.62 1.68m-5.05 2.25l1.44 1.5m.36-3.38l1.45 1.5\"/></g>"}, "codeowners": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m8.01 1.5l5.728 1.774C15.25 11.154 9.813 14.5 7.984 14.5s-7.281-3.357-5.7-11.226zM8 8.5v2z\"/><circle cx=\"8\" cy=\"7\" r=\"1.5\"/></g>"}, "coffeescript": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 7c-.5 2.5-2 5.5-3 6.5s-2 1-3 1s-2 0-3.02-1C4.45 12.5 3 9.5 2.5 7c3 2 9 2 12 0m-12-2c3 2 9 2 12 0\"/><path d=\"M2.5 8.4c-1.73 1.6-1.26 4.17 2 4.1M7 2c-.75-.36-1.5.18-1.5.74c-.*********** 1.5.75c.6-.03.97-.7 1.5-.96C8.98 2.3 9.41 2 10 2c.56 0 1.52.25 1.5 1c-.01.61-1.12.8-1.5.49\"/></g>"}, "commitlint": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M4.5 9.23c-.74.49-2.04.3-2.59-.45a2.21 2.21 0 0 1 0-2.58c.56-.74 1.85-.92 2.59-.42M5.5 4h1v4.4s-.14 1.12.93 1.1H8.5\"/><path stroke=\"#ed8796\" d=\"M10.5 9.51h4m-4 3.99l2-2l2 2\"/></g>"}, "config": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.711\" d=\"M7.997 9.694a1.726 1.695 0 1 0 0-3.39a1.726 1.695 0 0 0 0 3.39m3.021-6.78l3.021 5.085l-3.021 5.085H4.976L1.955 7.999l3.021-5.085z\"/>"}, "contributing": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M8.128 14.499h-4.81c-.386 0-.818-.432-.818-.818V2.317c0-.385.432-.817.817-.817h9.37c.385 0 .817.432.817.817v6.866m-8-3.683H10.5m-5 4h3\"/><path stroke=\"#a6da95\" d=\"m9.5 12.5l2 2l4-4\"/></g>"}, "cpp": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.556 12.952a7.05 7.05 0 0 0 9.944 0l-1.79-1.783a4.513 4.513 0 0 1-6.364 0a4.47 4.47 0 0 1 0-6.338a4.513 4.513 0 0 1 6.364 0l.895-.891l.895-.892a7.05 7.05 0 0 0-9.944 0a6.98 6.98 0 0 0 0 9.904\" clip-rule=\"evenodd\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 6v4M5.514 8h3.999m3.973-2v4M11.5 8h4\"/>"}, "cpp-header": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.556 12.952a7.05 7.05 0 0 0 9.944 0l-1.79-1.783a4.513 4.513 0 0 1-6.364 0a4.47 4.47 0 0 1 0-6.338a4.513 4.513 0 0 1 6.364 0l.895-.891l.895-.892a7.05 7.05 0 0 0-9.944 0a6.98 6.98 0 0 0 0 9.904\" clip-rule=\"evenodd\"/><path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 6v4M5.514 8h3.999m3.973-2v4M11.5 8h4\"/>"}, "csharp": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" d=\"M6.666 1.01a1 1 0 0 1 .822 1.15L7.18 3.999h2.972l.36-2.165a1 1 0 0 1 1.971.328l-.303 1.837H14a.999.999 0 1 1 0 2h-2.153l-.666 4H13a.999.999 0 1 1 0 2h-2.153l-.36 2.166a1 1 0 0 1-1.971-.328l.306-1.834H5.847l-.36 2.165a1 1 0 0 1-1.971-.328L3.819 12H2a.999.999 0 1 1 0-2h2.153l.666-4H3a.999.999 0 1 1 0-2h2.153l.36-2.166a1 1 0 0 1 1.15-.822Zm.18 4.988l-.665 4h2.972l.666-4Z\"/>"}, "cspell": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 1.5h4.5c1.108 0 2 .892 2 2V8H8Z\"/><path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.5 10.5v2c0 1.108-.892 2-2 2H8v-4Zm-13 0v2c0 1.108.892 2 2 2h2v-4Z\"/>"}, "css": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 1.5h8A2.5 2.5 0 0 1 14.5 4v8a2.5 2.5 0 0 1-2.5 2.5H4A2.5 2.5 0 0 1 1.5 12V4A2.5 2.5 0 0 1 4 1.5\"/><path stroke-width=\".814\" d=\"M10.24 11.53c0 .58.438 1.038.96 1.035l.453-.004c.522-.003.949-.451.949-1.033c0-.58-.427-1.065-.95-1.065h-.451c-.523 0-.95-.486-.95-1.066s.427-1.038.95-1.038h.452c.522 0 .951.458.951 1.039M6.8 11.529c0 .58.438 1.04.96 1.036l.465-.004c.523-.003.936-.451.936-1.031s-.409-1.066-.931-1.066h-.47c-.522 0-.949-.485-.949-1.066c0-.58.427-1.037.95-1.037h.451c.523 0 .964.457.964 1.037M3.407 11.53c0 .58.438 1.052.96 1.052h.452c.522 0 .95-.457.95-1.038m.01-2.131c0-.58-.437-1.038-.96-1.038h-.451c-.523 0-.96.468-.96 1.05v2.118\"/></g>"}, "css-map": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M.5 4.06c0 .77.24 1.52.7 2.13l2.24 3.96l.04.08h0c.***********.52.27s.36-.09.48-.23h0l.03-.03l.08-.15l2.2-3.88c.46-.61.71-1.37.71-2.15A3.63 3.63 0 0 0 3.88.5C1.95.5.5 2.1.5 4.06\"/><path stroke=\"#ed8796\" d=\"M5.5 4A1.5 1.5 0 0 1 4 5.5A1.5 1.5 0 0 1 2.5 4A1.5 1.5 0 0 1 4 2.5A1.5 1.5 0 0 1 5.5 4\"/><path stroke=\"#8aadf4\" d=\"M10 3.5h5.5l-1.27 9.8L10 15l-4.23-1.7L5.6 12\"/><path stroke=\"#cad3f5\" d=\"M9.46 5.5h3.08L12 11l-2 1l-2-1l-.05-.5m.55-2h3.62\"/></g>"}, "css3": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M1.5 1.5h13L13 13l-5 2l-5-2z\"/><path stroke=\"#cad3f5\" d=\"M5 4.5h6l-.5 6l-2.5 1l-2.5-1l-.08-1m1.08-2h4\"/></g>"}, "csv": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 3.5c0-.54.48-1 1.08-1H6.5l1.54 1h5.38c.6 0 1.08.44 1.08.98l-.09 9.04c0 .54-.48.98-1.08.98H2.58c-.6 0-1.08-.44-1.08-.98zm2 4v4m3-4v4m3-4v4m3-4v4m-9 0h9m-9-2h9m-9-2h9\"/>"}, "cucumber": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14 7.28a7.37 7.37 0 0 1-7.49 7.22v-1.63h0a5.76 5.76 0 0 1-4.32-7.04A5.8 5.8 0 0 1 4.94 2.3c1.37-.78 3-1 4.54-.62A5.86 5.86 0 0 1 14 7.3h0ZM6.89 5.16L6.5 4.4m2.64 5.26l.37.75M5.76 8.53l-.75.38m2.25.75l-.37.75m1.87-5.25l.38-.75m1.87 4.12l-.75-.37m-4.5-1.5l-.75-.38\"/>"}, "cuda": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M13.46 9C9.5 12 3.5 13 .5 8c3.53-4.5 7-4.5 11-.5c-.62.65-4.52 3.9-8.5.5c0 0 2.5-3 5.5-.5c0 0-1.05.59-1.5.75\"/><path d=\"M4.5 4.8V2.5h11v11h-11v-2.3\"/></g>"}, "cypress": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M7.5 9.393c-.476.873-1.446 1.308-2.376 1.065S3.532 9.353 3.501 8.347s.578-1.91 1.491-2.216c.913-.305 1.908.064 2.436.903m3.072 2.968L8.5 6m-.503 8.5c.367-.048.527-.073.731-.21c.126-.106.245-.28.302-.433L12.5 6\"/><path d=\"M7.964 14.5a6.5 6.5 0 1 1 3.838-1.228\"/></g>"}, "d": {"body": "<path fill=\"none\" stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15 3.5a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m-8-1c1.84 0 3.47.9 4.47 2.29a2 2 0 1 1 1.01 3.71a5.5 5.5 0 0 1-5.48 5H1.5v-11Zm-3.5 2v7H7a3.5 3.5 0 0 0 0-7z\"/>"}, "dart": {"body": "<g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M7 14.5h4.5v-3h3V7L9.17 1.64c-.28-.29-.8-.47-1.17-.29L3.5 3.5L1.35 8c-.18.37 0 .88.3 1.17z\"/><path d=\"M3.5 11V3.5H11m-7.5 0l8 8\"/></g>"}, "dart-generated": {"body": "<g fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M7 14.5h4.5v-3h3V7L9.17 1.64c-.28-.29-.8-.47-1.17-.29L3.5 3.5L1.35 8c-.18.37 0 .88.3 1.17z\"/><path d=\"M3.5 11V3.5H11m-7.5 0l8 8\"/></g>"}, "database": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 6.5c3.59 0 6.5-1.4 6.5-2.68S11.59 1.5 8 1.5S1.5 2.54 1.5 3.82S4.41 6.5 8 6.5M14.5 8c0 .83-1.24 1.79-3.25 2.2s-4.49.41-6.5 0S1.5 8.83 1.5 8m13 4.18c0 .83-1.24 1.6-3.25 2c-2.01.42-4.49.42-6.5 0c-2.01-.4-3.25-1.17-3.25-2m0-8.3v8.3m13-8.3v8.3\"/>"}, "deno": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 8a6.5 6.5 0 1 0 13 0a6.5 6.5 0 0 0-13 0m7.67 5.8L8.11 9.56C6.2 9.49 4.5 8.38 4.5 7.03c0-1.4 1.62-2.53 3.61-2.53c2 0 2.89.72 3.61 2.17c.02.03.5 1.6 1.45 4.7M8.5 6.5\"/>"}, "deno-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><path stroke=\"#cad3f5\" d=\"M12.5 6.5a6 6 0 1 0-5 5.92m.17-.92l-.84-3.33C5.07 8.1 3.5 7.08 3.5 5.83C3.5 4.54 5 3.5 6.83 3.5c1.84 0 2.67.67 3.34 2l.62 2m-3.29-2\"/></g>"}, "dependabot": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.5 7.5v2m10-.5V7m-5 .5v2M.5 9V7m8-3.5v-2H6.52m-4.02 2h11a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1h-11a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1\"/>"}, "devcontainer": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 4v8L8 14.5l6.5-2.5V4L8 1.5Z\"/><path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 5.5L8 7l3.5-1.5M8 7v4\"/>"}, "dhall": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3.5 11.5l2-7l6-2l2 2l-2 6l-7 2l3-3a1 1 0 1 0-1-1zm6.75-10.25l4.5 4.5M2 14.5a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1\"/>"}, "diff": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M10 6H6m2-2v4\"/><path stroke=\"#ed8796\" d=\"M10 11.5H6\"/><path stroke=\"#cad3f5\" d=\"M6.1.5h3.8c1.44 0 2.6 1.09 2.6 2.43v10.14c0 1.34-1.16 2.43-2.6 2.43H6.1c-1.44 0-2.6-1.09-2.6-2.43V2.93C3.5 1.6 4.66.5 6.1.5\"/></g>"}, "django": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.5 6.5v4.37c0 1.64-.12 2.43-.5 3.1a2.8 2.8 0 0 1-1.5 1.53l-2-1c.94-.43 1.3-.7 1.6-1.28c.3-.59.4-1.27.4-3.06V6.5zm0-2h-2v-2h2zm-4 7.71c-1.14.2-1.97.29-2.88.29c-2.7 0-4.12-1.16-4.12-3.38c0-2.14 1.72-3.53 4.04-3.53c.36 0 .63.03.96.11V2.5h2z\"/><path d=\"M3.5 9.08c0 1.12.84 1.47 1.98 1.47c.24 0 .7-.01 1.02-.05v-3c-.26-.08-.73-.1-1-.1c-1.13 0-2 .53-2 1.68\"/></g>"}, "docker": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 8.5H11l.75-.5a5.35 5.35 0 0 1 0-3.5c1 .6 1 1.88 1.74 2c.77-.09 1.23.01 2 .52c0 0-.97 1.77-2.5 1.98c-1.93 3.65-4.5 5.5-6.98 5.5C0 14.5.5 8.5.5 8.5m1 0v-2m0 0h8m-6 2v-4m0 0h4m-2-2h2m-2 6v-6m2 6v-6m2 6v-2\"/>"}, "docker-compose": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 8.5H11l.75-.5a5.35 5.35 0 0 1 0-3.5c1 .6 1 1.88 1.74 2c.77-.09 1.23.01 2 .52c0 0-.97 1.77-2.5 1.98c-1.93 3.65-4.5 5.5-6.98 5.5C0 14.5.5 8.5.5 8.5m1 0v-2m0 0h8m-6 2v-4m0 0h4m-2-2h2m-2 6v-6m2 6v-6m2 6v-2\"/>"}, "docker-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 8.5H11l.75-.5a5.35 5.35 0 0 1 0-3.5c1 .6 1 1.88 1.74 2c.77-.09 1.23.01 2 .52c0 0-.97 1.77-2.5 1.98c-1.93 3.65-4.5 5.5-6.98 5.5C0 14.5.5 8.5.5 8.5m1 0v-2m0 0h8m-6 2v-4m0 0h4m-2-2h2m-2 6v-6m2 6v-6m2 6v-2\"/>"}, "drawio": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.25 10.5h3.5c.41 0 .75.34.75.75v2.5c0 .41-.34.75-.75.75h-3.5a.75.75 0 0 1-.75-.75v-2.5c0-.41.34-.75.75-.75m8 0h3.5c.41 0 .75.34.75.75v2.5c0 .41-.34.75-.75.75h-3.5a.75.75 0 0 1-.75-.75v-2.5c0-.41.34-.75.75-.75m-4-9h3.5c.41 0 .75.34.75.75v2.5c0 .41-.34.75-.75.75h-3.5a.75.75 0 0 1-.75-.75v-2.5c0-.41.34-.75.75-.75M4 10.5l3.5-5m4.5 5l-3.5-5\"/>"}, "drizzle-orm": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.167\" d=\"m12.001 9.002l2-4.002M8 12.002L10 8M5.998 9l2-4.001m-6.001 7.002l2-4.002\"/>"}, "dub": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m4.537 10.789l-.841.41c-.25.12-.522.183-.799.183H.495V4.444h3.284c.924 0 1.673.75 1.673 1.673v2.831a2.613 2.613 0 0 0 2.613 2.613h0a2.613 2.613 0 0 0 2.614-2.613V4.44h3.157a1.67 1.67 0 0 1 1.669 1.67v.062c0 .888-.72 1.607-1.607 1.607h-1.83h1.626c1 0 1.811.811 1.811 1.811v.003c0 1-.81 1.81-1.81 1.81H11.23\"/><circle cx=\"8.034\" cy=\"8.941\" r=\"1.25\"/></g>"}, "dub-selections": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke-miterlimit=\"1.5\"><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m4.121 9.737l-.754.366a1.7 1.7 0 0 1-.717.162H.495V4.09h2.946c.83 0 1.501.667 1.501 1.49v2.519a2.335 2.335 0 0 0 2.344 2.326h.002a2.335 2.335 0 0 0 2.344-2.326V4.086h2.832c.827 0 1.498.665 1.498 1.486v.055c0 .79-.646 1.43-1.442 1.43h-1.642h1.46c.448 0 .854.181 1.148.473\"/><ellipse cx=\"7.259\" cy=\"8.092\" stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"1.122\" ry=\"1.113\"/><path stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/></g>"}, "editorconfig": {"body": "<path fill=\"none\" stroke=\"#f4dbd6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.86 6.46c.***********.66.28a2 2 0 0 1 .66.6c.**********.15.53a.8.8 0 0 1-.29.53c-.2.16-.5.14-.74.15a3.9 3.9 0 0 1-1.5-.32a3.5 3.5 0 0 1-.64-.31c-.18-.12-.44-.25-.55-.45A.73.73 0 0 1 1.5 7c.02-.18.1-.4.24-.51c.18-.16.49-.2.72-.23a4.3 4.3 0 0 1 1.4.2m4.05 1.7c.**********.7.17c.**********.72.3c.***********.45.48c.***********-.05.8a.97.97 0 0 1-.37.39a1.4 1.4 0 0 1-.6.2c-.18.03-.37-.03-.56-.05c-.25-.04-.5-.07-.73-.13c-.27-.07-.56-.13-.8-.28c-.17-.1-.32-.24-.43-.4a.9.9 0 0 1-.11-.33a2.8 2.8 0 0 1 0-.74c.03-.1.1-.2.19-.27c.1-.1.23-.18.37-.21c.19-.06.39-.03.58-.02c.***********.64.1zM3.59 6.04s.3-.61.52-.85a4.1 4.1 0 0 1 3.17-.97a4.25 4.25 0 0 1 2 .7c.24.18.47.4.67.64a5.4 5.4 0 0 1 1.04 2.02c.08.34.12.7.1 1.06a5.3 5.3 0 0 1-.56 1.86c-.11.21-.25.4-.39.6a7.3 7.3 0 0 1-2.29 2.17c-.5.3-1.04.53-1.58.73c-.53.2-1.06.4-1.62.47c-.4.05-.81.06-1.2-.02a2.6 2.6 0 0 1-.98-.43a2.7 2.7 0 0 1-.8-1.08a2.9 2.9 0 0 1-.17-1.04c0-.16 0-.32.02-.47c.02-.2.03-.4.07-.6a9 9 0 0 1 .95-2.34m2.49-4c-.02-.19-.06-.37-.06-.56c0-.22.02-.45.07-.66c.04-.13.1-.25.16-.37c.08-.15.17-.3.28-.42c.12-.15.27-.26.42-.38c.12-.1.23-.2.37-.27a2.6 2.6 0 0 1 1.02-.32c.13 0 .26 0 .39.02a1.08 1.08 0 0 1 .62.41c.09.12.17.25.2.4c.02.09.01.2 0 .3c-.02.19-.06.38-.13.57c-.04.1-.1.21-.16.31c-.12.22-.27.42-.4.62m3.07 2.45s.33-.5.55-.67c.2-.17.43-.3.68-.37c.36-.09.75-.1 1.1-.01a1.66 1.66 0 0 1 1.2 1.22a1.88 1.88 0 0 1-.11 1.42c-.19.33-.5.57-.79.8a3 3 0 0 1-1.6.72c-.23.04-.71.02-.71.02\"/>"}, "ejs": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.5 13.5L.5 8l5-5.5m2.99 11l6.01-11M9 5.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m5 8a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3\"/>"}, "eleventy": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 5.5L14 13m1.5-7.5L14 13s-.215 1-1 1s-.5-.5-.5-.5M8 5.5h2.5M9 2v9s0 1 1 1h.5M.5 3L2 2v10m2-9l1.5-1v10\"/>"}, "elixir": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.03 14.5C5 14.5 3.5 12.49 3.5 10.01c0-2.82 2.25-7.02 4.62-8.48a.24.24 0 0 1 .24 0c.08.04.12.12.11.2c-.13 1.25.22 2.5.98 3.54c.3.43.63.8 1.02 1.27c.54.66.94 1.03 1.52 2.08l.01.02c.33.56.5 1.2.5 1.84c0 2.03-1.69 4.02-4.47 4.02\"/>"}, "elm": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 1.5h10c.83 0 1.5.67 1.5 1.5v10c0 .83-.67 1.5-1.5 1.5H3A1.5 1.5 0 0 1 1.5 13V3c0-.83.67-1.5 1.5-1.5M2 2l12 12M8.5 1.5l6 6M11 11l3.5-3.5m-10-3h6.25M2 14l9-9\"/>"}, "ember": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 9.25c6.11 1.18 8.03-.92 8.79-1.7c1.53-1.57 0-4.71-1.91-3.93c-1.91.79-4.59 5.5-2.3 8.63c1.53 2.1 4.34 1.41 8.42-1.75\"/>"}, "env": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.5 8.5V12m0-6.5V4m0 4.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m5 3.5v-1.5m0-3V4m0 6.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M4 1.5h8A2.5 2.5 0 0 1 14.5 4v8a2.5 2.5 0 0 1-2.5 2.5H4A2.5 2.5 0 0 1 1.5 12V4A2.5 2.5 0 0 1 4 1.5\"/>"}, "envrc": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"M4.5 7.5V11m0-6.5V3m0 4.5c2 0 2-3 0-3s-2 3 0 3m5-1V3m1.406 4.45a1.5 1.5 0 0 0-.281-.481C10.375 6.688 10 6.5 9.5 6.5C8.5 6.5 8 7.25 8 8c0 .188.031.375.094.55M3 .5h8A2.5 2.5 0 0 1 13.5 3v4m-7.425 6.5H3A2.5 2.5 0 0 1 .5 11V3A2.5 2.5 0 0 1 3 .5\"/><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.752-4l1.75 3l-1.75 3h-3.5l-1.75-3l1.75-3z\"/></g>"}, "erlang": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M6.5 5.5c0-1.25 1-2 2-2s2 .75 2 2z\"/><path d=\"M13.5 13c.47-.57 1.12-1.24 1.5-2l-2.25-1.25c-.74 1.36-1.76 2.75-3.25 2.75c-2.1 0-3-2.3-3-5h8c.05-1.61-.31-3.45-1-4.5M3 13c-1.08-1.3-1.5-3-1.5-5S2.1 4.24 3 3\"/></g>"}, "esbuild": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14m.5-10.5h2L13 8l-2.5 3.5h-2L11 8zM4 4.5h2L8.5 8L6 11.5H4L6.5 8z\"/>"}, "eslint": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#c6a0f6\" d=\"M11.71 1.5L15.42 8l-3.71 6.5H4.29L.58 8l3.71-6.5z\"/><path stroke=\"#b7bdf8\" d=\"m8 4.07l3.5 1.97v3.92L8 11.93L4.5 9.96V6.04z\"/></g>"}, "eslint-ignore": {"body": "<g fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11.71 1.5L15.42 8l-3.71 6.5H4.29L.58 8l3.71-6.5z\"/><path d=\"m8 4.07l3.5 1.97v3.92L8 11.93L4.5 9.96V6.04z\"/></g>"}, "exe": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3 2.5h10c.83 0 1.5.67 1.5 1.5v9c0 .83-.67 1.5-1.5 1.5H3A1.5 1.5 0 0 1 1.5 13V4c0-.83.67-1.5 1.5-1.5m-1.5 3h13\"/><path d=\"m6.5 8l-2 2l2 2m3-4l2 2l-2 2\"/></g>"}, "fastlane": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M4.5 13.5L3.05 8.42m-.06-3.86a2 2 0 1 0 .06 3.86L4.5 13.5\"/><path stroke=\"#ed8796\" d=\"m2.5 6.5l3.88-2.82A2 2 0 1 1 10 2.3\"/><path stroke=\"#8aadf4\" d=\"M14.3 8.33a2 2 0 1 0-2.43-3L8.01 2.5\"/><path stroke=\"#a6da95\" d=\"M10.06 14.89a2 2 0 1 0 2-3.3L13.5 6.5\"/><path stroke=\"#c6a0f6\" d=\"M11.5 13.5h-5a2 2 0 1 1-3.73-1\"/><path stroke=\"#c6a0f6\" d=\"M12 13.5a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#7dc4e4\" d=\"M5 13.5a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#ed8796\" d=\"M3 6.5a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#8aadf4\" d=\"M8.5 2.5A.5.5 0 0 1 8 3a.5.5 0 0 1-.5-.5A.5.5 0 0 1 8 2a.5.5 0 0 1 .5.5\"/><path stroke=\"#a6da95\" d=\"M14 6.5a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "favicon": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3.67 14.57l.83-4.82L1 6.34l4.84-.71L8 1.25l2.16 4.38l4.84.71l-3.5 3.41l.83 4.81L8 12.29z\"/>"}, "figma": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M7.5 11.5h-2a2 2 0 1 0 2 2z\"/><path stroke=\"#c6a0f6\" d=\"M7.5 10.5v-4h-2a2 2 0 1 0 0 4z\"/><path stroke=\"#ed8796\" d=\"M7.5 5.5v-4h-2a2 2 0 1 0 0 4z\"/><path stroke=\"#f5a97f\" d=\"M10.5 5.5a2 2 0 1 0 0-4h-2v4z\"/><path stroke=\"#91d7e3\" d=\"M12.5 8.5a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2a2 2 0 0 1 2 2\"/></g>"}, "file": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 6.5v6a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h4.01m-.01 0l5 5h-4a1 1 0 0 1-1-1z\"/>"}, "firebase": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3.5 12.5l9-8l1 9l-5 2zm0 0l5-9l1.9 2.78M3.5 12.5l1-11l3.1 3.1\"/>"}, "flutter": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-width=\".264\"><path stroke=\"#91d7e3\" stroke-linejoin=\"round\" d=\"M2.146.38L.422 2.115l.535.53L3.215.381ZM1.22 2.911l.528-.53l.538.528l-.533.534Z\"/><path stroke=\"#91d7e3\" stroke-linejoin=\"round\" d=\"m1.751 2.38l.534.533l.929-.933l-1.063-.002Z\"/><path stroke=\"#8aadf4\" stroke-linejoin=\"round\" d=\"M2.285 2.913s-.531.535-.532.53c0-.006.398.398.398.398h1.064Z\"/><path stroke=\"#8aadf4\" d=\"m3.215 3.841l-.93-.928l-.532.53l.398.398\"/></g>", "width": 4.233, "height": 4.233}, "folder": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v6c0 .83-.67 1.5-1.5 1.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/>"}, "folder-admin": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M15.5 9.25v-.58L12 7.5L8.5 8.67v2.62c0 2.12 1.2 3.64 3.5 4.21m2.5-3a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1 3a1 1 0 1 0-2 0\"/></g>"}, "folder-admin-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24L15.12 7M6.5 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M15.5 9.25v-.58L12 7.5L8.5 8.67v2.62c0 2.12 1.2 3.64 3.5 4.21m2.5-3a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1 3a1 1 0 1 0-2 0\"/></g>"}, "folder-android": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M15.5 14a3.5 3.5 0 1 0-6.99 0l-.01 1.5h7zm-1-2.97l1-2.03m-6 2.03L8.5 9\"/></g>"}, "folder-android-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24L15.12 7M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M15.5 14a3.5 3.5 0 1 0-6.99 0l-.01 1.5h7zm-1-2.97l1-2.03m-6 2.03L8.5 9\"/></g>"}, "folder-animation": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5bde6\" d=\"m9.5 15l2-3l-2-3l-2 3zM14 9l1.5 3l-1.5 3m-2-6l1.5 3l-1.5 3\"/></g>"}, "folder-animation-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24L15 7.49M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5bde6\" d=\"m9.5 15l2-3l-2-3l-2 3zM14 9l1.5 3l-1.5 3m-2-6l1.5 3l-1.5 3\"/></g>"}, "folder-api": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.5 13.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m-3-4a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m6 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m0 6a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m-6 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m4-4.5L14 9.5M12.5 13l1.5 1.5m-5 0l1.5-1.5M9 9.5l1.5 1.5\"/></g>"}, "folder-api-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.5 13.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m-3-4a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m6 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m0 6a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m-6 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m4-4.5L14 9.5M12.5 13l1.5 1.5m-5 0l1.5-1.5M9 9.5l1.5 1.5\"/></g>"}, "folder-app": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M11.5 7v1.5m.5 2l2.5 5m-3.5-5l-2.5 5m-.708-4.497a4 4 0 0 0 5.495 2.077\"/><circle cx=\"11.5\" cy=\"9.5\" r=\"1\" stroke=\"#8aadf4\"/></g>"}, "folder-app-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M11.5 7v1.5m.5 2l2.5 5m-3.5-5l-2.5 5m-.708-4.497a4 4 0 0 0 5.495 2.077\"/><circle cx=\"11.5\" cy=\"9.5\" r=\"1\" stroke=\"#8aadf4\"/></g>"}, "folder-assets": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linejoin=\"round\" d=\"M12.923 11.904H7.5L10.212 7Z\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linejoin=\"round\" d=\"M11.662 9.641a2.569 2.596 0 1 1-1.308 2.263\"/>"}, "folder-assets-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24L15.12 7M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linejoin=\"round\" d=\"M12.923 11.904H7.5L10.21 7Z\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linejoin=\"round\" d=\"M11.662 9.641a2.569 2.596 0 1 1-1.308 2.263\"/>"}, "folder-audio": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#ee99a0\"><path d=\"M10.654 14.423A1.077 1.077 0 0 1 9.577 15.5A1.077 1.077 0 0 1 8.5 14.423a1.077 1.077 0 0 1 1.077-1.077a1.077 1.077 0 0 1 1.077 1.077m4.846-1.077a1.077 1.077 0 0 1-1.077 1.077a1.077 1.077 0 0 1-1.077-1.077a1.077 1.077 0 0 1 1.077-1.077a1.077 1.077 0 0 1 1.077 1.077\"/><path d=\"M10.654 14.423v-4.038c0-.291.237-.652.727-.808L14.773 8.5c.485 0 .727.474.727.808v4.081m-4.846-1.658l4.846-1.616\"/></g></g>"}, "folder-audio-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#ee99a0\"><path d=\"M10.654 14.423A1.077 1.077 0 0 1 9.577 15.5A1.077 1.077 0 0 1 8.5 14.423a1.077 1.077 0 0 1 1.077-1.077a1.077 1.077 0 0 1 1.077 1.077m4.846-1.077a1.077 1.077 0 0 1-1.077 1.077a1.077 1.077 0 0 1-1.077-1.077a1.077 1.077 0 0 1 1.077-1.077a1.077 1.077 0 0 1 1.077 1.077\"/><path d=\"M10.654 14.423v-4.038c0-.291.237-.652.727-.808L14.773 8.5c.485 0 .727.474.727.808v4.081m-4.846-1.658l4.846-1.616\"/></g></g>"}, "folder-audit": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 12.333L9.038 14l3.693-4m-1.539 3.333l.616.667l3.692-4\"/>"}, "folder-audit-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 12.333L9.038 14l3.693-4m-1.539 3.333l.616.667l3.692-4\"/>"}, "folder-aws": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#f5a97f\"><path d=\"M7.646 14c.824.92 2.022 1.5 3.354 1.5s2.53-.58 3.354-1.5\"/><path d=\"M12.5 13.5h2v2m-4.5-8h1.5a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-1a1 1 0 1 1 0-2h1.75\"/></g></g>"}, "folder-aws-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#f5a97f\"><path d=\"M7.646 14c.824.92 2.022 1.5 3.354 1.5s2.53-.58 3.354-1.5\"/><path d=\"M12.5 13.5h2v2m-4.5-8h1.5a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-1a1 1 0 1 1 0-2h1.75\"/></g></g>"}, "folder-azure-devops": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 12.253v-1.817l6-.936L10.44 8m-.253 7L8.5 13l6 .5v-3\"/>"}, "folder-azure-devops-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 12.253v-1.817l6-.936L10.44 8m-.253 7L8.5 13l6 .5v-3\"/>"}, "folder-azure-pipelines": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#b7bdf8\" d=\"M8.5 14v1.5H10m1-5l1.27-2h3.23v3.23l-2 1.77v2h-2L11 15l.75-.75l-2-2L9 13l-.5-.5v-2zm2.5 0\"/></g>"}, "folder-azure-pipelines-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#b7bdf8\" d=\"M8.5 14v1.5H10m1-5l1.27-2h3.23v3.23l-2 1.77v2h-2L11 15l.75-.75l-2-2L9 13l-.5-.5v-2zm2.5 0\"/></g>"}, "folder-benchmark": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M15 14.5c.339-.587.5-1.273.5-2a4 4 0 1 0-7.465 2m2.965 0l2-2.5\"/></g>"}, "folder-benchmark-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M15 14.5c.339-.587.5-1.273.5-2a4 4 0 1 0-7.465 2m2.965 0l2-2.5\"/></g>"}, "folder-caddy": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M12.27 13.774h.947v-2.306H10.32v.484m2.318-.484v-.865c0-1.153-1.739-1.153-1.739 0v.865\"/><path stroke=\"#91d7e3\" d=\"M15.274 10.465a3.75 3.75 0 0 1-1.014 4.076a3.75 3.75 0 0 1-3.94.674c-.557-.285-1.074-.55-1.61-1.268a3.746 3.746 0 0 1 .74-5.16a3.75 3.75 0 0 1 5.013.37c.31.396.679.793.81 1.308m-5.587 3.88l1.043-.976m4.219-3.585l-.66.537\"/></g>"}, "folder-caddy-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M12.27 13.774h.947v-2.306H10.32v.484m2.318-.484v-.865c0-1.153-1.739-1.153-1.739 0v.865\"/><path stroke=\"#91d7e3\" d=\"M15.274 10.465a3.75 3.75 0 0 1-1.014 4.076a3.75 3.75 0 0 1-3.94.674c-.557-.285-1.074-.55-1.61-1.268a3.746 3.746 0 0 1 .74-5.16a3.75 3.75 0 0 1 5.013.37c.31.396.679.793.81 1.308m-5.587 3.88l1.043-.976m4.219-3.585l-.66.537\"/></g>"}, "folder-cargo": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/><path stroke=\"#f5a97f\" stroke-width=\".571\" d=\"M8.286 11.429v2.285l3.714 2l3.714-2V9.143l-2-.857L10 10v.571ZM12 9.143L14 10v4.571M8.286 11.43l3.714 2l3.714-2M10 10l2 1.143l3.714-2M10 10.57v4m2-3.428v4.286\"/></g>"}, "folder-cargo-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.8 7.9l.69-2.7c.11-.44.51-.76.97-.76h11a1.003 1.003 0 0 1 .97 1.24l-.32 1.3m-9.1 6.5h-4c-.83 0-1.5-.67-1.5-1.5v-8.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/><path fill=\"none\" stroke=\"#f5a97f\" stroke-linejoin=\"round\" stroke-width=\".57\" d=\"M8.285 11.432v2.291L12 15.715l3.715-1.992V9.142l-2.008-.857l-3.715 1.693v.568ZM12 9.142l2.008.856v4.582m-5.723-3.088L12 13.484l3.715-1.992m-5.723-1.394L12 11.193l3.715-1.992m-5.723 1.395v3.984M12 11.193v4.283\"/>"}, "folder-circle-ci": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8087a2\"><path d=\"M8.836 13.498a3.5 3.5 0 1 0 .002-3\"/><circle cx=\"12\" cy=\"12\" r=\".5\"/></g></g>"}, "folder-circle-ci-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8087a2\"><path d=\"M8.836 13.498a3.5 3.5 0 1 0 .002-3\"/><circle cx=\"12\" cy=\"12\" r=\".5\"/></g></g>"}, "folder-client": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M15.5 15.5h-7m.8-7h5.4a.8.8 0 0 1 .8.8v3.4a.8.8 0 0 1-.8.8H9.3a.8.8 0 0 1-.8-.8V9.3a.8.8 0 0 1 .8-.8\"/></g>"}, "folder-client-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M15.5 15.5h-7m.8-7h5.4a.8.8 0 0 1 .8.8v3.4a.8.8 0 0 1-.8.8H9.3a.8.8 0 0 1-.8-.8V9.3a.8.8 0 0 1 .8-.8\"/></g>"}, "folder-cloud": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M7.5 12.429c0-1.142.834-2.068 1.863-2.068c.157-.813.717-1.477 1.47-1.741a2.15 2.15 0 0 1 2.177.461c.596.55.865 1.388.708 2.2h.396c.766 0 1.386.72 1.386 1.61c0 .888-.62 1.608-1.386 1.608H9.363c-1.029-.002-1.863-.928-1.863-2.07\"/></g>"}, "folder-cloud-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M7.5 12.429c0-1.142.834-2.068 1.863-2.068c.157-.813.717-1.477 1.47-1.741a2.15 2.15 0 0 1 2.177.461c.596.55.865 1.388.708 2.2h.396c.766 0 1.386.72 1.386 1.61c0 .888-.62 1.608-1.386 1.608H9.363c-1.029-.002-1.863-.928-1.863-2.07\"/></g>"}, "folder-command": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M14.25 13.5a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-5.5a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-.75.75h-5.5A.75.75 0 0 1 8 9.75v-.5a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-.5a.75.75 0 0 1 .75-.75h5z\"/></g>"}, "folder-command-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M14.25 13.5a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-5.5a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-.75.75h-5.5A.75.75 0 0 1 8 9.75v-.5a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-.5a.75.75 0 0 1 .75-.75h5z\"/></g>"}, "folder-components": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m9.485 9.516l3.968 3.968m-3.968 0l3.968-3.968m-1.634-1.874l3.507 3.507a.496.496 0 0 1 0 .702l-3.507 3.507a.496.496 0 0 1-.701 0L7.61 11.85a.496.496 0 0 1 0-.702l3.507-3.507a.496.496 0 0 1 .701 0z\"/></g>"}, "folder-components-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m9.485 9.516l3.968 3.968m-3.968 0l3.968-3.968m-1.634-1.874l3.507 3.507a.496.496 0 0 1 0 .702l-3.507 3.507a.496.496 0 0 1-.701 0L7.61 11.85a.496.496 0 0 1 0-.702l3.507-3.507a.496.496 0 0 1 .701 0z\"/></g>"}, "folder-composables": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"m12.635 15.154l-.515-.892m.513-.895l-.515.892m1.998-1.998l.276.554a1 1 0 0 1-.894 1.447H12.12m-1.502-5.004l.104-.207a1 1 0 0 1 1.788 0l.608 1.215m-1.119-.071l1.122.067m.615-.932l-.619.938m-2.996 3.994h-.387a1 1 0 0 1-.894-1.447l.776-1.553m.616.934l-.616-.934m-1.117.067l1.117-.067\"/></g>"}, "folder-composables-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"m12.635 15.154l-.515-.892m.513-.895l-.515.892m1.998-1.998l.276.554a1 1 0 0 1-.894 1.447H12.12m-1.502-5.004l.104-.207a1 1 0 0 1 1.788 0l.608 1.215m-1.119-.071l1.122.067m.615-.932l-.619.938m-2.996 3.994h-.387a1 1 0 0 1-.894-1.447l.776-1.553m.616.934l-.616-.934m-1.117.067l1.117-.067\"/></g>"}, "folder-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M11.498 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.752-4L15 12l-1.75 3h-3.5L8 12l1.75-3z\"/></g>"}, "folder-config-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M11.498 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.752-4L15 12l-1.75 3h-3.5L8 12l1.75-3z\"/></g>"}, "folder-connection": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"M14.001 12.437a2 2 0 1 0-2.439-2.434M10.5 13.5l3-3m-3.497 1.062A2 2 0 1 0 12.437 14\"/></g>"}, "folder-connection-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"M14.001 12.437a2 2 0 1 0-2.439-2.434M10.5 13.5l3-3m-3.497 1.062A2 2 0 1 0 12.437 14\"/></g>"}, "folder-constant": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M9.5 14.75q1-1.5 1-6.25m4 0H10q-1 0-1.5 1.25m5-1l-.25 3.75c0 1.5.5 2 1.25 2q.75 0 1-1.5\"/></g>"}, "folder-constant-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M9.5 14.75q1-1.5 1-6.25m4 0H10q-1 0-1.5 1.25m5-1l-.25 3.75c0 1.5.5 2 1.25 2q.75 0 1-1.5\"/></g>"}, "folder-content": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.038 8.5h5.924a.54.54 0 0 1 .538.538v5.385a1.077 1.077 0 0 1-1.077 1.077H9.577A1.077 1.077 0 0 1 8.5 14.423V9.038a.54.54 0 0 1 .538-.538M8.5 12h7M12 13.5\"/>"}, "folder-content-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.038 8.5h5.924a.54.54 0 0 1 .538.538v5.385a1.077 1.077 0 0 1-1.077 1.077H9.577A1.077 1.077 0 0 1 8.5 14.423V9.038a.54.54 0 0 1 .538-.538M8.5 12h7M12 13.5\"/>"}, "folder-controllers": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M11.5 12.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m-1.5-4l1.503-1L13 8.5m-3 6l1.503 1l1.497-1M8.5 13l-1-1.51l1-1.49m6 3l1-1.51l-1-1.49\"/></g>"}, "folder-controllers-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M11.5 12.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m-1.5-4l1.503-1L13 8.5m-3 6l1.503 1l1.497-1M8.5 13l-1-1.51l1-1.49m6 3l1-1.51l-1-1.49\"/></g>"}, "folder-core": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M10.5 9.5h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m0 0v-1m3 1v-1m-3 7v-1m3 1v-1m-5-4h1m-1 3h1m5-3h1m-1 3h1\"/></g>"}, "folder-core-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M10.5 9.5h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m0 0v-1m3 1v-1m-3 7v-1m3 1v-1m-5-4h1m-1 3h1m5-3h1m-1 3h1\"/></g>"}, "folder-coverage": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M12 8L8.5 9.167v2.125c0 2.122 1.195 3.641 3.5 4.208c2.361-.58 3.5-2.087 3.5-4.208V9.167Zm-1.502 3.5l1.504 1.5l1.5-2.5\"/></g>"}, "folder-coverage-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M12 8L8.5 9.167v2.125c0 2.122 1.195 3.641 3.5 4.208c2.361-.58 3.5-2.087 3.5-4.208V9.167Zm-1.502 3.5l1.504 1.5l1.5-2.5\"/></g>"}, "folder-cypress": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M11 12.157a1.41 1.41 0 0 1-1.387.403c-.637-.168-1.091-.763-1.112-1.457s.396-1.32 1.022-1.53a1.41 1.41 0 0 1 1.476.374M14.5 12.5l-1.5-3m.5 6l2-6\"/></g>"}, "folder-cypress-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M11 12.157a1.41 1.41 0 0 1-1.387.403c-.637-.168-1.091-.763-1.112-1.457s.396-1.32 1.022-1.53a1.41 1.41 0 0 1 1.476.374M14.5 12.5l-1.5-3m.5 6l2-6\"/></g>"}, "folder-database": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M8.5 9.5q1 1 3.5 1t3.5-1m-7 2.5q1 1 3.5 1t3.5-1m-7 2.5v-5q1-1 3.5-1t3.5 1v5q-1 1-3.5 1t-3.5-1\"/></g>"}, "folder-database-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M8.5 9.5q1 1 3.5 1t3.5-1m-7 2.5q1 1 3.5 1t3.5-1m-7 2.5v-5q1-1 3.5-1t3.5 1v5q-1 1-3.5 1t-3.5-1\"/></g>"}, "folder-debug": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ed8796\" d=\"M12.5 10.5v-1a1 1 0 1 0-2 0v1m2.667 0c.248.372.308 1.054.333 1.5v1.5c0 1.15-.85 2-2 2s-2-.85-2-2V12c.025-.446.085-1.128.333-1.5zm-5.667 2h2m4 0h2m-4 3V13M8 15.5l1.5-1m5.5 1l-1.5-1M8 9.5l1.5 1m5.5-1l-1.5 1\"/></g>"}, "folder-debug-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ed8796\" d=\"M12.5 10.5v-1a1 1 0 1 0-2 0v1m2.667 0c.248.372.308 1.054.333 1.5v1.5c0 1.15-.85 2-2 2s-2-.85-2-2V12c.025-.446.085-1.128.333-1.5zm-5.667 2h2m4 0h2m-4 3V13M8 15.5l1.5-1m5.5 1l-1.5-1M8 9.5l1.5 1m5.5-1l-1.5 1\"/></g>"}, "folder-devcontainer": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M8.5 9.5V14l3.5 1.5l3.5-1.5V9.5L12 8Z\"/><path stroke=\"#91d7e3\" d=\"m10.5 10.5l1.5.5l1.5-.5M12 11v2.5\"/></g>"}, "folder-devcontainer-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M8.5 9.5V14l3.5 1.5l3.5-1.5V9.5L12 8Z\"/><path stroke=\"#91d7e3\" d=\"m10.5 10.5l1.5.5l1.5-.5M12 11v2.5\"/></g>"}, "folder-direnv": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M13.5 9.876c0-.729-.72-1.32-1.6-1.32h-.8c-.88 0-1.6.591-1.6 1.32s.72 1.32 1.6 1.32h.8c.88 0 1.6.592 1.6 1.32s-.72 1.32-1.6 1.32h-.8c-.88 0-1.6-.591-1.6-1.32m2-5.016v1.056m0 5.28v1.057\"/></g>"}, "folder-direnv-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 9.876c0-.729-.72-1.32-1.6-1.32h-.8c-.88 0-1.6.591-1.6 1.32s.72 1.32 1.6 1.32h.8c.88 0 1.6.592 1.6 1.32s-.72 1.32-1.6 1.32h-.8c-.88 0-1.6-.591-1.6-1.32m2-5.016v1.056m0 5.28v1.057\"/>"}, "folder-dist": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ed8796\" d=\"M9.5 10.5h5a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m1-2h3v2h-3z\"/></g>"}, "folder-dist-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ed8796\" d=\"M9.5 10.5h5a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m1-2h3v2h-3z\"/></g>"}, "folder-docker": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8aadf4\"><path d=\"M8.5 11.5h5l.25-.5c-.151-.555-.465-.945-.25-1.5c.47.297.65.937 1 1c.357-.044 1-1 1 0c0 0 0 1.5-1 1.5c-.5 2-1.75 2.5-3.5 2.5c-2.75 0-2.5-3-2.5-3\"/><path d=\"M9.5 11.5v-2h2v2\"/></g></g>"}, "folder-docker-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8aadf4\"><path d=\"M8.5 11.5h5l.25-.5c-.151-.555-.465-.945-.25-1.5c.47.297.65.937 1 1c.357-.044 1-1 1 0c0 0 0 1.5-1 1.5c-.5 2-1.75 2.5-3.5 2.5c-2.75 0-2.5-3-2.5-3\" transform=\"matrix(.99904 0 0 1 .008 0)\"/><path d=\"M9.5 11.5v-2h2v2\" transform=\"matrix(.99904 0 0 1 .008 0)\"/></g></g>"}, "folder-docs": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M8.5 14.5v-5a1 1 0 0 1 1-1h6v6m-6-1h6v2h-6a1 1 0 1 1 0-2\"/></g>"}, "folder-docs-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M8.5 14.5v-5a1 1 0 0 1 1-1h6v6m-6-1h6v2h-6a1 1 0 1 1 0-2\"/></g>"}, "folder-download": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M9.5 15.5h4m-2-7v5m-2-2l2 2l2-2\"/></g>"}, "folder-download-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M9.5 15.5h4m-2-7v5m-2-2l2 2l2-2\"/></g>"}, "folder-drizzle-orm": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M.027 11.07L1.74 7.641m-5.143 6l1.714-3.429m-3.43.857l1.715-3.429m-5.143 6l1.714-3.429\" transform=\"matrix(.8418 0 0 .8401 14.11 4.099)\"/><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/></g>"}, "folder-drizzle-orm-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M.027 11.07L1.74 7.641m-5.143 6l1.714-3.429m-3.43.857l1.715-3.429m-5.143 6l1.714-3.429\" transform=\"matrix(.8418 0 0 .8401 14.11 4.099)\"/><path stroke=\"#cad3f5\" d=\"m1.873 8l.702-2.74a1.002 1 0 0 1 .961-.76h10.96a1.002 1 0 0 1 .973 1.24l-.22.875M6.009 13.5H2.001A1.503 1.5 0 0 1 .498 12V3.5a1.002 1 0 0 1 1.002-1h5.01a1.002 1 0 0 1 1.002 1v1\"/></g>"}, "folder-examples": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.5 7.5c-1.682 0-3 1.259-3 2.806c0 1.165.623 1.72 1.115 2.126c.316.261.887.418.887.612v.424c-.006.248-.004.505-.004.756c0 .763.332 1.276 1.012 1.276s.992-.513.992-1.276c0-.25.001-.517-.004-.756v-.424c0-.295.596-.473.957-.763c.488-.393 1.045-.88 1.045-1.975c0-1.547-1.318-2.806-3-2.806m-.998 5.956h1.996\"/></g>"}, "folder-examples-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.5 7.5c-1.682 0-3 1.259-3 2.806c0 1.165.623 1.72 1.115 2.126c.316.261.887.418.887.612v.424c-.006.248-.004.505-.004.756c0 .763.332 1.276 1.012 1.276s.992-.513.992-1.276c0-.25.001-.517-.004-.756v-.424c0-.295.596-.473.957-.763c.488-.393 1.045-.88 1.045-1.975c0-1.547-1.318-2.806-3-2.806m-.998 5.956h1.996\"/></g>"}, "folder-fastlane": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.25 15.5L8 11.5\"/><path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.75 15.5h-4.5\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m15 11.5l-1.25 4\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 11.5l3.5-3l3.5 3\"/><path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 11.5l3.5-3\"/>"}, "folder-fastlane-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.25 15.5L8 11.5\"/><path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.75 15.5h-4.5\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m15 11.5l-1.25 4\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 11.5l3.5-3l3.5 3\"/><path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 11.5l3.5-3\"/>"}, "folder-firebase": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"m9.5 13.75l5-4.25l1 4.25l-3 1.75zm0 0l2-6.25l1.5 3\"/></g>"}, "folder-firebase-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"m9.5 13.75l5-4.25l1 4.25l-3 1.75zm0 0l2-6.25l1.5 3\"/></g>"}, "folder-fonts": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f0c6c6\" d=\"M9.163 15.477L11.999 8.5l2.837 6.977M8.5 15.5h1.477m4.046 0H15.5m-5.5-2h4\"/></g>"}, "folder-fonts-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f0c6c6\" d=\"M9.163 15.477L11.999 8.5l2.837 6.977M8.5 15.5h1.477m4.046 0H15.5m-5.5-2h4\"/></g>"}, "folder-forgejo": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" stroke-width=\".65\" d=\"M14.48 8.724a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m-3.834 5.547v-3.593c0-1.079.841-1.954 1.879-1.954h.601\"/><path stroke=\"#ed8796\" stroke-width=\".65\" d=\"M11.33 14.97a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m3.157-3.59a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m-3.834 2.89v-.937c0-1.078.841-1.952 1.879-1.952h.601\"/><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/></g>"}, "folder-forgejo-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" stroke-width=\".65\" d=\"M14.48 8.724a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m-3.834 5.546v-3.593c0-1.079.841-1.953 1.879-1.953h.601\"/><path stroke=\"#ed8796\" stroke-width=\".65\" d=\"M11.33 14.97a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m3.157-3.591a.677.703 0 0 1-.677.703a.677.703 0 0 1-.676-.703a.677.703 0 0 1 .676-.703a.677.703 0 0 1 .677.703m-3.834 2.89v-.937c0-1.078.841-1.952 1.879-1.952h.601\"/><path stroke=\"#cad3f5\" d=\"m1.873 8l.702-2.74a1.002 1 0 0 1 .961-.76h10.96a1.002 1 0 0 1 .973 1.24l-.22.875M6.009 13.5H2.001A1.503 1.5 0 0 1 .498 12V3.5a1.002 1 0 0 1 1.002-1h5.01a1.002 1 0 0 1 1.002 1v1\"/></g>"}, "folder-functions": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M7 14.75c0 .583.417.75 1 .75s.556-1.556.945-3.5c.388-1.945.472-3.5 1.055-3.5s1 .167 1 .75M7.778 11.5h2.333m5.389 2H15c-1.5 0-1.818-3-3-3h-.5m0 3h.5c1.5 0 1.818-3 3-3h.5\"/></g>"}, "folder-functions-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M7 14.75c0 .583.417.75 1 .75s.556-1.556.945-3.5c.388-1.945.472-3.5 1.055-3.5s1 .167 1 .75M7.778 11.5h2.333m5.389 2H15c-1.5 0-1.818-3-3-3h-.5m0 3h.5c1.5 0 1.818-3 3-3h.5\"/></g>"}, "folder-fvm": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/><path stroke=\"#c6a0f6\" stroke-width=\".65\" d=\"m9.871 12.94l-2.818-2.737l1.974.003l2.339 2.255c.27.269.27.7 0 .959l-2.3 2.257H7.053zm5.808 2.737v-1.326h-3.738l.013 1.323z\"/></g>"}, "folder-fvm-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#c6a0f6\" stroke-width=\".65\" d=\"M9.868 12.94L7.05 10.203l1.974.003l2.339 2.255c.27.269.27.7 0 .959l-2.3 2.257H7.05zm5.808 2.737v-1.326h-3.738l.013 1.323z\"/><path stroke=\"#cad3f5\" d=\"m1.873 8l.702-2.74a1.002 1 0 0 1 .961-.76h10.96a1.002 1 0 0 1 .973 1.24l-.22.875M6.009 13.5H2.001A1.503 1.5 0 0 1 .498 12V3.5a1.002 1 0 0 1 1.002-1h5.01a1.002 1 0 0 1 1.002 1v1\"/></g>"}, "folder-git": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M10.5 10.5v3m-.75-4.75L8 7m4.75 4.75l-1.5-1.5m-.75 5.25a1 1 0 1 0 0-2a1 1 0 0 0 0 2m0-5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m3 3a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/></g>"}, "folder-git-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M10.5 10.5v3m-.75-4.75L8 7m4.75 4.75l-1.5-1.5m-.75 5.25a1 1 0 1 0 0-2a1 1 0 0 0 0 2m0-5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m3 3a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/></g>"}, "folder-github": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M10 14.907c-1.5.5-1.25-.657-2-.907m5 1.5v-1.062c0-.447-.238-.67-.5-.938c1.225-.134 2.5-.58 2.5-2.633c0-.534-.205-.965-.569-1.348a1.9 1.9 0 0 0-.043-1.428s-.482-.134-1.532.58a5.3 5.3 0 0 0-2.712 0c-1.05-.714-1.531-.58-1.531-.58a1.9 1.9 0 0 0-.044 1.428c-.364.383-.568.814-.569 1.348c0 2.053 1.275 2.5 2.5 2.633c-.262.268-.544.58-.5.938V15.5\"/></g>"}, "folder-github-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M10 14.907c-1.5.5-1.25-.657-2-.907m5 1.5v-1.062c0-.447-.238-.67-.5-.938c1.225-.134 2.5-.58 2.5-2.633c0-.534-.205-.965-.569-1.348a1.9 1.9 0 0 0-.043-1.428s-.482-.134-1.532.58a5.3 5.3 0 0 0-2.712 0c-1.05-.714-1.531-.58-1.531-.58a1.9 1.9 0 0 0-.044 1.428c-.364.383-.568.814-.569 1.348c0 2.053 1.275 2.5 2.5 2.633c-.262.268-.544.58-.5.938V15.5\"/></g>"}, "folder-gitlab": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" stroke-linecap=\"square\" d=\"m12 15.5l3.5-2.515l-1-4.485l-1.5 2h-2l-1.5-2l-1 4.485z\"/></g>"}, "folder-gitlab-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" stroke-linecap=\"square\" d=\"m12 15.5l3.5-2.515l-1-4.485l-1.5 2h-2l-1.5-2l-1 4.485z\"/></g>"}, "folder-gradle": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.855\"><path d=\"m7.5 7.5l-1.97.78c-.2.15-.74.33-1.2-.2a7.3 7.3 0 0 1-1-1.63\" transform=\"matrix(.53379 0 0 .54472 7.231 7.141)\"/><path d=\"M11.43 2.98c.54-.48 4.07-1.39 4.07 2.1c0 2.34-1.6 3.53-2.76 4.5c-.75.61-1.35 1.28-1.35 3.92H9.88c0-1.29-2.66-2.32-3.17 0H5.38c0-1.29-2.65-2.32-3.16 0H.7C.29 11.92.22 8.3 3.33 6.45c-.15-.25-.27-1.02.43-1.3c.87-.34 3.77-1.34 6.51.12s5.01 1.12 5.14.74\" transform=\"matrix(.53379 0 0 .54472 7.231 7.141)\"/></g>"}, "folder-gradle-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.855\"><path d=\"m7.5 7.5l-1.97.78c-.2.15-.74.33-1.2-.2a7.3 7.3 0 0 1-1-1.63\" transform=\"matrix(.53379 0 0 .54472 7.231 7.141)\"/><path d=\"M11.43 2.98c.54-.48 4.07-1.39 4.07 2.1c0 2.34-1.6 3.53-2.76 4.5c-.75.61-1.35 1.28-1.35 3.92H9.88c0-1.29-2.66-2.32-3.17 0H5.38c0-1.29-2.65-2.32-3.16 0H.7C.29 11.92.22 8.3 3.33 6.45c-.15-.25-.27-1.02.43-1.3c.87-.34 3.77-1.34 6.51.12s5.01 1.12 5.14.74\" transform=\"matrix(.53379 0 0 .54472 7.231 7.141)\"/></g>"}, "folder-hooks": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M12.5 9.5c0 1.333 2 1.333 2 0s-2-1.333-2 0m1 1v2.668c-.003 3.109-4.003 3.109-4 0V11.5L11 13\"/></g>"}, "folder-hooks-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M12.5 9.5c0 1.333 2 1.333 2 0s-2-1.333-2 0m1 1v2.668c-.003 3.109-4.003 3.109-4 0V11.5L11 13\"/></g>"}, "folder-husky": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f4dbd6\" d=\"m9.06 12.284l2.118-.774c.65-.238 1.36.163 1.474.831l.372 2.18c.12.702-.65 1.224-1.28.868a.6.6 0 0 1-.207-.19l-.11-.162A2.63 2.63 0 0 0 9.4 13.893l-.198-.011a.6.6 0 0 1-.271-.08a.834.834 0 0 1 .128-1.518\"/><path fill=\"#f4dbd6\" d=\"M9.224 10.801c.398.23.944.036 1.22-.435c.276-.47.176-1.039-.222-1.27c-.398-.23-.944-.035-1.22.436c-.275.47-.176 1.04.222 1.27m2.62-1c.398.23.944.036 1.22-.435c.275-.47.176-1.039-.222-1.27c-.398-.23-.944-.035-1.22.436c-.275.47-.176 1.04.222 1.27m2.045 1c.398.23.944.036 1.22-.435c.276-.47.177-1.039-.221-1.27c-.398-.23-.944-.035-1.22.436c-.276.47-.177 1.04.221 1.27m.688 2.793c.398.23.945.035 1.22-.436c.276-.47.177-1.039-.22-1.269c-.399-.23-.945-.035-1.22.435c-.277.471-.178 1.04.22 1.27\"/></g>"}, "folder-husky-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f4dbd6\" d=\"m9.06 12.285l2.118-.775c.65-.238 1.36.163 1.474.831l.372 2.18c.12.702-.65 1.224-1.28.868a.6.6 0 0 1-.207-.19l-.11-.162A2.63 2.63 0 0 0 9.4 13.893l-.198-.011a.6.6 0 0 1-.271-.08a.834.834 0 0 1 .128-1.517\"/><path fill=\"#f4dbd6\" d=\"M9.224 10.801c.398.23.944.036 1.22-.435c.276-.47.176-1.039-.222-1.27c-.398-.23-.944-.035-1.22.436c-.275.47-.176 1.04.222 1.27m2.62-1c.398.23.944.036 1.22-.435c.275-.47.176-1.039-.222-1.27c-.398-.23-.944-.035-1.22.436c-.275.47-.176 1.04.222 1.27m2.045 1c.398.23.944.036 1.22-.435c.276-.47.177-1.039-.221-1.27c-.398-.23-.944-.035-1.22.436c-.276.47-.177 1.04.221 1.27m.688 2.793c.398.23.945.035 1.22-.436c.276-.47.177-1.039-.22-1.269c-.399-.23-.945-.035-1.22.435c-.277.471-.178 1.04.22 1.27\"/></g>"}, "folder-images": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M14.5 15.5L11 12l-2.5 2.5\"/><path stroke=\"#7dc4e4\" d=\"M9.5 8.5h4.997a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H9.5a1 1 0 0 1-1-1v-5a1 1 0 0 1 1-1\"/><circle cx=\"13.5\" cy=\"10.5\" r=\".5\" stroke=\"#eed49f\"/></g>"}, "folder-images-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M14.5 15.5L11 12l-2.5 2.5\"/><path stroke=\"#7dc4e4\" d=\"M9.5 8.5h4.997a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H9.5a1 1 0 0 1-1-1v-5a1 1 0 0 1 1-1\"/><circle cx=\"13.5\" cy=\"10.5\" r=\".5\" stroke=\"#eed49f\"/></g>"}, "folder-intellij": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"m8.5 15.5l3.057.008\"/><path stroke=\"#ed8796\" d=\"M10 8.5v4m-1-4h2m-2 4h2\"/><path stroke=\"#8aadf4\" d=\"M14.5 8.5v3s0 1-1 1s-1-1-1-1\"/></g>"}, "folder-intellij-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"m8.5 15.5l3.057.008\"/><path stroke=\"#ed8796\" d=\"M10 8.5v4m-1-4h2m-2 4h2\"/><path stroke=\"#8aadf4\" d=\"M14.5 8.5v3s0 1-1 1s-1-1-1-1\"/></g>"}, "folder-kubernetes": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8aadf4\"><circle cx=\"11.494\" cy=\"11.509\" r=\"2.992\"/><path d=\"M11.494 11.51V7.47m0 4.04L9.389 9.83l-1.053-.84m3.158 2.52l1.168 2.425l.584 1.214m-1.752-3.64l2.625.6l1.312.3m-3.937-.9l2.105-1.679l1.052-.84m-3.157 2.52l-2.625.598l-1.313.3m3.938-.899l-1.169 2.426l-.584 1.213\"/></g></g>"}, "folder-kubernetes-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#8aadf4\"><circle cx=\"11.494\" cy=\"11.509\" r=\"2.992\"/><path d=\"M11.494 11.51V7.47m0 4.04L9.389 9.83l-1.053-.84m3.158 2.52l1.168 2.425l.584 1.214m-1.752-3.64l2.625.6l1.312.3m-3.937-.9l2.105-1.679l1.052-.84m-3.157 2.52l-2.625.598l-1.313.3m3.938-.899l-1.169 2.426l-.584 1.213\"/></g></g>"}, "folder-layouts": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M9 8.5h6a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-.5.5H9a.5.5 0 0 1-.5-.5V9a.5.5 0 0 1 .5-.5m-.5 2h7m-5 .5v4\"/></g>"}, "folder-layouts-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M9 8.5h6a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-.5.5H9a.5.5 0 0 1-.5-.5V9a.5.5 0 0 1 .5-.5m-.5 2h7m-5 .5v4\"/></g>"}, "folder-locales": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M12 14.5h3m-3.5 1l2-4l2 4m-6-4s.556 1.734 3 2m-1-3.5s.148 3.3-3 3.5H8m0-4h5M10.5 8v1.5\"/></g>"}, "folder-locales-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M12 14.5h3m-3.5 1l2-4l2 4m-6-4s.556 1.734 3 2m-1-3.5s.148 3.3-3 3.5H8m0-4h5M10.5 8v1.5\"/></g>"}, "folder-middleware": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M15.5 13.5v1.2a.8.8 0 0 1-.8.8h-4.4a.8.8 0 0 1-.8-.8v-1.2H9a1 1 0 0 1 0-2h.5v-1.2a.8.8 0 0 1 .8-.8h1.2V9a1 1 0 0 1 2 0v.5h1.2a.8.8 0 0 1 .8.8v1.2H15a1 1 0 0 0 0 2z\"/></g>"}, "folder-middleware-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M15.5 13.5v1.2a.8.8 0 0 1-.8.8h-4.4a.8.8 0 0 1-.8-.8v-1.2H9a1 1 0 0 1 0-2h.5v-1.2a.8.8 0 0 1 .8-.8h1.2V9a1 1 0 0 1 2 0v.5h1.2a.8.8 0 0 1 .8.8v1.2H15a1 1 0 0 0 0 2z\"/></g>"}, "folder-mocks": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"m10.327 15.5l4.795-4.795a1.292 1.292 0 1 0-1.827-1.827L8.5 13.673V15.5zm2.511-6.165l1.827 1.827\"/></g>"}, "folder-mocks-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"m10.327 15.5l4.795-4.795a1.292 1.292 0 1 0-1.827-1.827L8.5 13.673V15.5zm2.511-6.165l1.827 1.827\"/></g>"}, "folder-next": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M15.163 13.501a3.5 3.5 0 1 0-1.238 1.423m-.005-.004L10.5 10.5v3m3-3v1\"/></g>"}, "folder-next-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"M15.163 13.501a3.5 3.5 0 1 0-1.238 1.423m-.005-.004L10.5 10.5v3m3-3v1\"/></g>"}, "folder-nix": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M8 11.557h1.634m.648-.933L8.724 13.19\"/><path stroke=\"#8aadf4\" d=\"m9.634 8.757l.7 1.167m1.633.233h-3.01\"/><path stroke=\"#7dc4e4\" d=\"m13.367 8.757l-.7 1.4m.471 1.213l-1.404-2.613\"/><path stroke=\"#8aadf4\" d=\"m15 12.033l-1.633-.01m-.644.934L14.3 10.39\"/><path stroke=\"#7dc4e4\" d=\"m13.6 14.824l-1.166-1.4m-1.386.009l3.024-.01\"/><path stroke=\"#8aadf4\" d=\"m9.634 14.824l.7-1.4m-.453-1.167l1.386 2.567\"/></g>"}, "folder-nix-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M8 11.557h1.634m.648-.933L8.724 13.19\"/><path stroke=\"#8aadf4\" d=\"m9.634 8.757l.7 1.167m1.633.233h-3.01\"/><path stroke=\"#7dc4e4\" d=\"m13.367 8.757l-.7 1.4m.471 1.213l-1.404-2.613\"/><path stroke=\"#8aadf4\" d=\"m15 12.033l-1.633-.01m-.644.934L14.3 10.39\"/><path stroke=\"#7dc4e4\" d=\"m13.6 14.824l-1.166-1.4m-1.386.009l3.024-.01\"/><path stroke=\"#8aadf4\" d=\"m9.634 14.824l.7-1.4m-.453-1.167l1.386 2.567\"/></g>"}, "folder-node": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m12.5 8.576l3 1.714v3.42l-3 1.714l-3-1.714v-3.42z\"/></g>"}, "folder-node-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m12.5 8.576l3 1.714v3.42l-3 1.714l-3-1.714v-3.42z\"/></g>"}, "folder-nuxt": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M12.865 14.5H15.5L13.383 11l-1.667 2.722c-.017.03-.054.106-.105.178a3 3 0 0 1-.197.259a.8.8 0 0 1-.173.159a1 1 0 0 1-.225.102a1.5 1.5 0 0 1-.246.054c-.082.012-.205.016-.246.02c-.021 0-1.09.006-1.696.006c-.468.014-.363-.257-.206-.5L11 10l1.407 2.347\"/></g>"}, "folder-nuxt-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M12.865 14.5H15.5L13.383 11l-1.667 2.722c-.017.03-.054.106-.105.178a3 3 0 0 1-.197.259a.8.8 0 0 1-.173.159a1 1 0 0 1-.225.102a1.5 1.5 0 0 1-.246.054c-.082.012-.205.016-.246.02c-.021 0-1.09.006-1.696.006c-.468.014-.363-.257-.206-.5L11 10l1.407 2.347\"/></g>"}, "folder-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-1.75 7a1 1 0 0 1-.97.76H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/>"}, "folder-packages": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M12 15.337v-3.919L8.5 9.214m3.5 2.204l3.5-2.204M12 7.5l3.5 1.714v4.408L12 15.5l-3.5-1.878V9.214Z\"/></g>"}, "folder-packages-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M12 15.337v-3.919L8.5 9.214m3.5 2.204l3.5-2.204M12 7.5l3.5 1.714v4.408L12 15.5l-3.5-1.878V9.214Z\"/></g>"}, "folder-plugins": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m8.518 15.5l1.015-1.016m3.969-5.968l-2 1.99m3.998.01l-1.998 2m-2.97-2.625l3.595 3.594l-.899.898a2.542 2.542 0 1 1-3.594-3.594Z\"/></g>"}, "folder-plugins-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m8.518 15.5l1.015-1.016m3.969-5.968l-2 1.99m3.998.01l-1.998 2m-2.97-2.625l3.595 3.594l-.899.898c-.57.629-1.545.914-2.477.704c-.899-.202-1.589-.928-1.82-1.82a2.54 2.54 0 0 1 .703-2.478Z\"/></g>"}, "folder-pre-commit": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><rect width=\"6.034\" height=\"6.034\" x=\"-3.015\" y=\"13.247\" fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\".635\" ry=\".635\" transform=\"rotate(-45.004)\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.62 13.118v-3.2h1.612s.662.096.662.85c0 .753-.662.9-.662.9h-1.555\"/>"}, "folder-pre-commit-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><rect width=\"6.034\" height=\"6.034\" x=\"-3.015\" y=\"13.247\" fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\".635\" ry=\".635\" transform=\"rotate(-45.004)\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.62 13.118v-3.2h1.612s.662.096.662.85c0 .753-.662.9-.662.9h-1.555\"/>"}, "folder-prisma": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m15.5 14.25l-5 1.25l-2-2.25L12 8.5zM12 8.5l-1.5 7\"/></g>"}, "folder-prisma-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m15.5 14.25l-5 1.25l-2-2.25L12 8.5zM12 8.5l-1.5 7\"/></g>"}, "folder-public": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#7dc4e4\" d=\"M8.5 12h7M12 15.5c-1.933 0-3.5-1.5-3.5-3.5s1.567-3.5 3.5-3.5c2 0 3.5 1.5 3.5 3.5S14 15.5 12 15.5M11.556 9c-1.379 2.01-1.448 4.01.087 6.34M12.454 9c1.361 1.98 1.45 3.98-.062 6.34\"/></g>"}, "folder-public-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#7dc4e4\" d=\"M8.5 12h7M12 15.5c-1.933 0-3.5-1.5-3.5-3.5s1.567-3.5 3.5-3.5c2 0 3.5 1.5 3.5 3.5S14 15.5 12 15.5M11.556 9c-1.38 2.01-1.448 4.01.087 6.34M12.454 9c1.36 1.98 1.45 3.98-.062 6.34\"/></g>"}, "folder-queue": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M11.5 7.5L8 9.5l3.5 2l3.5-2zm-3.5 4l3.5 2l3.5-2m-7 2l3.5 2l3.5-2\"/></g>"}, "folder-queue-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M11.5 7.5L8 9.5l3.5 2l3.5-2zm-3.5 4l3.5 2l3.5-2m-7 2l3.5 2l3.5-2\"/></g>"}, "folder-redux": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M13.436 10.47c-.138-1.125-.735-1.97-1.452-1.97c-.818 0-1.481 1.104-1.481 2.466c0 .806.232 1.522.592 1.972m1.695 1.905c.894.698 1.915.873 2.421.368c.579-.578.266-1.827-.698-2.79c-.571-.57-1.243-.912-1.815-.977m-3.11 1.092c-.887.76-1.384 2.137-.877 2.643c.578.578 1.83.265 2.794-.698c.57-.57.913-1.24.978-1.812\"/></g>"}, "folder-redux-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M13.436 10.47c-.138-1.125-.736-1.97-1.452-1.97c-.818 0-1.482 1.104-1.482 2.466c0 .806.233 1.522.592 1.972m1.696 1.905c.893.698 1.914.873 2.42.368c.58-.578.267-1.827-.698-2.79c-.57-.57-1.242-.912-1.815-.977m-3.11 1.092c-.886.76-1.383 2.137-.877 2.643c.58.578 1.83.266 2.795-.698c.57-.57.913-1.24.978-1.812\"/></g>"}, "folder-renovate": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/><path stroke=\"#cad3f5\" stroke-width=\".613\" d=\"M13.4 13.41c-.123-.184-.203-.325-.153-.52a.7.7 0 0 1 .19-.32c.786-.771 1.486-1.44 2.125-2.248c.055-.074.08-.166.104-.252c.019-.073.019-.153.019-.232a.6.6 0 0 0-.031-.215a4.237 4.228 0 0 0-.86-1.336c-.086-.061-.172-.123-.27-.165a.676.674 0 0 0-.221-.055c-.098 0-.203.006-.295.03c-.104.03-.178.08-.282.141m.19 7.445l-.823-1.385a.43.429 0 0 1-.049-.343a.448.447 0 0 1 .215-.27l.473-.263a.473.472 0 0 1 .626.165l.823 1.385\"/><path stroke=\"#8bd5ca\" stroke-width=\".613\" d=\"m8.076 9.968l3.929-2.209a1.173 1.173 0 0 1 1.588.423a1.12 1.12 0 0 1-.417 1.54l-3.929 2.221a1.176 1.176 0 0 1-1.588-.43a1.12 1.12 0 0 1 .417-1.545\"/></g>"}, "folder-renovate-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.873 8l.702-2.74a1.002 1 0 0 1 .961-.76h10.96a1.002 1 0 0 1 .973 1.24l-.22.875M6.009 13.5H2.001A1.503 1.5 0 0 1 .498 12V3.5a1.002 1 0 0 1 1.002-1h5.01a1.002 1 0 0 1 1.002 1v1\"/><path stroke=\"#cad3f5\" stroke-width=\".613\" d=\"M13.41 13.42c-.122-.184-.202-.325-.153-.522a.7.7 0 0 1 .19-.319c.783-.773 1.48-1.442 2.117-2.252c.055-.073.08-.165.104-.251c.018-.074.018-.154.018-.233a.6.6 0 0 0-.03-.215a4.222 4.234 0 0 0-.857-1.338a1.5 1.5 0 0 0-.27-.166a.673.675 0 0 0-.22-.055a1.2 1.2 0 0 0-.293.03c-.104.031-.178.08-.282.142m.19 7.455l-.82-1.387a.428.43 0 0 1-.05-.344a.447.448 0 0 1 .215-.27l.471-.264a.471.473 0 0 1 .624.166l.82 1.387\"/><path stroke=\"#8bd5ca\" stroke-width=\".613\" d=\"m8.076 9.968l3.929-2.209a1.173 1.173 0 0 1 1.588.423a1.12 1.12 0 0 1-.417 1.54l-3.929 2.221a1.176 1.176 0 0 1-1.588-.43a1.12 1.12 0 0 1 .417-1.545\"/></g>"}, "folder-routes": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M11.5 11.5v4m0-7.5v1.5m-1.5 6h3m-4.5-6v2h5.75l1.25-1l-1.25-1z\"/></g>"}, "folder-routes-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"M11.5 11.5v4m0-7.5v1.5m-1.5 6h3m-4.5-6v2h5.75l1.25-1l-1.25-1z\"/></g>"}, "folder-sass": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5bde6\" d=\"M11.82 11.127c1.006.575 1.824.401 2.635-.109s1.471-1.495.707-2.231c-.383-.368-1.774-.43-3.063.1c-1.29.531-2.504 1.627-2.588 2.215c-.169 1.177 1.72 1.551 1.986 2.278s.152 1.063-.113 1.52c-.264.458-1.067.876-1.521.367s.908-1.48 1.62-1.75c.711-.27 1.698-.212 2.014.135c.317.347-.035.964-.014.964\"/></g>"}, "folder-sass-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5bde6\" d=\"M11.82 11.127c1.006.575 1.824.401 2.635-.109s1.471-1.495.707-2.231c-.383-.368-1.774-.43-3.063.1c-1.29.531-2.504 1.627-2.588 2.215c-.169 1.177 1.72 1.551 1.986 2.278s.152 1.063-.113 1.52c-.264.458-1.067.876-1.521.367s.908-1.48 1.62-1.75c.711-.27 1.698-.212 2.014.135c.317.347-.035.964-.014.964\"/></g>"}, "folder-scripts": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M8.5 13.5V9.75C8.5 9 9 8.5 9.75 8.5h3.75v5.75c0 .75-.5 1.25-1.25 1.25H8.5a1 1 0 0 1 0-2h3a1 1 0 0 0 0 2m2-5h1a1 1 0 0 0 0-2h-1\"/></g>"}, "folder-scripts-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#c6a0f6\" d=\"M8.5 13.5V9.75C8.5 9 9 8.5 9.75 8.5h3.75v5.75c0 .75-.5 1.25-1.25 1.25H8.5a1 1 0 0 1 0-2h3a1 1 0 0 0 0 2m2-5h1a1 1 0 0 0 0-2h-1\"/></g>"}, "folder-security": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#f0c6c6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.42\" d=\"M7.684 7.575h.01m9.5 2.964h5.122l-3.414 6.918l-3.015-4.269m2.527-5.12l-3.902 7.907l-10.135-5.14a2.848 2.886 0 0 1-1.269-3.863l1.347-2.758A2.848 2.886 0 0 1 8.279 2.93Zm-15.608 9.39h3.668A1.951 1.977 0 0 0 8.23 16.37l1.405-2.866m-6.829 5.93v-3.953\" transform=\"matrix(-.4104 0 0 .41613 16.655 7.41)\"/>"}, "folder-security-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#f0c6c6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.42\" d=\"M7.684 7.575h.01m9.5 2.964h5.122l-3.414 6.918l-3.015-4.269m2.527-5.12l-3.902 7.907l-10.135-5.14a2.848 2.886 0 0 1-1.269-3.863l1.347-2.758A2.848 2.886 0 0 1 8.279 2.93Zm-15.608 9.39h3.668A1.951 1.977 0 0 0 8.23 16.37l1.405-2.866m-6.829 5.93v-3.953\" transform=\"matrix(-.4104 0 0 .41613 16.655 7.41)\"/>"}, "folder-server": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M8.5 9.5h7v2h-7zm2 0v2m-2 2h7v2h-7zm2 0v2\"/></g>"}, "folder-server-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M8.5 9.5h7v2h-7zm2 0v2m-2 2h7v2h-7zm2 0v2\"/></g>"}, "folder-src": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m10.5 8.5l-3 3.5l3 3.5m2-7l3 3.5l-3 3.5\"/></g>"}, "folder-src-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m10.5 8.5l-3 3.5l3 3.5m2-7l3 3.5l-3 3.5\"/></g>"}, "folder-storybook": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1M10 13c0 .55.529 1 1 1s1-.529 1-1s-1-1-1-1s-1-.529-1-1s.529-1 1-1s.902.538 1 1\"/><path stroke=\"#f5bde6\" d=\"M12 8.253v.75M8.5 8.5V15l5 .5l.015-7.5Z\"/></g>"}, "folder-storybook-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1M10 13c0 .55.529 1 1 1s1-.529 1-1s-1-1-1-1s-1-.529-1-1s.529-1 1-1s.902.538 1 1\"/><path stroke=\"#f5bde6\" d=\"M12 8.253v.75M8.5 8.5V15l5 .5l.015-7.5Z\"/></g>"}, "folder-styles": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M8.5 15.5v-1.555a1.556 1.556 0 1 1 1.556 1.555zm7-7a6.22 6.22 0 0 0-4.978 3.967M15.5 8.5a6.22 6.22 0 0 1-3.967 4.978m-.077-2.645a3.5 3.5 0 0 1 1.71 1.711\"/></g>"}, "folder-styles-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"M8.5 15.5v-1.555a1.556 1.556 0 1 1 1.556 1.555zm7-7a6.22 6.22 0 0 0-4.978 3.967M15.5 8.5a6.22 6.22 0 0 1-3.967 4.978m-.077-2.645a3.5 3.5 0 0 1 1.71 1.711\"/></g>"}, "folder-svg": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" stroke-linecap=\"square\" d=\"M11.5 8.5v6m0-6a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1Zm0 7a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1Zm2.598-2.5l-5.196-3m5.196 3a.5.5 0 1 0 .866.5a.5.5 0 0 0-.866-.5ZM8.036 9.5a.5.5 0 1 0 .866.5a.5.5 0 0 0-.866-.5Zm6.062.5l-5.196 3m5.196-3a.5.5 0 1 0 .866-.5a.5.5 0 0 0-.866.5Zm-6.062 3.5a.5.5 0 1 0 .866-.5a.5.5 0 0 0-.866.5Z\"/></g>"}, "folder-svg-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" stroke-linecap=\"square\" d=\"M11.5 8.5v6m0-6a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1Zm0 7a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1Zm2.598-2.5l-5.196-3m5.196 3a.5.5 0 1 0 .866.5a.5.5 0 0 0-.866-.5ZM8.036 9.5a.5.5 0 1 0 .866.5a.5.5 0 0 0-.866-.5Zm6.062.5l-5.196 3m5.196-3a.5.5 0 1 0 .866-.5a.5.5 0 0 0-.866.5Zm-6.062 3.5a.5.5 0 1 0 .866-.5a.5.5 0 0 0-.866.5Z\"/></g>"}, "folder-tauri": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.11 9.812a2.32 2.32 0 0 1 4.383.95a2.326 2.326 0 0 1-1.608 2.329\"/><path fill=\"#91d7e3\" d=\"M12.5 13a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#91d7e3\" d=\"M13.881 14.243a2.32 2.32 0 0 1-4.377-.931a2.326 2.326 0 0 1 1.562-2.342\"/><path fill=\"#eed49f\" d=\"M13.5 11a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "folder-tauri-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#eed49f\" d=\"M11.11 9.812a2.32 2.32 0 0 1 4.383.95a2.326 2.326 0 0 1-1.608 2.329\"/><path fill=\"#91d7e3\" d=\"M12.5 13a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#91d7e3\" d=\"M13.881 14.243a2.32 2.32 0 0 1-4.377-.931a2.326 2.326 0 0 1 1.562-2.342\"/><path fill=\"#eed49f\" d=\"M13.5 11a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "folder-temp": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"M12 15.5a3.5 3.5 0 1 0 0-7a3.5 3.5 0 0 0 0 7m0-5.5v2h1\"/></g>"}, "folder-temp-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"M12 15.5a3.5 3.5 0 1 0 0-7a3.5 3.5 0 0 0 0 7m0-5.5v2h1\"/></g>"}, "folder-templates": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ee99a0\" d=\"M11.5 10.5h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m2-.333v-.834a.833.833 0 0 0-.833-.833H9.333a.833.833 0 0 0-.833.833v3.334c0 .46.373.833.833.833h.834\"/></g>"}, "folder-templates-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#ee99a0\" d=\"M11.5 10.5h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1m2-.333v-.834a.833.833 0 0 0-.833-.833H9.333a.833.833 0 0 0-.833.833v3.334c0 .46.373.833.833.833h.834\"/></g>"}, "folder-tests": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m13.158 9.16l-4.272 4.254c-.558.555-.45 1.31-.064 1.694c.406.403 1.077.64 1.743-.023l4.269-4.25M12.5 8.501l.5.5l1 1l1 1l.5.5m-3.478 2.005H8.96\"/></g>"}, "folder-tests-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m13.158 9.16l-4.272 4.253c-.558.555-.45 1.31-.064 1.694c.406.404 1.077.641 1.743-.022l4.269-4.25M12.5 8.5l.5.5l1 1l1 1l.5.5m-3.478 2.006H8.96\"/></g>"}, "folder-themes": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.4\"><path stroke=\"#f4dbd6\" d=\"M12 21a9 9 0 0 1 0-18c4.97 0 9 3.582 9 8c0 1.06-.474 2.078-1.318 2.828S17.693 15 16.5 15H14a2 2 0 0 0-1 3.75A1.3 1.3 0 0 1 12 21\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#ed8796\" d=\"M7.5 10.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#a6da95\" d=\"M11.5 7.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#8aadf4\" d=\"M15.5 10.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/></g>"}, "folder-themes-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.4\"><path stroke=\"#f4dbd6\" d=\"M12 21a9 9 0 0 1 0-18c4.97 0 9 3.582 9 8c0 1.06-.474 2.078-1.318 2.828S17.693 15 16.5 15H14a2 2 0 0 0-1 3.75A1.3 1.3 0 0 1 12 21\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#ed8796\" d=\"M7.5 10.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#a6da95\" d=\"M11.5 7.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/><path stroke=\"#8aadf4\" d=\"M15.5 10.5c0 1.333 2 1.333 2 0s-2-1.333-2 0\" transform=\"matrix(.41667 0 0 .4167 6.75 6.747)\"/></g>"}, "folder-turbo": {"body": "<g fill=\"none\"><path stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.8 0 1.5.7 1.5 1.5v.5m-7.5 7H2c-.8 0-1.5-.7-1.5-1.5V3.5c0-.6.4-1 1-1h5c.6 0 1 .4 1 1v1\"/><circle cx=\"11.5\" cy=\"11.5\" r=\"2\" stroke=\"#cad3f5\"/><path stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 7.6a4 4 0 0 1 3 3.7\"/><path stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.5 11.5a4 4 0 0 1-.7 2.3\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 15.4a4 4 0 0 1-1.4-.7m-1.3-1.6a4 4 0 0 1-.3-1.6m7.2 2.5a4 4 0 0 1-2.2 1.4\"/></g>"}, "folder-turbo-open": {"body": "<g fill=\"none\"><path stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.9 8l.7-2.7a1 1 0 0 1 1-.8h10.9a1 1 0 0 1 1 1.2L15 7m-9 6.5H2c-.8 0-1.5-.7-1.5-1.5V3.5c0-.6.4-1 1-1h5c.6 0 1 .4 1 1v1\"/><circle cx=\"11.5\" cy=\"11.5\" r=\"2\" stroke=\"#cad3f5\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.7 14a4 4 0 0 1-2.2 1.4\"/><path stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 7.6a4 4 0 0 1 3 3.7\"/><path stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.5 11.5a4 4 0 0 1-.7 2.3\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 15.4a4 4 0 0 1-1.4-.7m-1.3-1.6a4 4 0 0 1-.3-1.6\"/></g>"}, "folder-types": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m8.499 12.5l4.001-4h3v3l-4 4.003z\"/></g>"}, "folder-types-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8bd5ca\" d=\"m8.499 12.5l4.001-4h3v3l-4 4.003z\"/></g>"}, "folder-upload": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M9.5 15.5h4m-2-2v-5m-2 2l2-2l2 2\"/></g>"}, "folder-upload-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#91d7e3\" d=\"M9.5 15.5h4m-2-2v-5m-2 2l2-2l2 2\"/></g>"}, "folder-utils": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m15.5 14l-1.265-1.266a3 3 0 0 0-3.969-3.97L12 10.5L10.5 12l-1.736-1.733a3 3 0 0 0 3.97 3.968L14 15.5\"/></g>"}, "folder-utils-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#a6da95\" d=\"m15.5 14l-1.265-1.266a3 3 0 0 0-3.969-3.97L12 10.5L10.5 12l-1.736-1.733a3 3 0 0 0 3.97 3.968L14 15.5\"/></g>"}, "folder-vercel": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"m12 9l3.5 6.5h-7z\"/></g>"}, "folder-vercel-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8087a2\" d=\"m12 9l3.5 6.5h-7z\"/></g>"}, "folder-video": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m15.5 12l-5-3.5v7zm-7-3.5v7\"/></g>"}, "folder-video-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m15.5 12l-5-3.5v7zm-7-3.5v7\"/></g>"}, "folder-views": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M14.692 8.5H9.308a.31.31 0 0 0-.307.336l.47 5.241c.01.128.1.235.223.27l2.303 1.153l2.308-1.153a.31.31 0 0 0 .225-.27L15 8.836a.31.31 0 0 0-.307-.336\"/></g>"}, "folder-views-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M14.692 8.5H9.308a.31.31 0 0 0-.307.336l.47 5.241c.01.128.1.235.223.27l2.303 1.153l2.308-1.153a.31.31 0 0 0 .225-.27L15 8.836a.31.31 0 0 0-.307-.336\"/></g>"}, "folder-vscode": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m8 10l5.555 5.432s.179.143.342.015l1.434-.739c.195-.144.168-.321.168-.321V9.55c0-.212-.202-.286-.202-.286l-1.322-.687c-.288-.192-.477.035-.477.035l-.421.406L8 14\"/></g>"}, "folder-vscode-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#8aadf4\" d=\"m8 10l5.555 5.432s.179.143.342.015l1.434-.739c.195-.144.168-.321.168-.321V9.55c0-.212-.202-.286-.202-.286l-1.322-.687c-.288-.192-.477.035-.477.035l-.421.406L8 14\"/></g>"}, "folder-workflows": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M9.5 8.5h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1m4 4h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1m-4-1v3h3\"/></g>"}, "folder-workflows-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path stroke=\"#f5a97f\" d=\"M9.5 8.5h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1m4 4h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1m-4-1v3h3\"/></g>"}, "folder-wxt": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 10.278c0-.576.468-1.044 1.044-1.044h1.045v-.19a1.044 1.044 0 0 1 2.088 0v.19h1.045c.576 0 1.044.468 1.044 1.044v1.045h.19a1.044 1.044 0 1 1 0 2.088h-.19v1.045c0 .576-.468 1.044-1.044 1.044h-1.045v-.19a1.044 1.044 0 1 0-2.088 0v.19H8v-2.089h.19a1.044 1.044 0 0 0 0-2.088H8Z\"/>"}, "folder-wxt-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 10.278c0-.576.468-1.044 1.044-1.044h1.045v-.19a1.044 1.044 0 0 1 2.088 0v.19h1.045c.576 0 1.044.468 1.044 1.044v1.045h.19a1.044 1.044 0 1 1 0 2.088h-.19v1.045c0 .576-.468 1.044-1.044 1.044h-1.045v-.19a1.044 1.044 0 1 0-2.088 0v.19H8v-2.089h.19a1.044 1.044 0 0 0 0-2.088H8Z\"/>"}, "folder-xcode": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2c-.83 0-1.5-.67-1.5-1.5V3.5c0-.55.45-1 1-1h5c.55 0 1 .45 1 1v1\"/><path stroke=\"#8aadf4\" stroke-width=\".563\" d=\"M14.59 11.761v2.09c0 .868-.699 1.566-1.567 1.566h-1.245m-2.532 0h-.401a1.563 1.563 0 0 1-1.567-1.567V9.671c0-.868.699-1.567 1.567-1.567h1.527m-1.407 5.908l2.532-4.501m-1.125 0l.83 1.477m1.543 2.743l.158.281m-4.218-1.407h1.675\"/><g stroke-width=\"1.884\"><path stroke=\"#cad3f5\" d=\"M14 12L.34 25.7c-1.443 1.447-3.267-.727-1.996-2.003l13.66-13.7\" transform=\"matrix(.28528 -.08959 .08932 .28431 8.46 8.178)\"/><path stroke=\"#8087a2\" d=\"m21.5 11.5l-1.914-1.914A2 2 0 0 1 19 8.172V7l-2.26-2.26a6 6 0 0 0-4.202-1.756L9 2.96l.92.82A6.18 6.18 0 0 1 12 8.4V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5m-.408.569l3.854-4.031\" transform=\"matrix(.28528 -.08959 .08932 .28431 8.46 8.178)\"/></g></g>"}, "folder-xcode-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" stroke-width=\".583\" d=\"M14.54 11.61v2.164c0 .9-.724 1.623-1.623 1.623h-1.29m-2.622 0h-.416a1.62 1.62 0 0 1-1.623-1.623V9.445c0-.9.724-1.623 1.623-1.623h1.582m-1.457 6.119l2.622-4.662m-1.165 0l.86 1.53m1.598 2.841l.164.291m-4.37-1.457h1.735\" transform=\"matrix(.9666 0 0 .9654 .545 .551)\"/><g stroke-width=\"1.884\"><path stroke=\"#cad3f5\" d=\"M14 12L.34 25.7c-1.443 1.447-3.267-.727-1.996-2.003l13.66-13.7\" transform=\"matrix(.28563 -.08959 .08943 .28431 8.462 8.176)\"/><path stroke=\"#8087a2\" d=\"m21.5 11.5l-1.914-1.914A2 2 0 0 1 19 8.172V7l-2.26-2.26a6 6 0 0 0-4.202-1.756L9 2.96l.92.82A6.18 6.18 0 0 1 12 8.4V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5m-.408.569l3.854-4.031\" transform=\"matrix(.28563 -.08959 .08943 .28431 8.462 8.176)\"/></g><path stroke=\"#cad3f5\" d=\"m1.873 8l.702-2.74a1.002 1 0 0 1 .961-.76h10.96a1.002 1 0 0 1 .973 1.24l-.22.875M6.009 13.5H2.001A1.503 1.5 0 0 1 .498 12V3.5a1.002 1 0 0 1 1.002-1h5.01a1.002 1 0 0 1 1.002 1v1\" transform=\"scale(1.0004 1.0002)\"/></g>"}, "folder-yarn": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5H12c.83 0 1.5.67 1.5 1.5v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"10.927\"><path d=\"M11.9 65.033s-1.241-16.91 11.549-24.936c0 0-8.286-12.228 0-19.036s12.645-9.145 18.736-8.634c0 0 7.247-20.251 14.643 0c0 0 9.061-7.776 7.844 17.247c-.323 6.646-4.717 16.289-7.844 20.326c0 0 8.881 6.323 8.881 25.924c0 0 14.526-7.698 18.663-8.162c4.136-.463 7.724-.011 8.572 2.799s1.245 4.744-1.062 6.558c-2.308 1.813-10.589 4.258-19.484 10.194c-8.894 5.936-11.77 4.105-14.208 5.576c-2.438 1.472-16.058 7.342-33.033.927c0 0-15.323 4.247-14.195-6.503c0 0-10.684-12.422.938-22.28\" transform=\"matrix(.09189 0 0 .09115 6.896 6.683)\"/><path d=\"M27.469 94.285c-1.525-1.407.321-8.703.321-8.703s-1.252 4.723-3.116 7.525\" transform=\"matrix(.09189 0 0 .09115 6.896 6.683)\"/></g>"}, "folder-yarn-open": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"m1.87 8l.7-2.74a1 1 0 0 1 .96-.76h10.94a1 1 0 0 1 .97 1.24l-.219.875M6 13.5H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><g stroke=\"#c6a0f6\" stroke-width=\"10.927\"><path d=\"M11.9 65.033s-1.241-16.91 11.549-24.936c0 0-8.286-12.228 0-19.036s12.645-9.145 18.736-8.634c0 0 7.247-20.251 14.643 0c0 0 9.061-7.776 7.844 17.247c-.323 6.646-4.717 16.289-7.844 20.326c0 0 8.881 6.323 8.881 25.924c0 0 14.526-7.698 18.663-8.162c4.136-.463 7.724-.011 8.572 2.799s1.245 4.744-1.062 6.558c-2.308 1.813-10.589 4.258-19.484 10.194c-8.894 5.936-11.77 4.105-14.208 5.576c-2.438 1.472-16.058 7.342-33.033.927c0 0-15.323 4.247-14.195-6.503c0 0-10.684-12.422.938-22.28\" transform=\"matrix(.09189 0 0 .09115 6.896 6.683)\"/><path d=\"M27.469 94.285c-1.525-1.407.321-8.703.321-8.703s-1.252 4.723-3.116 7.525\" transform=\"matrix(.09189 0 0 .09115 6.896 6.683)\"/></g></g>"}, "font": {"body": "<path fill=\"none\" stroke=\"#f0c6c6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m7 5l4 8.5h2.5L8 2.5l-4.5 11m-1 0h2m5 0h5m-9-4H9\"/>"}, "forgejo": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"M12.58 1.877a1.345 1.377 0 0 1-1.345 1.377A1.345 1.377 0 0 1 9.89 1.877A1.345 1.377 0 0 1 11.235.5a1.345 1.377 0 0 1 1.345 1.377m-7.619 10.87V5.709c0-2.114 1.672-3.827 3.734-3.827H9.89\"/><path stroke=\"#ed8796\" d=\"M6.305 14.13a1.345 1.377 0 0 1-1.345 1.377a1.345 1.377 0 0 1-1.345-1.377a1.345 1.377 0 0 1 1.345-1.377a1.345 1.377 0 0 1 1.345 1.377m6.273-7.038a1.345 1.377 0 0 1-1.345 1.377a1.345 1.377 0 0 1-1.345-1.377a1.345 1.377 0 0 1 1.345-1.377a1.345 1.377 0 0 1 1.345 1.377m-7.619 5.664v-1.837c0-2.114 1.672-3.827 3.734-3.827h1.195\"/></g>"}, "fortran": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.5 14.5v-1l-1-1v-3h2l1 2h1v-6h-1l-1 2h-2v-4h5l1 3h1v-5h-11v1l1 1v9l-1 1.25v.75z\"/>"}, "fsharp": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1 8l5.5-6v2.93L3.57 8l2.93 2.8v2.93zm14 0L9.5 2v2.93L12.25 8L9.5 10.79v2.93z\"/>"}, "fvm": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.024 12.85L9 10.505h6.5v2.35zM5.4 8.005L.5 3.155l3.433.005L8 7.155c.47.476.47 1.24 0 1.7l-4 4H.5z\"/>"}, "gatsby": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11.32 4.79a4.64 4.64 0 0 0-7.75 1.86l5.82 5.82a4.65 4.65 0 0 0 3.23-3.97H9.39m-6.03.47l3.71 3.71\"/><path d=\"M8 14.5a6.5 6.5 0 1 1 0-13a6.5 6.5 0 0 1 0 13\"/></g>"}, "gcp": {"body": "<g fill=\"none\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M5.576 5.627a3.266 3.266 0 0 1 4.451-.339h-.009h.458\"/><path stroke=\"#ed8796\" d=\"m11.78 3.984l.064-.553a5.87 5.87 0 0 0-3.879-1.467A5.876 5.876 0 0 0 2.309 6.29\"/><path stroke=\"#8aadf4\" d=\"M13.637 6.29a5.9 5.9 0 0 0-1.771-2.855l-1.83 1.83a3.26 3.26 0 0 1 1.195 2.581v.325c.893 0 1.628.735 1.628 1.628c0 .894-.735 1.629-1.628 1.629H7.973v2.608h3.291c2.324 0 4.236-1.913 4.236-4.237a4.24 4.24 0 0 0-1.863-3.509Z\"/><path stroke=\"#a6da95\" d=\"m2.268 12.709l-.114.457a4.2 4.2 0 0 0 2.557.851h3.258V11.41H4.711c-.232 0-.461-.05-.672-.147\"/><path stroke=\"#eed49f\" d=\"m3.221 12.085l.822-.822a1.63 1.63 0 0 1-.958-1.484c0-.893.735-1.629 1.629-1.629a1.63 1.63 0 0 1 1.484.958l1.89-1.889a4.24 4.24 0 0 0-3.377-1.661C2.397 5.572.5 7.48.5 9.795a4.24 4.24 0 0 0 1.654 3.358l1.067-1.068\"/></g>"}, "git": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M8.5 10.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m0-6a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m3 3a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-4-2v4m-1-6l-1-1m4 4l-1-1\"/><path stroke=\"#f5a97f\" d=\"m9.06 1.06l5.88 5.88a1.5 1.5 0 0 1 0 2.12l-5.88 5.88a1.5 1.5 0 0 1-2.12 0L1.06 9.06a1.5 1.5 0 0 1 0-2.12l5.88-5.88a1.5 1.5 0 0 1 2.12 0\"/></g>"}, "git-cliff": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.143 10.585s3.16-2.431 3.58-2.851s.906-.884 1.702-.133C8.22 8.353 8.86 8.95 8.86 8.95s.707-.154 1.348.265c.64.42 2.983 1.812 2.983 1.812\"/><path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.95 8.922s-.973.248-1.26.425s-.862.243-1.083.221m4.29.09s2.14 1.59-3.804 4.683\"/><circle cx=\"8\" cy=\"8\" r=\"6.5\" fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>"}, "gitlab": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 14.49L14.5 10L12 2l-1.5 4.5h-5L4 2l-2.5 8z\"/>"}, "gitpod": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.7 3.27L4.68 5.79a.38.38 0 0 0-.18.33v3.96c0 .14.07.26.18.33l3.14 2c.11.06.25.06.36 0l3.14-2a.4.4 0 0 0 .18-.33V7.5L8.7 9.4c-.33.19-.72.23-1.08.13s-.67-.36-.85-.7a1.52 1.52 0 0 1 .54-2.02l4.27-2.54c.61-.35 1.35-.35 1.96.02c.6.37.96 1.04.96 1.77v4.32c0 1.01-.51 1.95-1.35 2.45l-3.82 2.3c-.82.5-1.84.5-2.66 0l-3.82-2.3a2.85 2.85 0 0 1-1.35-2.45V5.82c0-1.01.51-1.95 1.35-2.45L7.3.7c.33-.2.71-.25 1.07-.15s.67.36.85.7c.39.7.16 1.61-.52 2.02\"/>"}, "gleam": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-width=\".84\" d=\"M7.986 1.42a.96.96 0 0 0-.851.51L5.704 4.577a.98.98 0 0 1-.658.498l-2.856.601c-.733.155-1.032 1.115-.526 1.69l1.972 2.238c.192.217.284.512.251.806l-.334 3.02c-.086.775.696 1.37 1.377 1.044l2.65-1.265a.94.94 0 0 1 .812 0l2.65 1.265c.68.325 1.462-.269 1.376-1.044l-.334-3.02a1.04 1.04 0 0 1 .251-.806l1.972-2.239c.506-.574.207-1.534-.526-1.689l-2.856-.6a.98.98 0 0 1-.658-.5L8.837 1.93a.96.96 0 0 0-.851-.51\"/><path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.685 9.262a.75.754 0 0 1-.705.496a.75.754 0 0 1-.704-.496M10.5 8\"/>"}, "gleam-config": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/><path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.267.5a.84.84 0 0 0-.747.448L4.265 3.272a.86.86 0 0 1-.577.438l-2.507.527C.538 4.373.276 5.216.72 5.72l1.73 1.964a.92.92 0 0 1 .22.708l-.293 2.65c-.075.68.611 1.201 1.208.916h0l2.325-1.11h0a.82.82 0 0 1 .714 0h0m3.46-3.164l1.73-1.964h0c.444-.503.181-1.346-.462-1.482L8.846 3.71a.86.86 0 0 1-.577-.438L7.014.948A.84.84 0 0 0 6.267.5\"/><path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.87 7.371a.657.66 0 0 1-.617.435a.657.66 0 0 1-.617-.435M8.46 6.266\"/>"}, "go": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m15.48 8.06l-4.85.48m4.85-.48a4.98 4.98 0 0 1-4.54 5.42a5 5 0 1 1 2.95-8.66l-1.7 1.84a2.5 2.5 0 0 0-4.18 2.06c.05.57.3 1.1.69 1.51c.25.27 1 .83 1.78.82c.8-.02 1.58-.25 2.07-.81c0 0 .8-.96.68-1.88M2.5 8.5l-2 .01m1.5 2h1.5m-2-3.99l2-.02\"/>"}, "go-mod": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m2.5 8.51l-2 .01m1.5 2h1.5m-2-4h1m6.73-2.19a1.5 1.5 0 1 0-2.16.58m2.16-.58l2.78-.74l.75 2.78m.57 2.16l.75 2.79l-7.73 2.07l-2.07-7.73l2.78-.75m6.28 3.63a1.5 1.5 0 1 0-.58-2.17\"/>"}, "go-template": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m2.5 8.51l-2 .01m1.5 2h1.5m-2-4h1\"/><path fill=\"none\" stroke=\"#7dc4e4\" stroke-dasharray=\"1 1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m14.094 5.17l1.347 5.03a1.735 1.735 0 0 1-1.227 2.126L8.349 13.9a1.735 1.735 0 0 1-2.125-1.228l-2.02-7.544A1.74 1.74 0 0 1 5.43 3l3.36-.9m-.008.002l5.312 3.068l-3.352.898a.867.868 0 0 1-1.062-.613Z\"/>"}, "godot": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.5 11l2.5.5l.25 1h2l.5-1h2.5l.5 1h2l.25-1l2.5-.5m-13 1.25c0 2.25 2.74 3.25 6.5 3.25c3.75 0 6.5-1 6.5-3.25v-5L15.75 6L14.5 4L13 5.25L11.5 4l.5-1.5l-2.5-1L8.75 3h-1.5L6.5 1.5L4 2.5L4.5 4L3 5.25L1.5 4L.25 6L1.5 7.25zM8 7.5v2m-3.5 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2m7 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "godot-assets": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.5 11l2.5.5l.25 1h2l.5-1h2.5l.5 1h2l.25-1l2.5-.5m-13 1.25c0 2.25 2.74 3.25 6.5 3.25c3.75 0 6.5-1 6.5-3.25v-5L15.75 6L14.5 4L13 5.25L11.5 4l.5-1.5l-2.5-1L8.75 3h-1.5L6.5 1.5L4 2.5L4.5 4L3 5.25L1.5 4L.25 6L1.5 7.25zM8 7.5v2m-3.5 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2m7 0a1 1 0 1 0 0-2a1 1 0 0 0 0 2\"/>"}, "gradle": {"body": "<g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7.5 7.5l-1.97.78c-.2.15-.74.33-1.2-.2a7.3 7.3 0 0 1-1-1.63\"/><path d=\"M11.43 2.98c.54-.48 4.07-1.39 4.07 2.1c0 2.34-1.6 3.53-2.76 4.5c-.75.61-1.35 1.28-1.35 3.92H9.88c0-1.29-2.66-2.32-3.17 0H5.38c0-1.29-2.65-2.32-3.16 0H.7C.29 11.92.22 8.3 3.33 6.45c-.15-.25-.27-1.02.43-1.3c.87-.34 3.77-1.34 6.51.12s5.01 1.12 5.14.74\"/></g>"}, "graphql": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9 1.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-5.5 3a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m0 7a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m11 0a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-5.5 3a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m5.5-10a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-12 1v5m11-5v5m-10 1h9m-6 2.5l-3-1.5m6 1.5l3-1.5m-9-2l4-8m5 8l-4-8m-5 1l3-1.5m3 0l3 1.5\"/>"}, "groovy": {"body": "<g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11.68 5.38c.4.19.54.68 1.53 3.25c1 2.57-.92 4.07-.92 4.07s-6.73 2.47-6.73 1.63c-.18-.92-1.92-2.08-1.92-2.08s-.52-.63.06-.75c5.89-1.27 6.96-.61 7.3-2\"/><path d=\"M7.38 10.63C2.62 10.88 2.48 8.08 2.5 8C3.6 4.6 9.24.91 10.8 1.58C14.07 3.04 9.2 8.96 7 8.5c-4.02-.83 1.5-4 1.5-4\"/></g>"}, "gulp": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m13 4l-1 7.5l-1 1l-.5 3h-5l-.5-3l-1-1l-.93-7L3 4m5 1.5c2.76 0 5-.67 5-1.5s-2.24-1.5-5-1.5S3 3.17 3 4s2.24 1.5 5 1.5M4 11c1.78.33 3.11.5 4 .5s2.22-.17 4-.5M9.5 4l1-2.5l2-1\"/>"}, "haml": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M7.6 9.5s1.75.97 2.55 1.13c.8.17 1.5.8 2.49-.26c1.07-1.15 1.13-1.02.26-1.68c-.88-.66-2.41-1.94-2.41-2.69\"/><path stroke=\"#eed49f\" d=\"M6.87 5.56a.83.83 0 0 0 1.23.27L12 2.5l1 1.04L9.58 6.8a6.47 6.47 0 0 0-2.36 3.56l-1.2 4.13l-1.49-.5l1-3.98a6.17 6.17 0 0 0-.47-4.24L3.5 2.04l1.37-.54Z\"/><path stroke=\"#cad3f5\" d=\"m12 4.5l-1-1.16ZM5.41 2.62l-1.4.7Z\"/></g>"}, "handlebars": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 6.15a1.73 1.73 0 0 0-2.48-.22c-.97.82-1.78 2.5-3.31 2.5a.99.99 0 0 1-.83-.66a.97.97 0 0 1 .24-1.02C.45 6.75.1 8.67 1.03 9.56a4.76 4.76 0 0 0 4.7.56C6.63 9.76 7.4 9.2 8 8.45zm0 0a1.73 1.73 0 0 1 2.48-.22c.96.82 1.78 2.5 3.3 2.5a1 1 0 0 0 .84-.66a.97.97 0 0 0-.24-1.02c1.17 0 1.52 1.92.58 2.81a4.76 4.76 0 0 1-4.7.56A5.34 5.34 0 0 1 8 8.45z\"/>"}, "hardhat": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 12.741a.75.73 0 0 0 .75.73h13.5a.75.73 0 0 0 .75-.73v-1.459a.75.73 0 0 0-.75-.73H1.25a.75.73 0 0 0-.75.73Zm6-5.835V3.259a.75.73 0 0 1 .75-.73h1.5a.75.73 0 0 1 .75.73v3.647M2 10.553V8.365a4.5 4.376 0 0 1 4.5-4.377m3 0A4.5 4.376 0 0 1 14 8.365v2.188\"/>"}, "haskell": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 4.5h3m-1.5 3h1.5m-10 6l2.5-5l-2.5-5H8l5.6 10h-2.53l-1.52-2.92L8 13.5zm-5 0l2.5-5l-2.5-5H3l2.5 5l-2.5 5z\"/>"}, "haxe": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.5 1.5H5l3 1.75l3-1.75h3.5V5l-1.75 3l1.75 3v3.5H11l-3-1.75l-3 1.75H1.5V11l1.75-3L1.5 5z\"/><path d=\"m12.65 8.35l-4.3 4.3a.5.5 0 0 1-.7 0l-4.3-4.3a.5.5 0 0 1 0-.7l4.3-4.3c.2-.2.5-.2.7 0l4.3 4.3c.2.2.2.5 0 .7\"/></g>"}, "helm": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.5 11.5C4.48 12.62 6.52 13 8 13s3.52-.38 4.5-1.5M8 13v2.5m-4-3L3 14m9-1.5l1 1.5M3.5 4.5C4.48 3.38 6.52 3 8 3s3.52.38 4.5 1.5M8 3V.5m-4 3L3 2m9 1.5L13 2m1.5 7.5v-3L13 8l-1.5-1.5v3m-3-3v3H10m-3-3H5.5v3H7M5.5 8h1m-5-1.5v3m0-1.5h2m0-1.5v3\"/>"}, "heroku": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 1.5h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2\"/><path d=\"M5.53 3.58L5.5 8.5s2-2 5-1v5m-5-2v2m5-9c0 1 0 1.49-1 2\"/></g>"}, "histoire": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m5 4.5l2.5 3l3-2l1.5 6l-2.5-3l-3 2z\"/><path d=\"m2.5 1.5l11-1l1 14l-11 1z\"/></g>"}, "html": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"M1.5 1.5h13L13 13l-5 2l-5-2z\"/><path stroke=\"#cad3f5\" d=\"M11 4.5H5l.25 3h5.5l-.25 3l-2.5 1l-2.5-1l-.08-1\"/></g>"}, "http": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8\"/><path d=\"M8 1.5c1.67 2 2.5 4.17 2.5 6.5s-.83 4.5-2.5 6.5m0-13A9.96 9.96 0 0 0 5.5 8c0 2.33.83 4.5 2.5 6.5m-5.5-4h11m-11-5h11\"/></g>"}, "huff": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.64 10.515c-.063-.232-.154-.389-.351-.389H6.65c-.197 0-.273.157-.336.389l-.836 1.317h5.044zm-.017-.389c-.075-.487-.618-1.047-1.002-1.42c-.921-.893-.836-1.658-.685-1.865l.92.652c.218.155.492.119.68-.09v0c.206-.23.253-.61.112-.906l-.693-1.458l.266-.747c-.68-.649-2.01-.591-2.767.048c-.846.72-.726 1.368-.107 5.785\"/><path fill=\"none\" stroke=\"#cad3f5\" d=\"M13.565 10.46q.081-.183.15-.372l1.513-.85a.42.42 0 0 0 .213-.318a7.5 7.5 0 0 0-.017-1.975a.42.42 0 0 0-.218-.317l-1.532-.825a6 6 0 0 0-.156-.368l.469-1.676a.42.42 0 0 0-.077-.377a7.5 7.5 0 0 0-1.408-1.381a.42.42 0 0 0-.377-.065l-1.666.5a6 6 0 0 0-.373-.151L9.24.77a.42.42 0 0 0-.318-.213a7.5 7.5 0 0 0-1.975.018a.42.42 0 0 0-.317.217l-.822 1.533q-.188.072-.368.157l-1.68-.466a.42.42 0 0 0-.376.077c-.52.406-.985.88-1.382 1.408a.42.42 0 0 0-.065.372l.5 1.666a6 6 0 0 0-.15.373l-1.517.85a.42.42 0 0 0-.213.318a7.5 7.5 0 0 0 .019 1.972a.42.42 0 0 0 .217.316l1.532.825q.072.188.157.368l-.465 1.676a.42.42 0 0 0 .077.377c.406.52.88.985 1.408 1.381c.108.08.247.106.376.067l1.659-.499q.183.081.372.15l.853 1.518a.42.42 0 0 0 .317.212a7.5 7.5 0 0 0 1.972-.018a.42.42 0 0 0 .317-.218l.825-1.532q.187-.071.368-.156l1.676.469c.**************.377-.077a7.5 7.5 0 0 0 1.381-1.408a.42.42 0 0 0 .067-.376z\"/>"}, "hugo": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5bde6\" d=\"M7.991 2.163L2.823 5.317l.031 6.062l5.157 3.009l5.257-3.238l-.031-6.155Z\"/><path stroke=\"#cad3f5\" d=\"m6.007 5.095l.046 6.269m.051-3.372l3.889-.003m.01-2.902l-.028 6.247\"/></g>"}, "humans": {"body": "<g fill=\"none\"><g stroke=\"#f5a97f\" stroke-linejoin=\"round\" clip-path=\"url(#catppuccinHumans0)\"><path d=\"M2.28 1.87C1.1 2.2.32 3.45.53 4.8l1.38 8.6c.14.9.7 1.62 1.44 1.93V3.68c0-.58-.21-.7-.64-.7h-.43v-1.1Zm3.19-.47c.***********.24 1v4.53l.31-.4c.62-.83 1.27-1.69 3.02-1.69c2.36 0 3.32 1.28 3.32 2.9v4.83c-.01.43-.02.87.54.87h2.56c.04-.27.05-.55.02-.84l-1.24-9.9C14.07 1.33 12.9.35 11.62.53l-6.15.88Z\"/><path d=\"M10.89 15.5c-.54-.14-.78-.68-.78-1.48V8.44c0-.81-.54-1.27-1.72-1.27s-2.68.7-2.68 2.32v6.01z\"/></g><defs><clipPath id=\"catppuccinHumans0\"><path fill=\"#fff\" d=\"M0 0h16v16H0z\"/></clipPath></defs></g>"}, "husky": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m9.63 3.54l.48-.84c.94-1.68-1.51-3.13-2.46-1.46l-.47.84c-.94 1.68 1.5 3.14 2.45 1.46m4.09 2.42l.47-.84c.95-1.68-1.5-3.13-2.45-1.45l-.47.84c-.95 1.67 1.5 3.13 2.45 1.45m.33 5.23l.24-.42c.94-1.68-1.51-3.13-2.46-1.45l-.23.42c-.95 1.67 1.5 3.13 2.45 1.45M5.3 5.44C6.22 3.76 3.77 2.31 2.83 4l-.24.42C1.65 6.1 4.1 7.55 5.04 5.87l.24-.42ZM2.57 9.02l4.06-1.56c1.25-.48 2.6.33 2.83 1.68l.71 4.38c.23 1.41-1.25 2.46-2.45 1.75a1.2 1.2 0 0 1-.4-.38l-.2-.33a5 5 0 0 0-3.9-2.3l-.38-.02a1.16 1.16 0 0 1-.51-.16a1.71 1.71 0 0 1 .24-3.06\"/>"}, "image": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"M11.5 6A1.5 1.5 0 0 1 10 7.5A1.5 1.5 0 0 1 8.5 6A1.5 1.5 0 0 1 10 4.5A1.5 1.5 0 0 1 11.5 6\"/><path stroke=\"#a6da95\" d=\"M7.5 13.5L11 10c.5-.5 1.5-.5 2 0l1.5 1.5\"/><path stroke=\"#a6da95\" d=\"m1.5 9.5l2-2C4 7 5 7 5.5 7.5l4 4\"/><path stroke=\"#7dc4e4\" d=\"M3 2.5h10c.83 0 1.5.67 1.5 1.5v8c0 .83-.67 1.5-1.5 1.5H3A1.5 1.5 0 0 1 1.5 12V4c0-.83.67-1.5 1.5-1.5\"/></g>"}, "ionic": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.6 3.4A6.5 6.5 0 1 0 14 5.5\"/><path d=\"M10.5 8A2.5 2.5 0 0 1 8 10.5A2.5 2.5 0 0 1 5.5 8A2.5 2.5 0 0 1 8 5.5A2.5 2.5 0 0 1 10.5 8M13 3.5a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "java": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.73 8.41c.57 3 1.59 5.83 2.77 7.09c-6.63-3.45-9.76-1.75-10.5 0c-.66-3.4-.54-5.74.09-7.78\"/><path stroke=\"#ed8796\" d=\"M8.5 7c.63.34 1.82 1.07 2.24 1.41c-.54-2.9-.64-5.96-.74-7.91c-2.13.58-5.73 1.98-6.9 7.22c.52-.69 1.72-1.05 2.4-1.22\"/><path stroke=\"#ed8796\" d=\"M5.5 7A1.5 1.5 0 0 0 7 8.5A1.5 1.5 0 0 0 8.5 7A1.5 1.5 0 0 0 7 5.5A1.5 1.5 0 0 0 5.5 7\"/></g>"}, "java-alt-1": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.73 8.41c.57 3 1.59 5.83 2.77 7.09c-6.63-3.45-9.76-1.75-10.5 0c-.66-3.4-.54-5.74.09-7.78\"/><path stroke=\"#a6da95\" d=\"M8.5 7c.63.34 1.82 1.07 2.24 1.41c-.54-2.9-.64-5.96-.74-7.91c-2.13.58-5.73 1.98-6.9 7.22c.52-.69 1.72-1.05 2.4-1.22\"/><path stroke=\"#a6da95\" d=\"M5.5 7A1.5 1.5 0 0 0 7 8.5A1.5 1.5 0 0 0 8.5 7A1.5 1.5 0 0 0 7 5.5A1.5 1.5 0 0 0 5.5 7\"/></g>", "hidden": true}, "java-alt-2": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.73 8.41c.57 3 1.59 5.83 2.77 7.09c-6.63-3.45-9.76-1.75-10.5 0c-.66-3.4-.54-5.74.09-7.78\"/><path stroke=\"#c6a0f6\" d=\"M8.5 7c.63.34 1.82 1.07 2.24 1.41c-.54-2.9-.64-5.96-.74-7.91c-2.13.58-5.73 1.98-6.9 7.22c.52-.69 1.72-1.05 2.4-1.22\"/><path stroke=\"#c6a0f6\" d=\"M5.5 7A1.5 1.5 0 0 0 7 8.5A1.5 1.5 0 0 0 8.5 7A1.5 1.5 0 0 0 7 5.5A1.5 1.5 0 0 0 5.5 7\"/></g>", "hidden": true}, "java-alt-3": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.73 8.41c.57 3 1.59 5.83 2.77 7.09c-6.63-3.45-9.76-1.75-10.5 0c-.66-3.4-.54-5.74.09-7.78\"/><path stroke=\"#eed49f\" d=\"M8.5 7c.63.34 1.82 1.07 2.24 1.41c-.54-2.9-.64-5.96-.74-7.91c-2.13.58-5.73 1.98-6.9 7.22c.52-.69 1.72-1.05 2.4-1.22\"/><path stroke=\"#eed49f\" d=\"M5.5 7A1.5 1.5 0 0 0 7 8.5A1.5 1.5 0 0 0 8.5 7A1.5 1.5 0 0 0 7 5.5A1.5 1.5 0 0 0 5.5 7\"/></g>", "hidden": true}, "java-annotation": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 8v1c0 .67.4 1.21.97 1.55c.58.34 1.53.34 2.1 0c.58-.34.94-.95.93-1.62V8a6.5 6.5 0 1 0-2.79 5.33M10.5 8a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "java-class": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.825 10.605V5.393a1.282 1.287 0 0 0-.647-1.116l-4.53-2.605a1.307 1.313 0 0 0-1.295 0L2.822 4.277c-.4.23-.647.657-.647 1.117v5.212c0 .46.246.886.647 1.116l4.53 2.605a1.307 1.313 0 0 0 1.295 0l4.531-2.605c.4-.231.647-.657.647-1.117M8 14.5V8m0 0l5.65-3.276m-11.3 0L8 8\"/>"}, "java-class-abstract": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.825 10.605V5.393a1.282 1.287 0 0 0-.647-1.116l-4.53-2.605a1.307 1.313 0 0 0-1.295 0L2.822 4.277c-.4.23-.647.657-.647 1.117v5.212c0 .46.246.886.647 1.116l4.53 2.605a1.307 1.313 0 0 0 1.295 0l4.531-2.605c.4-.231.647-.657.647-1.117M8 14.5V8m0 0l5.65-3.276m-11.3 0L8 8\"/>"}, "java-class-final": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.825 10.605V5.393a1.282 1.287 0 0 0-.647-1.116l-4.53-2.605a1.307 1.313 0 0 0-1.295 0L2.822 4.277c-.4.23-.647.657-.647 1.117v5.212c0 .46.246.886.647 1.116l4.53 2.605a1.307 1.313 0 0 0 1.295 0l4.531-2.605c.4-.231.647-.657.647-1.117M8 14.5V8m0 0l5.65-3.276m-11.3 0L8 8\"/>"}, "java-class-sealed": {"body": "<path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.825 10.605V5.393a1.282 1.287 0 0 0-.647-1.116l-4.53-2.605a1.307 1.313 0 0 0-1.295 0L2.822 4.277c-.4.23-.647.657-.647 1.117v5.212c0 .46.246.886.647 1.116l4.53 2.605a1.307 1.313 0 0 0 1.295 0l4.531-2.605c.4-.231.647-.657.647-1.117M8 14.5V8m0 0l5.65-3.276m-11.3 0L8 8\"/>"}, "java-enum": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.625 1.5H14.5v4.875H9.625ZM1.5 9.625h4.875V14.5H1.5Zm8.125 2.438a2.438 2.437 0 1 0 4.875 0a2.438 2.437 0 1 0-4.875 0M1.5 3.938a2.438 2.437 0 1 0 4.875 0a2.438 2.437 0 1 0-4.875 0\"/>"}, "java-exception": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4 2h8L9 7h3l-6 7V9H4z\"/>"}, "java-interface": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 9.604H8v3.21h3.25v-3.21h3.25V6.396H8v-3.21H4.75v3.21H1.5Z\"/><path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.75 9.604V6.396H8v3.208h3.25V6.396\"/>"}, "java-jar": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.73 8.41c.57 3 1.59 5.83 2.77 7.09c-6.63-3.45-9.76-1.75-10.5 0c-.66-3.4-.54-5.74.09-7.78\"/><path stroke=\"#8087a2\" d=\"M8.5 7c.63.34 1.82 1.07 2.24 1.41c-.54-2.9-.64-5.96-.74-7.91c-2.13.58-5.73 1.98-6.9 7.22c.52-.69 1.72-1.05 2.4-1.22\"/><path stroke=\"#8087a2\" d=\"M5.5 7A1.5 1.5 0 0 0 7 8.5A1.5 1.5 0 0 0 8.5 7A1.5 1.5 0 0 0 7 5.5A1.5 1.5 0 0 0 5.5 7\"/></g>"}, "java-record": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 14.5a6.5 6.5 0 1 0 0-13a6.5 6.5 0 0 0 0 13\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 10a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "javascript": {"body": "<g fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4.5 11a1.5 1.5 0 0 0 3 0V7.5m5 1.25c0-.69-.537-1.25-1.2-1.25h-.6c-.663 0-1.2.56-1.2 1.25S10.037 10 10.7 10h.6c.663 0 1.2.56 1.2 1.25s-.537 1.25-1.2 1.25h-.6c-.663 0-1.2-.56-1.2-1.25\"/><path d=\"M4 1.5h8c1.385 0 2.5 1.115 2.5 2.5v8c0 1.385-1.115 2.5-2.5 2.5H4A2.495 2.495 0 0 1 1.5 12V4c0-1.385 1.115-2.5 2.5-2.5\"/></g>"}, "javascript-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"M9.5 5c-.33-.33-.83-.5-1.5-.5c-1 0-1.5.5-1.5 1s.5 1 1.5 1s1.5.5 1.5 1s-.5 1-1.5 1c-.67 0-1.17-.17-1.5-.5m-2-3.5v3a1 1 0 1 1-2 0\"/><path stroke=\"#eed49f\" d=\"M6.5 11.5h-4a2 2 0 0 1-2-2v-7c0-1.1.9-2 2-2h7a2 2 0 0 1 2 2V7\"/><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/></g>"}, "javascript-map": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M.5 4.06c0 .77.24 1.52.7 2.13l2.24 3.96l.04.08h0c.***********.52.27s.36-.09.48-.23h0l.03-.03l.08-.15l2.2-3.88c.46-.61.71-1.37.71-2.15A3.63 3.63 0 0 0 3.88.5C1.95.5.5 2.1.5 4.06\"/><path stroke=\"#ed8796\" d=\"M5.5 4A1.5 1.5 0 0 1 4 5.5A1.5 1.5 0 0 1 2.5 4A1.5 1.5 0 0 1 4 2.5A1.5 1.5 0 0 1 5.5 4\"/><path stroke=\"#eed49f\" d=\"M10 4.5h3.5a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2V13\"/><path stroke=\"#eed49f\" d=\"M13.5 9c-.33-.33-.83-.5-1.5-.5c-1 0-1.5.5-1.5 1s.5 1 1.5 1s1.5.5 1.5 1s-.5 1-1.5 1c-.67 0-1.17-.17-1.5-.5m-2-3.5v3a1 1 0 1 1-2 0\"/></g>"}, "javascript-react": {"body": "<g fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 10.8c4.14 0 7.5-1.25 7.5-2.8S12.14 5.2 8 5.2S.5 6.45.5 8s3.36 2.8 7.5 2.8\"/><path d=\"M5.52 9.4c2.07 3.5 4.86 5.72 6.23 4.95c1.37-.78.8-4.24-1.27-7.75C8.41 3.1 5.62.88 4.25 1.65c-1.37.78-.8 4.24 1.27 7.75\"/><path d=\"M5.52 6.6c-2.07 3.5-2.64 6.97-1.27 7.75c1.37.77 4.16-1.45 6.23-4.95s2.64-6.97 1.27-7.75C10.38.88 7.59 3.1 5.52 6.6\"/><path d=\"M8.5 8a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "javascript-test": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 7.5H4.98M9.5.5l6 6m-4.78-4.75l-8.49 8.48a2.5 2.5 0 1 0 3.54 3.54l8.48-8.49M15.5 12c-.33-.33-.83-.5-1.5-.5c-1 0-1.5.5-1.5 1s.5 1 1.5 1s1.5.5 1.5 1s-.5 1-1.5 1c-.67 0-1.17-.17-1.5-.5m-2-3.5v3a1 1 0 1 1-2 0\"/>"}, "jest": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M7.5 6L5.56 1.5h9.94l-2 4.5\"/><path d=\"m12.5 6l-2-2.5l-2 2.5m1 1.5h2m2 1.5c1 1-.5 2.75-3 4s-4.5 1.5-6 1.5c-2 0-3-1-3-2.9q0-1.35 1.5-2.1\"/><path d=\"M7.5 9c0 1.5-2 2.5-3 2.5S3 11.25 3 9.5\"/><path d=\"M4.5 8.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m5-1A1.5 1.5 0 0 1 8 9a1.5 1.5 0 0 1-1.5-1.5A1.5 1.5 0 0 1 8 6a1.5 1.5 0 0 1 1.5 1.5m5 0A1.5 1.5 0 0 1 13 9a1.5 1.5 0 0 1-1.5-1.5A1.5 1.5 0 0 1 13 6a1.5 1.5 0 0 1 1.5 1.5\"/></g>"}, "jinja": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 1.5c3.78 1.03 8.02 1.54 13 0L13 5c-3.5.75-6.5.75-10 0zm0 6.09C6 8.75 10 8.75 14.5 7.5m-9-1.52v8.52m-3 0h4m3 0h4m-3-8.52v8.52M3.5 8v6.5m9-6.5v6.5\"/>"}, "json": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 2.5H4c-.75 0-1.5.75-1.5 1.5v2c0 1.1-1 2-1.83 2c.83 0 1.83.9 1.83 2v2c0 .75.75 1.5 1.5 1.5h.5m7-11h.5c.75 0 1.5.75 1.5 1.5v2c0 1.1 1 2 1.83 2c-.83 0-1.83.9-1.83 2v2c0 .74-.75 1.5-1.5 1.5h-.5m-6.5-3a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m3 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m3 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1\"/>"}, "json-schema": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4 15c-2.4-.6-1.6-2.2-1.6-3.8S2.6 8 1 8c1.6 0 1.4-1.6 1.4-3.2S1.6 1.6 4 1m8 0c2.4.6 1.6 2.2 1.6 3.8S13.4 8 15 8c-1.6 0-1.4 1.6-1.4 3.2s.8 3.2-1.6 3.8M4 5.5C5 5.6 5.8 7 5.8 7s1-3 3.2-4m-.4 6c0 .7.5 1.5.9 2m0 0a11 11 0 0 0 1.5 1.8m-1.5-1.7c.8-.8 1.6-1.5 2.5-2.1m-2.5 2L7 14\"/>"}, "juce": {"body": "<g fill=\"none\"><path stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9 3c-.5-.5-1.5-.5-2 0s.5 1.5 1 2.5c.5-1 1.5-2 1-2.5\"/><path stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9 13c-.5.5-1.5.5-2 0s.5-1.5 1-2.5c.5 1 1.5 2 1 2.5\"/><path stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 7c-.5.5-.5 1.5 0 2s1.5-.5 2.5-1c-1-.5-2-1.5-2.5-1\"/><path stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13 7c.5.5.5 1.5 0 2s-1.5-.5-2.5-1c1-.5 2-1.5 2.5-1\"/><path stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.2 3.8c-.7 0-1.4.7-1.4 1.4s1.4.7 2.4 1c-.3-1-.3-2.4-1-2.4\"/><path stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.2 10.8c0 .7-.7 1.4-1.4 1.4s-.7-1.4-1-2.4c1 .3 2.4.3 2.4 1\"/><path stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.2 5.2c0-.7-.7-1.4-1.4-1.4s-.7 1.4-1 2.4c1-.3 2.4-.3 2.4-1\"/><path stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.2 12.2c-.7 0-1.4-.7-1.4-1.4s1.4-.7 2.4-1c-.3 1-.3 2.4-1 2.4\"/><circle cx=\"8\" cy=\"8\" r=\"7\" stroke=\"#a6da95\"/><path stroke=\"#cad3f5\" stroke-linejoin=\"round\" d=\"M7.9 7.9h.2v.2h-.2z\"/></g>"}, "julia": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"M10.5 5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/><path stroke=\"#ed8796\" d=\"M6.5 11a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/><path stroke=\"#c6a0f6\" d=\"M14.5 11a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/></g>"}, "jupyter": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M2.5 2a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5m12 0a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5m-12 12a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#f5a97f\" d=\"M1.5 9.5c1.67 2 3.83 3 6.5 3s4.83-1 6.5-3m-13-3c1.67-2 3.83-3 6.5-3s4.83 1 6.5 3\"/></g>"}, "just": {"body": "<circle cx=\"8\" cy=\"8\" r=\"6.5\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><circle cx=\"8\" cy=\"5\" r=\"1.5\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><circle cx=\"8\" cy=\"11\" r=\"1.5\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>"}, "kdl": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m6.74 4.996l-1 5.367l2.48.4s2.16.2 2.6-2.262c.44-2.464-1.58-3.184-1.58-3.184z\"/><path fill=\"none\" stroke=\"#f4dbd6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.54 4.916L2.04 8.06l3.86 2.483M1.5 5.076l.58 6.008\"/><path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.28 5.357l.32 5.206l2.9-.26\"/>"}, "key": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 10.5a4.5 4.5 0 1 0-4.02-2.48L1.5 12.5v2h2v-2h2v-2h2l.48-.48c.6.3 1.3.48 2.02.48\"/><path d=\"M12 5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1\"/></g>"}, "kotlin": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#c6a0f6\" d=\"M2.5 13.5h11L8 8\"/><path stroke=\"#f5a97f\" d=\"M8.03 2.5h5.47l-8 8\"/><path stroke=\"#ed8796\" d=\"M2.5 13.5V8\"/><path stroke=\"#7dc4e4\" d=\"M8 2.5H2.5V8l3-2.5\"/></g>"}, "laravel": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.51 5.49v3.29M9.64 3.89l2.86 1.6l2.74-1.53M6.5 12v3.5m-3-12l3-1.5\"/><path d=\"m3.5 10.5l6-3.5V3.5L12.51 2l2.99 1.5V7l-3.06 1.5L9.5 7\"/><path d=\"m.5 2l3-1.5l3 1.5v6.5\"/><path d=\"M.5 2v10.17l6 3.33l6.02-3.41V8.5L6.5 12.04l-3-1.54v-7z\"/></g>"}, "latex": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13 12.5h1.5m-.5-4l-3 4m0-4l3 4m-3.5 0h1m2-4l1.1.01m-4.1-.01h1.42m-4.42 4H9m-1.5-2v4m-.86 0h3.66l.2-.5m-3.75-3.5H10l.5.5M2 13.5h3m-1.5-5v5m-2-3.5l.25-1.5h3.5L5.5 10m4-3.5l2-5l2 5m-3-2h2M9 6.5h1m3 0h1m-9.5-5v5m-1-5h2m-2 5H7l.54-.99\"/>"}, "latte": {"body": "<g fill=\"none\" stroke-linecap=\"round\"><path stroke=\"#eed49f\" d=\"M10.2 4s-1.3-.7-2.5-.7c-1.3 0-1.9.7-1.9 2c0 1.4 2.5 2 2.5 2s1.9.7 1.9 2c0 1.4-.6 2-1.9 2c-1.2 0-2.5-.6-2.5-.6M8.3 2v11.3\"/><path stroke=\"#f5a97f\" d=\"M4 2s-.7 0-1.3.7C2 3.3 2 6 2 6.7C2 7.3.8 7.3.8 8s1.3.7 1.3 1.3c0 .7 0 3.4.6 4c.6.7 1.2.7 1.2.7m8.1 0s.7 0 1.3-.7c.6-.6.6-3.3.6-4c0-.6 1.3-.6 1.3-1.3s-1.3-.7-1.3-1.3c0-.7 0-3.4-.6-4c-.6-.7-1.2-.7-1.2-.7\"/></g>"}, "lerna": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m1 10.3l.6.15l.78.17c.01.01-.14.25-.32.52c-.7 1-.83 1.5-.44 1.5c.12 0 .38-.06.6-.14c.24-.07.67-.11 1.15-.11c.97 0 1.95.24 3.18.8c1.17.53 1.56.65 2.9.93a9 9 0 0 1 1.2.3c.04.04-.05.16-.2.28s.74.17 1.08.13c1.05-.13 1.79-.6 1.8-1.16c.03-.7-.09-1.8-.22-1.88c-.58.6-1.12.59-1.75.63a3.7 3.7 0 0 1-2.05-.48c-.5-.29-1.21-.55-1.6-1.14c-.3-.49-.62-1.28-.56-1.4c.02-.02.13-.02.23.02c.29.11.7.28 2 .11a5.7 5.7 0 0 1 2.46.12c.73.18.62.34 1 .56c.64-1.45.61-1 1.35-1.49c.45-.32.82-.61.81-.67c0-.04-.26-.35-.56-.68c-.47-.52-.6-.6-1.03-.76a8.97 8.97 0 0 1-3.9-2.63C8.58 2.81 8.33 2.54 7.67 2.1a7.3 7.3 0 0 0-2.4-1.06c-.73-.14-.52.1-.21.6c1.11 1.3 1.08 1.34 1.08 2.2c-.11.67-.72.77-1.4.92c-.3.06-.71.05-.91.05c-.3 0-.93-.36-1.27-.73c-.19-.2-.52-.61-.74-.93c-.23-.32-.45-.58-.52-.58\"/><path d=\"M8 5.79c1.27.43 1.34.63 1.76 1.37c.1.17.16.3.15.31l-.25.07c-.33.07-.85.07-1.15 0c-.56-.12-.58-.22-.81-.42a1 1 0 0 1-.3-.42c-.08-.21.04-.47-.04-.68c-.07-.18-.4-.25-.35-.44c.3-.1.78.13 1 .2Z\"/></g>"}, "less": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4 2.5c-.74 0-1.5.76-1.5 1.5v2c0 1.1-1.1 2-1.83 2c.74 0 1.83.9 1.83 2v2c0 .74.76 1.5 1.5 1.5m1.5-8v5a1 1 0 0 0 1 1H7m4.5-4c0-.69-.59-1-1.25-1h-.5c-.66 0-1.25.56-1.25 1.25S9.09 9 9.75 9h.5c.66 0 1.25.56 1.25 1.25s-.59 1.25-1.25 1.25h-.5c-.66 0-1.25-.31-1.25-1m3.5-8c.74 0 1.5.76 1.5 1.5v2c0 1.1 1.1 2 1.83 2c-.74 0-1.83.9-1.83 2v2c0 .74-.76 1.5-1.5 1.5\"/>"}, "lib": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.5 1.5h2a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-11a1 1 0 0 1 1-1m4 3h1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1m3.48.4l1.93-.52a.5.5 0 0 1 .62.35l2.33 8.7a.5.5 0 0 1-.36.6l-1.93.53a.5.5 0 0 1-.61-.36L9.63 5.5a.5.5 0 0 1 .35-.6\"/>"}, "license": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 13.5h7M8.01 1v12.06M1.5 3.5h3l1.5-1h4l1.5 1h3M.5 10L3 4.48L5.5 10C4 11 2 11 .5 10m10 0L13 4.48L15.5 10c-1.5 1-3.5 1-5 0\"/>"}, "lint-staged": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"M4.28 8.2c-.01.02.13.2.38.36c.14.08.3.14.45.16m.45-2.92c-.03.02.56.77 1.42.45\"/><path stroke=\"#f5a97f\" d=\"M3.6 10.45a2 2 0 0 1-.05-.2C3.4 9.69 3.5 9 4.4 8.45c-.67-1.23.66-2.56 1.13-2.6c.24 0-.17-2.8 2.84-2.35m.35 1.51c-.16-.14-.97-.54-.35-1.4m2.56 4.72s.1-.33 0-.74a.9.9 0 0 0-.1-.32\"/><path stroke=\"#f5a97f\" d=\"M6.42 11.5h4.96c.6.07 2.03-1.64-.42-3.21c-.56.63-1.12 1.2-2.09 1.05\"/><path stroke=\"#ed8796\" d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8m-11 4.5l9-9\"/></g>"}, "liquid": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.5 4.49c-.594 0-1.183-.084-1.7-.365a4.6 4.6 0 0 0-4.434 0c-.93.52-2.08.522-3.013.005a4.2 4.2 0 0 0-2.21-.576a4.2 4.2 0 0 0-2.24.578A2.8 2.8 0 0 1 .5 4.514m15 3.942c-.594 0-1.183-.085-1.7-.366a4.6 4.6 0 0 0-4.434 0c-.93.52-2.08.522-3.013.005a4.2 4.2 0 0 0-2.21-.576a4.2 4.2 0 0 0-2.24.578A2.8 2.8 0 0 1 .5 8.479m15 3.984c-.594 0-1.183-.084-1.7-.366a4.6 4.6 0 0 0-4.434 0c-.93.52-2.08.522-3.013.005a4.2 4.2 0 0 0-2.21-.575a4.2 4.2 0 0 0-2.24.577a2.8 2.8 0 0 1-1.403.382\"/>"}, "lisp": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M.5 5.06v6.07C.5 12.41.82 13 2.27 13h5.6c1.04 0 1.63-.51 1.63-1.62c0-.85-.2-1.88-1.5-1.88h-.36C6.4 9.5 6 8.77 6 7.75C6 6.81 6.8 6 7.49 6h2.68\"/><path d=\"M3.5 10.5V4.99C3.5 3.89 3.62 3 5 3h9c.97 0 1.5.99 1.5 1.63c.12 1.55-.98 1.62-2.1 2.16c-.58.26-1.4.52-1.4.98V11\"/></g>"}, "lock": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.744\" d=\"M12.36 7.104c.482 0 .872.39.872.872v5.23c0 .481-.39.871-.872.871H3.639a.87.87 0 0 1-.872-.871v-5.23c0-.482.39-.872.872-.872zm-6.977 0V4.488a2.617 2.617 0 0 1 5.234 0v2.616\"/>"}, "log": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4.5 3.5h9v11h-9z\"/><path d=\"M11.5 3.45V1.5h-9v11h1.95m3.05-5h3m-3 3h3\"/></g>"}, "lua": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M10.5 7A1.5 1.5 0 0 1 9 8.5A1.5 1.5 0 0 1 7.5 7A1.5 1.5 0 0 1 9 5.5A1.5 1.5 0 0 1 10.5 7\"/><path stroke=\"#8aadf4\" d=\"M7 2.5a6.5 6.5 0 1 0 0 13a6.5 6.5 0 0 0 0-13m7-2a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3\"/></g>"}, "luau": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linejoin=\"round\" d=\"M4.289 1.5L14.5 4.245L11.777 14.5L1.5 11.711Z\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linejoin=\"round\" d=\"m9.2 5l-.7 2.8l2.8.7l.7-2.8z\"/>"}, "makefile": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"m9.24 8.47l-5.28 5.58c-.56.6-1.47.6-2.04 0a1.57 1.57 0 0 1 0-2.14L7.2 6.32\"/><path stroke=\"#cad3f5\" d=\"m13.74 8.03l-.86-.93a2.46 2.46 0 0 1-.64-1.68v-.65l-1.89-2.04A3.7 3.7 0 0 0 7.63 1.5H5.5l.64.61A4.72 4.72 0 0 1 7.5 5.57v1.16l1.46 1.5h1.71l1.57 1.42m-.74.85l3-3\"/></g>"}, "markdown": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m9.25 8.25l2.25 2.25l2.25-2.25M3.5 11V5.5l2.04 3l1.96-3V11m4-.5V5M1.65 2.5h12.7c.59 0 1.15.49 1.15 1v9c0 .51-.56 1-1.15 1H1.65c-.59 0-1.15-.49-1.15-1V3.58c0-.5.56-1.08 1.15-1.08\"/>"}, "markdown-mdx": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m9.25 8.25l2.25 2.25l2.25-2.25M3.5 11V5.5l2.04 3l1.96-3V11m4-.5V5M1.65 2.5h12.7c.59 0 1.15.49 1.15 1v9c0 .51-.56 1-1.15 1H1.65c-.59 0-1.15-.49-1.15-1V3.58c0-.5.56-1.08 1.15-1.08\"/>"}, "marko": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"m12 13.5l3.5-5l-3.5-6\"/><path stroke=\"#8aadf4\" d=\"m4 2.5l-3.47 6l3.47 5\"/><path stroke=\"#8bd5ca\" d=\"M6.43 8.5L4 2.5\"/><path stroke=\"#a6da95\" d=\"m9 2.5l-2.57 6\"/><path stroke=\"#eed49f\" d=\"m9 13.5l3.51-5L9 2.5\"/></g>"}, "matlab": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M4 11L.5 8.5L5 7q.78-1.77 1.89-1.89c.74-.07 1.94-1.28 3.61-3.61M5 7l1.5 1.5\"/><path stroke=\"#f5a97f\" d=\"m15.5 12.5l-5-11C8.5 6.83 6.33 10 4 11c1.67-.33 2.67.83 3 3.5c3.5-1.5 3.5-3.5 5-4s1.5 1.5 3.5 2\"/></g>"}, "mdbook": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m15.5 4.39l-2.794 8.978s-.399 1.464-1.596 1.464H2.33S.5 14.233.5 13.002c0-1.23 4.024-11.207 4.024-11.207s.177-.627 1.087-.627s7.095.029 7.095.029s1.597-.067 1.098 1.097c-.5 1.164-2.76 9.212-2.76 9.212s-.2 1.13-1.331 1.13s-9.02-.107-9.02-.107\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m5.977 5.964l1.166-3.316l.714 2.85l2.49-2.693l-.943 3.402M5.538 7.712l-1.035 3.08s3.55.687 4.098-1.125s-3.063-1.955-3.063-1.955\"/>"}, "mermaid": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 2.5c0 6 2.25 5.75 4 7c.83.67 1.17 2 1 4h3c-.17-2 .17-3.33 1-4c1.75-1.25 4-1 4-7C12 2.5 10 3 8 7C6 3 4 2.5 1.5 2.5\"/>"}, "meson": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 1.5C4 3.75 1.5 8 1.5 12.74C5 15 11 15 14.5 12.75C14.5 8 12 3.75 8 1.5\"/><path d=\"M10.43 8.55c-.11.74-.8 1.95-1.67 2.74c.12.18.18.39.18.6c-.55.3-1.09-.03-1.74-.62a5.77 5.77 0 0 1-1.6-2.96a.9.9 0 0 1-.6-.14c.05-.68.73-1.08 1.34-1.32a5.8 5.8 0 0 1 3.29.1c.08-.2.23-.35.4-.45c.58.4.5 1.38.4 2.05\"/></g>"}, "midi": {"body": "<g fill=\"none\" stroke=\"#cad3f5\"><path d=\"M5.1 1.3a7.5 7.5 0 1 0 5.8 0c-.7-.3-1.4.3-1.4 1c0 .2-.2.4-.5.4H7a.5.5 0 0 1-.5-.5c0-.6-.7-1.2-1.4-.9Z\"/><path d=\"M5.5 11.7a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0Zm6 0a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0ZM4 9.2a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0Zm9 0a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0Zm-4.5 4a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0Z\"/></g>", "height": 17}, "mjml": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 4.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m-11-2h7a1 1 0 0 1 0 2h-7a1 1 0 1 1 0-2m11 12a1 1 0 1 0 0-2a1 1 0 0 0 0 2m-11-2h7a1 1 0 0 1 0 2h-7a1 1 0 0 1 0-2m0-3a1 1 0 1 0 0-2a1 1 0 0 0 0 2m4-2h7a1 1 0 0 1 0 2h-7a1 1 0 1 1 0-2\"/>"}, "modernizr": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 14.5h12v-12h-4v4h-4v4h-4zm8 0v-8m-4 8v-4\"/>"}, "ms-excel": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2.5 3.13c0-.77.86-1.63 1.62-1.63h9.76c.76 0 1.62.86 1.62 1.63v9.75c0 .76-.86 1.62-1.62 1.62H4.13c-.77 0-1.63-.86-1.63-1.62M.5 5.5l4 5m0-5l-4 5\"/><path d=\"M7.5 5.5h5v5h-5zm2 0v5m-2-3h5\"/></g>"}, "ms-powerpoint": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2.5 3.13c0-.77.86-1.63 1.62-1.63h9.76c.76 0 1.62.86 1.62 1.63v9.75c0 .76-.86 1.62-1.62 1.62H4.13c-.77 0-1.63-.86-1.63-1.62\"/><path d=\"M7.5 5.8L11.88 8L7.5 10.2zm-7-.3v5m0-2H2a1.5 1.5 0 0 0 0-3H.5\"/></g>"}, "ms-word": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2.5 3.13c0-.77.86-1.63 1.62-1.63h9.76c.76 0 1.62.86 1.62 1.63v9.75c0 .76-.86 1.62-1.62 1.62H4.13c-.77 0-1.63-.86-1.63-1.62\"/><path d=\"m.5 5.5l1 5l1-5l1 5l.97-5m3.03 1h4m-4 3h4\"/></g>"}, "nativescript": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 10.75q0 .75.75.75H6.5V8l3 3.5h1.25q.75 0 .75-.75v-1.5c0-.5.42-.92 1.25-1.25c-.83-.33-1.25-.75-1.25-1.25v-1.5q0-.75-.75-.75H9.5V8l-3-3.5H5.25q-.75 0-.75.75v1.5c0 .5-.42.92-1.25 1.25c.83.33 1.25.75 1.25 1.25zM4 1.5h8A2.5 2.5 0 0 1 14.5 4v8a2.5 2.5 0 0 1-2.5 2.5H4A2.5 2.5 0 0 1 1.5 12V4A2.5 2.5 0 0 1 4 1.5\"/>"}, "nest": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-controller": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-decorator": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-filter": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-guard": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-middleware": {"body": "<path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-pipe": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "nest-service": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.5 15.5l.5-3.47C10.5 16 8.34 15.28 7.52 15.5c-.23.06 1.67-.48 2.48-2c-.9.33-1.56.5-1.98.5c1.42-1.23 1.91-2.4 1.5-3.5c-.34 2.33-4.61 4.11-5.53 2.06c-.6-1.37-.28-2.23.97-2.57c0 1.06.51 1.59 1.54 1.59V10.5l1.97.91C8.16 8.14 6 6.83 2 7.5C1 6.35.5 5.52.5 5c0-.78.25-1 1-1s1-.02 2.03-1.05C5.09 1.46 7.1 1.1 9.5 2.57Q9.17 1.625 10.55.5c1.48.73 2.13 1.73 1.94 3s-1.02 1.94-2.5 2c.49.37 1.15.37 2 0a2.41 2.41 0 0 0 1.48-2c1.35 1.67 2.02 3.33 2.02 5s-.35 3.02-1.04 4.06l-.5-2.06c-.98 3.5-1.56 4.3-2.47 5Z\"/>"}, "netlify": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8bd5ca\" d=\"m3.5 3.5l1 1m-1 8l1-1M1.5 8h3m7 0h3M8 1.5v3m0 7v3\"/><path stroke=\"#cad3f5\" d=\"M6.5 9.5v-3h2c.68 0 .97.57 1 1v2\"/></g>"}, "next": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.33 12.85a6.5 6.5 0 1 1 1.55-2.08\"/><path d=\"M12.33 12.85L5.5 4.5v7m5-7v3\"/></g>"}, "nextflow": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.5 2.36c-2.4.52-4.2 1.62-6.46 3.79C4.7 2.23 1.89 2 1.5 2v2.47c.16.02 2.2.33 4.82 3.48a14.5 14.5 0 0 1-4.82 3.02v2.58a16.4 16.4 0 0 0 6.33-3.62A11.38 11.38 0 0 0 14.5 14v-2.55a9.04 9.04 0 0 1-4.93-3.32c1.81-2.02 3.2-2.93 4.93-3.34z\"/>"}, "nginx": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M5.5 10.5v-5l5 5v-5\"/><path d=\"M1.5 11.5v-7L8 .5l6.5 4v7l-6.5 4z\"/></g>"}, "nim": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1 7L.5 4.5l1.01.67c.28-.27.47-.48 1.18-.85l.56-1.82L4.5 3.84c.77-.18 1.53-.36 2.4-.33L8 1.5l1.1 2.01c.87-.03 1.63.15 2.4.33l1.25-1.34l.56 1.82c.7.37.9.58 1.18.85l1.01-.67L15 7m-1.5 1C13 6.5 11 5.5 8 5.5S3 6.5 2.5 8m11.5.75L13.5 8l-1 1.5l-1.5.5l-3-1.5L5 10l-1.5-.5l-1-1.5l-.5.75L1 7l1.25 3.75C3 12.75 6 13.5 8 13.5s5-.75 5.75-2.75L15 7z\"/>"}, "ninja": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8M2.68 4.5H13.4m-11.65 5h12.5\"/><path d=\"M6.5 7a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5m4 0a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "nix": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M.5 7.5H4m1.39-2L2.05 11\"/><path stroke=\"#8aadf4\" d=\"M4 1.5L5.5 4m3.5.5H2.55\"/><path stroke=\"#7dc4e4\" d=\"m12 1.5l-1.5 3m1.01 2.6L8.5 1.5\"/><path stroke=\"#8aadf4\" d=\"M15.5 8.52L12 8.5m-1.38 2L14 5\"/><path stroke=\"#7dc4e4\" d=\"m12.5 14.5l-2.5-3m-2.97.02l6.48-.02\"/><path stroke=\"#8aadf4\" d=\"m4 14.5l1.5-3M4.53 9l2.97 5.5\"/></g>"}, "nix-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M.5 7.5H4m1.39-2L2.05 11\"/><path stroke=\"#8aadf4\" d=\"M4 1.5L5.5 4m3.5.5H2.55\"/><path stroke=\"#7dc4e4\" d=\"m12 1.5l-1.5 3m1.01 2.6L8.5 1.5\"/><path stroke=\"#8aadf4\" d=\"m12.733 7.063l.422-.688L14 5\"/><path stroke=\"#7dc4e4\" d=\"m7.03 11.52l.81-.002\"/><path stroke=\"#8aadf4\" d=\"m4 14.5l1.5-3M4.53 9l2.97 5.5\"/></g><path fill=\"none\" stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/>"}, "nodemon": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m14.17 5.79l-.67-.29c.64-.84.98-2.12.5-4c0 0-.94 2.79-3.11 2.7L8.35 2.57a.7.7 0 0 0-.32-.08h-.05a.74.74 0 0 0-.32.08L5.12 4.2c-2.17.09-3.08-2.7-3.08-2.7c-.48 1.88-.18 3.15.46 3.99l-.66.29a.6.6 0 0 0-.34.53l.01 7.88c0 .1.07.2.18.26c.1.05.23.05.33 0l3.64-1.82a.6.6 0 0 0 .34-.52V8.34a.6.6 0 0 1 .34-.53l1.32-.73a.74.74 0 0 1 .69 0l1.3.73c.22.11.35.31.35.53v3.78c0 .21.13.41.34.52L14 14.46c.11.05.24.05.35 0a.3.3 0 0 0 .17-.26V6.32a.6.6 0 0 0-.34-.53\"/>"}, "npm": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M2.45 1.5a.95.95 0 0 0-.95.95v11.1a.95.95 0 0 0 .95.95h11.1a.95.95 0 0 0 .95-.95V2.45a.95.95 0 0 0-.95-.95z\"/><path stroke=\"#cad3f5\" d=\"M4.5 4.5h7v7h-2v-5h-2v5h-3z\"/></g>"}, "npm-ignore": {"body": "<g fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2.45 1.5a.95.95 0 0 0-.95.95v11.1a.95.95 0 0 0 .95.95h11.1a.95.95 0 0 0 .95-.95V2.45a.95.95 0 0 0-.95-.95z\"/><path d=\"M4.5 4.5h7v7h-2v-5h-2v5h-3z\"/></g>"}, "npm-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><path stroke=\"#cad3f5\" d=\"M9.5 9V5.5h-2v6h-4v-8h8v3\"/><path stroke=\"#ed8796\" d=\"M7.54 13.5H3A1.5 1.5 0 0 1 1.5 12V3c0-.83.67-1.5 1.5-1.5h9c.83 0 1.5.67 1.5 1.5v3.5\"/></g>"}, "nuget": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.5 3.5h7a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-7c0-1.1.9-2 2-2M2 2.5a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m4.5 5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m4 5a2 2 0 1 0 0-4a2 2 0 0 0 0 4\"/>"}, "nunjucks": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14 14.5h-4c-.75 0-1-.25-1-1v-3l2-.5v2.5h2v-8h2v9c0 .75-.25 1-1 1m-13 0v-13h1.5l2.5 6v-6h2v13H5.5L3 8.5v6z\"/>"}, "nuxt": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.5 12.5h6l-5-7L7.44 11c-.67.98-1.32 1.48-1.94 1.5h-5l5-9l3 5\"/>"}, "nuxt-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.5 12.5h6l-5-7L7.44 11c-.67.98-1.32 1.48-1.94 1.5h-5l5-9l3 5\"/>"}, "ocaml": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.5 8V3c0-.83.67-1.5 1.5-1.5h10c.83 0 1.5.67 1.5 1.5v10c0 .83-.67 1.5-1.5 1.5H9\"/><path d=\"m1.5 8l1.14-2.3q.09-.21.36-.24a.8.8 0 0 1 .44.13c.18.12.23.53.28.64c.06.1.64 1.23.85 1.23c.2 0 .71-1.47.71-1.47s.37-.49.72-.49s.55.32.67.49c.12.16.24 1.76.46 2.01s1.32.87 1.67.73c.34-.13.53-.4.63-.73c.1-.34-.14-.75 0-1a1.1 1.1 0 0 1 1.02-.55c.56.03 2.05.56 2.05 1.05q0 .75-1.5.75c-.48 1.33.28 2.22-3 2.25l1 4\"/><path d=\"m4.5 14.5l1.5-4l1 4zm-2 0l1.5-4l-1.5-.5l-1 1.54V14l1 .49Z\"/></g>"}, "org": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" d=\"M2.287 10.409C1.95 11.15.888 12.95 2 15.48M9.56 9.5s-1.911 2.816 1.085 5.55m-.372-11.662L13.5.5l-1.96 3.986\"/><path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" d=\"M8 7.467c.344 2.288 2.827 1.646 3.575 1.66c.094.003.346.157.425.208l.994.47c.116.073.264.124.389.069l.467-.207a.77.77 0 0 0 .603-.673l.049-.461a.77.77 0 0 0-.206-.612L11.5 4.5l-1.5-1v-2s-2.19.261-2 1.925c0 0-4.434 1.764-1.513 5.867c0 0 .79 1.51.018 3.16\"/><path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" d=\"M8 3s-6.4.163-4 6\"/><path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"10\" d=\"M3.526 6s-3.462 2.329-.025 6c0 0 .979 1.518.499 3.497\"/>"}, "package-json": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.5 6.5v-.17c0-.73-.6-1.33-1.33-1.33H9a1.5 1.5 0 1 0 0 3h1a1.5 1.5 0 0 1 0 3H9a1.5 1.5 0 0 1-1.5-1.5M5.5 5v4.5c0 1.5-.08 1.87-.71 2.5c-.5.5-1.5.5-1.5.5c-.96-.4-1.2-.66-1.79-1v-7L8 .5l6.5 4v7l-6.5 4l-2.49-1.53\"/>"}, "panda-css": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.18 1.55c-.7.08-1.36.22-2 .47a5.27 5.27 0 0 0-3.05 2.84c-.45 1-.61 2.05-.63 3.14c-.02 1.15.12 2.28.35 3.4c.2 1.02.47 2.02.86 2.98c.03.09.08.12.18.12h4.74a2 2 0 0 0 .14 0l-.02-.06l-.3-.66c-.2-.41-.4-.83-.57-1.25A13.6 13.6 0 0 1 5.8 8.5a5.1 5.1 0 0 1 .12-1.84c.2-.69.6-1.17 1.29-1.38a3.1 3.1 0 0 1 1.89.02c.55.19.92.58 1.07 1.16c.12.45.12.9.03 1.35c-.07.35-.2.66-.46.92c-.45.45-1 .56-1.61.52l-.33-.03l-.15-.02v.13c.04.1.06.21.08.32c.06.25.12.5.2.76c.15.48.32.96.52 1.43a8.74 8.74 0 0 0 4.05-1.3l.06-.04a3.87 3.87 0 0 0 1.39-1.48c.53-1.02.63-2.1.47-3.22a4.54 4.54 0 0 0-1.54-2.88a5.4 5.4 0 0 0-2.06-1.1c-1.2-.34-2.41-.39-3.65-.28Z\"/>"}, "pdf": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.8 14.34c1.81-1.25 3.02-3.16 3.91-5.5c.9-2.33 1.86-4.33 1.44-6.63c-.06-.36-.57-.73-.83-.7c-1.02.06-.95 1.21-.85 1.9c.24 1.71 1.56 3.7 2.84 5.56c1.27 1.87 2.32 2.16 3.78 2.26c.5.03 1.25-.14 1.37-.58c.77-2.8-9.02-.54-12.28 2.08c-.4.33-.86 1-.6 1.46c.2.36.87.4 1.23.15h0Z\"/>"}, "perl": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 14.5v-3.34c-1-.66-1-1.35-1-2.66m-3 1l.02 2.53l.98 2.47m-4-5v5m9 0V9.23s.17-1.73-1-1.73c0-1.5-.5-6-2.5-6S8.75 4.25 8.75 4.25A3.67 3.67 0 0 0 6.5 7.12v-3.5c0-.63-.85-1.32-1.5-1.32c-.92 0-1.33.59-1.5 1.2H2.25c-.42.11-.75.59-.75 1c0 .5.28 1 .75 1h1.22l.02 3c.01.75.51 1 1.51 1h4.5\"/>"}, "php": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 12.5v.74c0 .76.774 1.26 1.5 1.26c.938 0 1.5-.5 1.5-1.255V6c0-1.715 1.494-3.478 3.65-3.501c2.344 0 3.85 1.558 3.85 3c.166 2.99-1.422 4.137-3.504 5v4h8.002V9c.041-.635-.56-1.844-1.367-2.5c-.937-.692-2.073-.997-3.131-1m.5 9v-3M6 6.5a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1\"/>"}, "phrase": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M3.5 1.5H10c1.385 0 2.5 1.115 2.5 2.5v5c0 1.385-1.115 2.5-2.5 2.5H9\"/><path stroke=\"#8bd5ca\" d=\"M3.5 1.5v10l4 3v-10z\"/></g>"}, "phtml": {"body": "<g fill=\"none\" stroke=\"#8aadf4\"><path d=\"M1 2.7c0-.7.5-1.4 1.2-1.4h11.6c.7 0 1.3.7 1.2 1.4l-1.3 10.4c0 .4-.3.8-.6 1l-4.5 2.4q-.6.45-1.2 0l-4.5-2.4c-.3-.2-.5-.6-.6-1z\"/><path stroke-linecap=\"round\" d=\"m4 9l.2-1.4m0 0S4.4 6 4.7 6h.6c.3 0 .7.3.7.8c0 .6-.4.8-.7.8zM7.3 9l.4-1.5M8 6l-.3 1.5m0 0s.5-.5.8-.5c.4 0 .8 0 .8.3L9 9m1.6 0l.2-1.4m0 0S11 6 11.3 6h.6c.3 0 .7.3.7.8c0 .6-.4.8-.7.8z\"/></g>", "height": 18}, "pixi": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m6.702 5.889l5.046 6.426\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3.71 3.786l-.396-2.149l2.03.843L7.39 1.5l-.275 2.149l1.772 1.667l-2.392.24l-.808 2.132L4.569 5.78l-2.288-.24Zm4.995 6.005s-.656.559-1.192.073s.145-1.474.633-1.945s1.54-1.07 2.015-.594c.696.7-.268 1.382-.268 1.382\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.31 11.076s-1.132 1.025-1.817.447c-.684-.578.185-1.752.81-2.312c.623-.56 1.928-1.272 2.487-.665s-.806 1.956-.806 1.956\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.93 13.037s-1.508 1.326-2.33.621s.221-2.136.971-2.818c.75-.683 2.318-1.55 2.991-.81s-.979 2.331-.979 2.331\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.785 12.358l1.682 2.142\"/>"}, "pixi-lock": {"body": "<path fill=\"none\" stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.043 4.989L9.146 9.94\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m2.39 3.123l-.35-1.907l1.799.747l1.814-.87l-.244 1.908l1.57 1.48l-2.119.213l-.716 1.892l-.991-1.694l-2.028-.213Zm4.428 5.33s-.582.495-1.057.064c-.474-.432.128-1.308.56-1.726s1.366-.95 1.787-.527c.616.62-.238 1.226-.238 1.226\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.59 9.358s-1.354 1.145-1.96.632c-.607-.513.164-1.555.716-2.052c.553-.498 1.71-1.129 2.205-.59c.497.538-.965 2.003-.965 2.003\"/>"}, "plantuml": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.389 10.167h7.222v2.889a1.444 1.444 0 0 1-1.444 1.444H5.833a1.444 1.444 0 0 1-1.444-1.444ZM8 5.833A4.333 4.333 0 0 0 3.667 1.5H1.5v1.444a4.333 4.333 0 0 0 4.333 4.334H8m0 0a4.333 4.333 0 0 1 4.333-4.334H14.5v.723A4.333 4.333 0 0 1 10.167 8H8m0 2.167V5.833\"/>"}, "playwright": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M5 9.5c-1.02.35-1.5 1-1.5 1\"/><path stroke=\"#ed8796\" d=\"m5.97 12.36l-.1.03c-2.98.8-4.55-2.64-5.03-4.43C.62 7.13.53 6.28.5 5.88c-.03-.46.29-.33.89-.23a6.5 6.5 0 0 0 2.7-.17c.52-.14 1.26-.4 1.74-.65\"/><path stroke=\"#ed8796\" d=\"M2.5 7.5s.36.56 1.18.31C4.5 7.57 4.5 7 4.5 7\"/><path stroke=\"#a6da95\" d=\"M15.05 7.57c-.62 2.33-2.67 6.81-6.52 5.78C4.68 12.3 5.14 7.4 5.77 5.08c.28-1.07.58-1.84.81-2.31c.27-.54.53-.18 1.14.32a8.4 8.4 0 0 0 3.14 1.56a8.4 8.4 0 0 0 3.49.22c.78-.13 1.19-.3 1.15.29c-.04.52-.16 1.33-.45 2.4Z\"/><path stroke=\"#a6da95\" d=\"M9.5 7.5s0-.82-.72-1.08s-1.28.08-1.28.08m0 3S8 11 9.21 11.33c1.21.34 2.29-.83 2.29-.83M13 9s.18-.87-.58-1.23C11.66 7.4 11 8 11 8\"/></g>"}, "plop": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.5 10.62c.2 2.6 1.94 4.88 4.54 4.88h.05c2.6 0 4.33-2.29 4.53-4.88l.01-.23h0c0-1.15-.61-2.74-1.8-4.62A391 391 0 0 0 8.31 1.9l-.17-.27l-.05-.07l-.03-.05l-.04.05l-.05.07l-.17.27l-2.5 3.88C4.1 7.66 3.5 9.25 3.5 10.4v.23h0Z\"/>"}, "pnpm": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"M6 2v4\"/><path stroke=\"#cad3f5\" d=\"M10 9.5V14M6 6v8\"/><path stroke=\"#f5a97f\" d=\"M10 2v7.5m4 .5V2H2v4h12\"/><path stroke=\"#cad3f5\" d=\"M2 10v4h12v-4z\"/></g>"}, "pnpm-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><path stroke=\"#f5a97f\" d=\"M4.5 1.5v3\"/><path stroke=\"#cad3f5\" d=\"M7.5 7.5v3m-3-6v6\"/><path stroke=\"#f5a97f\" d=\"M7.5 1.5v6m3 0v-6h-9v3h9\"/><path stroke=\"#cad3f5\" d=\"M10.5 7.5h-9v3h7\"/></g>"}, "poetry-lock": {"body": "<defs><radialGradient id=\"catppuccinPoetryLock0\" cx=\"0\" cy=\"0\" r=\"1\" gradientTransform=\"matrix(10.5875 0 0 10.784 4.993 -.868)\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#fff\"/><stop offset=\"1\"/></radialGradient></defs><path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><path fill=\"url(#catppuccinPoetryLock0)\" d=\"m0 0l7.778 7.923q.104-.107.205-.215Z\"/><path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5.5h10a10 10 0 0 1-3.306 7.43\"/><path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m7.25 7.835l1.058 1.16l.529.58m-1.408 2.094A12.9 12.9 0 0 1 2.69 13.51L.501 10.5\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7.32 7.814A10 10 0 0 1 .5 10.5V.5z\"/>"}, "postcss": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8\"/><path d=\"m8 1.5l6 9H2z\"/><path d=\"M5.5 5.5h5v5h-5z\"/><path d=\"M9.5 8A1.5 1.5 0 0 1 8 9.5A1.5 1.5 0 0 1 6.5 8A1.5 1.5 0 0 1 8 6.5A1.5 1.5 0 0 1 9.5 8\"/></g>"}, "powershell": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M2 15.5c-.7 0-1.5-.8-1.5-1.5V5c0-.7.8-1.5 1.5-1.5h9c.7 0 1.5.8 1.5 1.5v9c0 .7-.8 1.5-1.5 1.5z\"/><path d=\"m1.2 3.8l3.04-2.5S5.17.5 5.7.5h8.4c.66 0 1.4.73 1.4 1.4v7.73a2.7 2.7 0 0 1-.7 1.75l-2.68 3.51M3 8.5l3 2l-3 2m4 0h2\"/></g>"}, "pre-commit": {"body": "<rect width=\"9.801\" height=\"9.801\" x=\"-4.901\" y=\"6.413\" fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" rx=\"1.032\" ry=\"1.032\" transform=\"rotate(-45)\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.569 10.628V5.43h2.619s1.076.157 1.076 1.38c0 1.224-1.076 1.462-1.076 1.462H6.663\"/>"}, "premake": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"m11.05 4.4l-.72-.2l-.06-.9l-.8-.1l-.17.96l-.7.17l-.45-.93l-1.05.32l.39.96l-.37.47l-.9-.38l-.5.6l1.2 1.54m4.62-3.13s-1 1.74-1.77 2.27c-.76.53-1.42 1.14-3.9.84c-2.5-.3-3.9.04-4.2.38s-.25 1.07.58 1.51c.82.44 1.94.8 1.94.8\"/><path stroke=\"#eed49f\" d=\"m11.86 10.7l.54-.52l.78.42l.5-.65l-.7-.65l.22-.7l1 .1l.3-1.08l-1.01-.17l-.2-.56l.8-.59l-.23-.74l-1.92.22m.17 5.67s-.92-1.8-.96-2.73c-.03-.94-.2-1.83 1.36-3.84c1.55-2 2-3.4 1.89-3.85c-.12-.44-.76-.78-1.56-.3c-.8.5-1.55 1.45-1.55 1.45\"/><path stroke=\"#8aadf4\" d=\"m6.12 8.41l.18.74l-.75.5l.29.76l.9-.3l.49.54l-.58.84l.77.8l.64-.82l.58.12l.1 1l.74.16l.77-1.82m-4.9-2.67s1.99.08 2.8.51c.8.44 1.66.74 2.58 3.12c.93 2.37 1.89 3.47 2.32 3.6c.44.11 1.04-.3 1.03-1.25c0-.95-.35-2.1-.35-2.1\"/></g>"}, "prettier": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M1.5 2.5h11m-11 6h5\"/><path stroke=\"#eed49f\" d=\"M1.5 4.5h5m3 4h5\"/><path stroke=\"#c6a0f6\" d=\"M9.5 4.5h5m-13 2h5m-5 6h5\"/><path stroke=\"#ed8796\" d=\"M9.5 6.5h5m-13 4h11m-11 4h5\"/></g>"}, "prettier-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 2.5h11m-11 6h5m-5-4h5m3 4h5m-5-4h5m-13 2h5m-5 6h5m3-6h5m-13 4h11m-11 4h5\"/>"}, "prisma": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8 .5l6.5 12.05l-10 2.95l-3-5zm-3.5 15L8 .5\"/>"}, "prolog": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 13.5c-.33.33-.5.67-.5 1s.17.67.5 1c.17-.67.5-1 1-1s.83.33 1 1c.33-.33.5-.83.5-1.5h2c0 .67.17 1.17.5 1.5c.17-.67.5-1 1-1s.83.33 1 1c.33-.33.5-.67.5-1s-.17-.67-.5-1l1-1l1.25 1.25c0-2.75 1-2.75 1-5.75a7.1 7.1 0 0 0-2-5.25A3.64 3.64 0 0 1 13 .5c-1.17 0-2 .42-2.5 1.25A3.08 3.08 0 0 0 8 .5c-1 0-1.83.42-2.5 1.25C5 .92 4.17.5 3 .5c.5.83.58 1.58.25 2.25a7.1 7.1 0 0 0-2 5.25c0 3 1 3 1 5.75L3.5 12.5zm6-5a2 2 0 1 0 0-4a2 2 0 0 0 0 4m-5 0a2 2 0 1 0 0-4a2 2 0 0 0 0 4M7 8l1 2.5L9 8m1.5-1.5\"/>"}, "properties": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 1.5c-.87 0-1.17 1.32-2.03 1.63c-.86.3-2.17-.68-2.84 0c-.68.67.3 1.98 0 2.84S1.5 7.13 1.5 8s1.32 1.17 1.63 2.03c.3.86-.68 2.17 0 2.85c.67.67 1.98-.3 2.84 0c.85.3 1.16 1.62 2.03 1.62s1.17-1.32 2.03-1.63c.86-.3 2.17.68 2.85 0c.67-.67-.3-1.98 0-2.84c.3-.85 1.62-1.16 1.62-2.03s-1.32-1.17-1.63-2.03c-.3-.86.68-2.17 0-2.84c-.67-.68-1.98.3-2.84 0S8.87 1.5 8 1.5m0 9a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5\"/>"}, "proto": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"m.5 8.5l3-6h3l-3 6\"/><path stroke=\"#8aadf4\" d=\"M6.5 13.5h-3l-3-5l1.36-2.73z\"/><path stroke=\"#eed49f\" d=\"m15.5 7.5l-3 6h-3l3-6\"/><path stroke=\"#a6da95\" d=\"M9.5 2.5h3l3 5l-1.36 2.73z\"/></g>"}, "pug": {"body": "<path fill=\"none\" stroke=\"#f4dbd6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.56 3.68c.66-.5 1.15-.83 1.48-1c.5-.24 5.43-.24 5.92 0c.33.17.82.5 1.48 1m-8.88 0C1.75 4 .76 4.34.6 4.67c-.25.5 0 3.08.49 3.58c.33.33.82.33 1.48 0l1.48-3.58Zm8.88 0c1.81.33 2.8.66 2.96.99c.25.5 0 3.1-.49 3.6c-.33.33-.82.33-1.48 0l-1.48-3.6ZM2.57 8.14c-.33 1.65-.33 2.53 0 2.86s1.15.76 2.47 1.1m8.39-3.84c.33 1.66.33 2.41 0 2.74s-1.15.76-2.47 1.1M9 8.45c-.74-.49-1.24-.49-1.98 0c-.5.33-1.15 1.65-1.97 3.63c-.5.5-.58.91-.25 1.24c.5.5 2.22-.33 3.21-.33s2.71.82 3.2.33c.34-.33.25-.74-.24-1.24c-.82-1.98-1.48-3.3-1.97-3.63m-2.22.41C6.93 9.7 7.34 10 8 10s1.07-.3 1.23-1.13M5.5 6.5\"/>"}, "puppeteer": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m12 2l.5 6M4 2l-.5 6\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 10h10M4 8h8c.554 0 1 .446 1 1v5.5c0 .554-.446 1-1 1H4c-.554 0-1-.446-1-1V9c0-.554.446-1 1-1\"/><path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.5 3L3 4.5V5l1.5.5L8 4l3.5 1.5L13 5v-.5L9.5 3L13 1.5V1L11.5.5L8 2L4.5.5L3 1v.5Z\"/>"}, "python": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M8.5 5.5h-3m6 0V3c0-.8-.7-1.5-1.5-1.5H7c-.8 0-1.5.7-1.5 1.5v2.5H3c-.8 0-1.5.7-1.5 1.5v2c0 .8.7 1.5 1.48 1.5\"/><path stroke=\"#eed49f\" d=\"M10.5 10.5h-3m-3 0V13c0 .8.7 1.5 1.5 1.5h3c.8 0 1.5-.7 1.5-1.5v-2.5H13c.8 0 1.5-.7 1.5-1.5V7c0-.8-.7-1.5-1.48-1.5H11.5c0 1.5 0 2-1 2h-2\"/><path stroke=\"#8aadf4\" d=\"M2.98 10.5H4.5c0-1.5 0-2 1-2h2m0-5\"/></g>"}, "python-compiled": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M8.5 5.5h-3m6 0V3c0-.8-.7-1.5-1.5-1.5H7c-.8 0-1.5.7-1.5 1.5v2.5H3c-.8 0-1.5.7-1.5 1.5v2c0 .8.7 1.5 1.48 1.5\"/><path stroke=\"#8087a2\" d=\"M10.5 10.5h-3m-3 0V13c0 .8.7 1.5 1.5 1.5h3c.8 0 1.5-.7 1.5-1.5v-2.5H13c.8 0 1.5-.7 1.5-1.5V7c0-.8-.7-1.5-1.48-1.5H11.5c0 1.5 0 2-1 2h-2\"/><path stroke=\"#cad3f5\" d=\"M2.98 10.5H4.5c0-1.5 0-2 1-2h2m0-5\"/></g>"}, "python-config": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M6.961 4.192h-2.77m5.54 0V1.884C9.73 1.146 9.083.5 8.345.5h-2.77c-.738 0-1.384.646-1.384 1.384v2.308H1.884C1.146 4.192.5 4.838.5 5.577v1.846c0 .738.646 1.384 1.366 1.384\"/><path stroke=\"#eed49f\" d=\"M8.115 8.807H6.038m-2.77 0v2.308c0 .739.647 1.385 1.385 1.385h1.385m6.35-4.55c.071-.164.112-.343.112-.527V5.577c0-.739-.646-1.385-1.366-1.385H9.73c0 1.385 0 1.846-.923 1.846H6.961\"/><path stroke=\"#8aadf4\" d=\"M1.866 8.807h1.403c0-1.384 0-1.846.923-1.846h1.846m0-4.615\"/></g>"}, "r": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M13.5 9.5c.63-.7 1-1.54 1-2.43c0-2.52-2.91-4.57-6.5-4.57S1.5 4.55 1.5 7.07c0 1.9 1.65 3.53 4 4.22\"/><path stroke=\"#8aadf4\" d=\"M10.5 9.5c.4 0 .86.34 1 .7l1 3.3m-5 0v-8h3.05c.95 0 1.95 1 1.95 2s-1 2-1.95 2H7.5Z\"/></g>"}, "racket": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M13.11 12.01A6.5 6.5 0 0 0 5.77 1.9c2.65 1.38 6.14 5.9 7.34 10.12h0Z\"/><path stroke=\"#ed8796\" d=\"M7.07 5.65A11.3 11.3 0 0 0 3.72 3.1a6.48 6.48 0 0 0-.61 9.17c.88-2.65 2.54-5.2 3.96-6.63Zm1.2 1.47a15.15 15.15 0 0 0-3.32 6.62a6.47 6.47 0 0 0 6.18-.04a17.9 17.9 0 0 0-2.86-6.58\"/></g>"}, "razor": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 8v1c0 .67.4 1.21.97 1.55c.58.34 1.53.34 2.1 0c.58-.34.94-.95.93-1.62V8a6.5 6.5 0 1 0-2.79 5.33M10.5 8a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0\"/>"}, "readme": {"body": "<g fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 14.5a6.5 6.5 0 1 0 0-13a6.5 6.5 0 0 0 0 13\"/><path d=\"M7 7.5h2v5H7Zm2-3a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1\"/></g>"}, "reason": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 1.5h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2m7 9h2\"/><path d=\"M13 7.5h-2.5v5H13m-6-2l1.5 2m-3 0l-.02-5H7c.75 0 1.5.5 1.5 1.5s-.75 1.5-1.5 1.5H5.5\"/></g>"}, "redwood": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.74 3.19v-.65L8 1l3.26 1.54v.65m-6.52 0L8 5.56M4.74 3.19L2.19 5.56m5.81 0L4.74 7.77M8 5.56l3.26-2.37M8 5.56l3.26 2.21m-6.52 0l3.26 3m-3.26-3L2.19 5.56m2.55 2.21L1.99 9.45M8 10.76L4.74 12.5M8 10.76l3.26-2.99m-3.26 3l3.26 1.72m-6.52 0L8 15l3.26-2.51m-6.52 0H3.5L2 9.45m.2-3.9l-.7 2.92l.5 1m9.26-6.27l2.55 2.37m-2.55 2.21l2.55-2.21m-2.55 2.21l2.75 1.68m-2.75 3.04h1.23L14 9.45m-.2-3.9l.69 2.91l-.5 1\"/>"}, "release": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.047 5.892C2.637 5.334 1.5 8.682 1.5 8.682l2.842.557m5.928.798c.568 3.347-2.842 4.463-2.842 4.463l-.568-2.79\"/><path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.888 7.008a1.607 1.578 0 1 1 2.274-2.231a1.607 1.578 0 0 1-2.274 2.23\"/><path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.637 10.913c-1.137 1.115-.569 2.789-.569 2.789s1.705.558 2.842-.558\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.535 8.94l-4.92 2.53l-2.273-2.23l2.577-4.83a4.82 4.82 0 0 1 3.684-2.502l3.356-.405a.478.478 0 0 1 .538.528l-.413 3.294a4.74 4.74 0 0 1-2.549 3.616\"/>"}, "remix": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 14.5c0-1.67 0-3-2-3h-6v-3h6.63c.97 0 1.37-.67 1.37-1.5s-.32-1.5-1.38-1.5H2.5v-3h8c2.4 0 3 2.33 3 4s-.67 2.5-2 3.5c1.33.66 2 2.17 2 4.5m-11 0H8\"/>"}, "renovate": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M11.28 11.04c-.2-.3-.33-.53-.25-.85c.05-.2.17-.38.31-.52C12.62 8.41 13.76 7.32 14.8 6c.09-.12.13-.27.17-.41c.03-.12.03-.25.03-.38c0-.12-.01-.24-.05-.35a6.9 6.9 0 0 0-1.4-2.18c-.14-.1-.28-.2-.44-.27a1.1 1.1 0 0 0-.36-.09c-.16 0-.33.01-.48.05c-.17.05-.29.13-.46.23m.31 12.15l-1.34-2.26a.7.7 0 0 1-.08-.56a.73.73 0 0 1 .35-.44l.77-.43a.77.77 0 0 1 1.02.27l1.34 2.26\"/><path stroke=\"#8bd5ca\" d=\"m2.63 5.4l6.41-3.6c.91-.52 2.06-.2 2.59.69A1.83 1.83 0 0 1 10.95 5L4.54 8.62c-.9.51-2.06.2-2.59-.7a1.83 1.83 0 0 1 .68-2.52\"/></g>"}, "rescript": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 1.5h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2\"/><path d=\"M5.5 4.55c.83 0 1.5.67 1.5 1.5v4a1.5 1.5 0 1 1-3 0v-4c0-.83.67-1.5 1.5-1.5M12 6a1.5 1.5 0 0 1-1.5 1.5A1.5 1.5 0 0 1 9 6a1.5 1.5 0 0 1 1.5-1.5A1.5 1.5 0 0 1 12 6\"/></g>"}, "roblox": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.752 14.5L1.5 11.734L4.249 1.5L14.5 4.266ZM9.814 6.965l-2.862-.787l-.766 2.857l2.862.787Z\"/>"}, "robots": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3.5 5.5h9a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-9a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1\"/><path d=\"M6.5 9a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5m4 0a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5M8 5.5v-2m-7.5 5v3m15-3v3M9 2.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m-2.5 10h3\"/></g>"}, "rojo": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m2.438 12.065l4.12-10.127m-2.581.897s4.842-2.752 6.86-.352c1.539 2.275-6.443 5.255-6.443 5.255S3.96 9.79 13.562 14.5\"/>"}, "rollup": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"M4.37 14.5h9.13l-2-5c1.36-1.08 2-2.13 2-4c0-2.4-2.25-3.97-4-4h-7v11.3\"/><path stroke=\"#eed49f\" d=\"M11.46 6.2c-3.25 3.32-4.37 4.25-7.2 8.3c-1.03-.13-1.67-.61-1.74-1.55c-.36-.35 4.87-8.22 5.42-9.16c.6-1 2.55-2.22 3.78-1.4c-1.18-.38-2.58.56-2.41 1.5l.47 1.89c.34.58.66.8 1.18.73c.56-.16.67-.53.63-1l-.36-.99\"/></g>"}, "root": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4.5 4.5H12A1.5 1.5 0 0 1 13.5 6v.5m-7.5 7H2A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><circle cx=\"11.5\" cy=\"12\" r=\"3\"/><circle cx=\"11.5\" cy=\"12\" r=\".5\"/></g>"}, "root-open": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1\"/><circle cx=\"11.5\" cy=\"12\" r=\"3\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><circle cx=\"11.5\" cy=\"12\" r=\".5\" fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>"}, "ruby": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 9.06v2.5c.02.86.36 1.61.9 2.15c1.76 1.76 5.71.65 8.84-2.47c3.12-3.13 4.23-7.08 2.47-8.84a3.1 3.1 0 0 0-2.15-.9h-2.5M14.5 4l-.25 10.25L4 14.5m4.39-6.11c2.34-2.35 3.29-5.2 2.12-6.37S6.49 1.8 4.14 4.14C1.8 6.5.85 9.34 2.02 10.51s4.02.22 6.37-2.12M5.5 14.5l.25-3.75L11 11l-.25-5.25l3.75-.25\"/>"}, "ruby-gem": {"body": "<g fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m8 12.5l4.5-5l-2-2h-5l-2 2z\"/><path d=\"M14.5 12L8 15.5L1.5 12V4L8 .5L14.5 4z\"/></g>"}, "ruby-gem-lock": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#ed8796\" d=\"m6 10.5l3.5-4l-1.5-2H4l-1.5 2z\"/><path stroke=\"#ed8796\" d=\"M7.5 12.68L6 13.5l-5.5-3v-7L6 .5l5.5 3v3\"/><path stroke=\"#8087a2\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/></g>"}, "ruff": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.958 1.5a.22.22 0 0 0-.224.217v.65c0 .12-.1.216-.224.216h-.627a.22.22 0 0 0-.224.217v1.733c0 .12-.1.217-.224.217h-.627a.22.22 0 0 0-.225.217v1.191c0 .12-.1.217-.224.217h-.627a.22.22 0 0 0-.224.217v1.191c0 .12-.1.217-.224.217h-.56a.22.22 0 0 0-.224.217v.65c0 .12.1.216.224.216h2.912a.22.22 0 0 1 .224.217v1.192c0 .12-.1.216-.224.216H5.01a.22.22 0 0 0-.224.217v1.192c0 .12-.1.216-.224.216h-.627a.22.22 0 0 0-.224.217v1.733c0 .12.1.217.224.217h1.792a.22.22 0 0 0 .224-.217v-.866h.896a.22.22 0 0 0 .224-.217v-.65c0-.12.1-.217.224-.217h.628a.22.22 0 0 0 .224-.216v-.65c0-.12.1-.217.224-.217h.627a.22.22 0 0 0 .224-.217v-.65c0-.12.1-.216.224-.216h.627a.22.22 0 0 0 .224-.217V9.3c0-.12.1-.217.224-.217h.628a.22.22 0 0 0 .224-.216v-.65c0-.12.1-.217.224-.217h.627a.22.22 0 0 0 .224-.217V6.05a.22.22 0 0 0-.224-.217h-.65a.22.22 0 0 1-.224-.216v-.65c0-.12.1-.217.224-.217h.628a.22.22 0 0 0 .224-.217v-.65c0-.12.1-.216.224-.216h.627a.22.22 0 0 0 .224-.217V1.717a.22.22 0 0 0-.224-.217Z\" clip-rule=\"evenodd\"/>"}, "rust": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M15.5 9.5Q8 13.505.5 9.5l1-1l-1-2l2-.5V4.5h2l.5-2l1.5 1l1.5-2l1.5 2l1.5-1l.5 2h2V6l2 .5l-1 2z\"/><path d=\"M6.5 7.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1m5 0a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1a1 1 0 0 1 1 1M4 11.02c-.67.37-1.5.98-1.5 2.23s1.22 1.22 2 1.25v-2M12 11c.67.37 1.5 1 1.5 2.25s-1.22 1.22-2 1.25v-2\"/></g>"}, "rust-config": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/><g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.252\"><path d=\"M15.5 9.5Q8 13.505.5 9.5l1-1l-1-2l2-.5V4.5h2l.5-2l1.5 1l1.5-2l1.5 2l1.5-1l.5 2h2V6l2 .5l-1 2z\" transform=\"matrix(.79989 0 0 .79796 .1 -.697)\"/><path d=\"M6.5 7.5a1 1 0 1 1-2 0a1 1 0 0 1 2 0m5 0a1 1 0 1 1-2 0a1 1 0 0 1 2 0M4 11.02c-.67.37-1.5.98-1.5 2.23s1.22 1.22 2 1.25v-2M12 11c.168.093.345.201.517.332\" transform=\"matrix(.79989 0 0 .79796 .1 -.697)\"/></g>"}, "salesforce": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.058 4.418c-.445 0-.833.133-1.215.26c-.445-.77-1.279-1.278-2.176-1.278c-.7 0-1.336.318-1.788.763C6.37 3.527 5.6 3.075 4.71 3.075c-1.482 0-2.755 1.216-2.755 2.698c0 .375.134.757.261 1.139A2.34 2.34 0 0 0 1 8.954c0 1.273 1.024 2.367 2.303 2.367c.191 0 .382 0 .51-.063c.254.96 1.215 1.667 2.366 1.667c1.082 0 1.979-.643 2.297-1.54c.325.134.643.26.96.26c.834 0 1.598-.464 1.98-1.15c.197.05.4.05.579.05c1.66 0 3.003-1.33 3.003-3.054a2.93 2.93 0 0 0-2.94-3.073\"/>"}, "sass": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.75 6.38c1.85 1.07 3.35.74 4.83-.2c1.5-.95 2.7-2.78 1.3-4.15c-.7-.68-3.25-.8-5.62.19c-2.36.99-4.59 3.02-4.74 4.11c-.31 2.19 3.15 2.88 3.64 4.23s.28 1.98-.2 2.83c-.5.85-1.96 1.62-2.8.68c-.83-.95 1.67-2.75 2.98-3.25c1.3-.5 3.1-.4 3.69.25c.58.64-.07 1.79-.03 1.79\"/>"}, "scala": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m2.5 2.48l11-.98v3.04l-11 1zm0 5l11-.98v3.04l-11 1zm0 5l11-.98v3.04l-11 1z\"/>"}, "search": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12.5 6.5a6 6 0 0 1-6 6a6 6 0 0 1-6-6a6 6 0 0 1 6-6a6 6 0 0 1 6 6m3 9L11 11\"/>"}, "security": {"body": "<path fill=\"none\" stroke=\"#f0c6c6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.75 5.738h.007m6.33 1.941H14.5l-2.275 4.53l-2.008-2.796\"/><path fill=\"none\" stroke=\"#f0c6c6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.9 6.061l-2.6 5.177l-6.753-3.365a1.898 1.89 0 0 1-.845-2.53l.897-1.805a1.898 1.89 0 0 1 2.548-.842ZM1.5 12.21h2.444a1.3 1.294 0 0 0 1.17-.712L6.05 9.62M1.5 13.503v-2.588\"/>"}, "semantic-release": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 11.5v-7L8 .5l6.5 4v7l-6.5 4zm6.5-2a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M1.75 7Q2.5 8.5 4 8.5m0 4.41c1.13.07 1.93-.33 2.43-1.2m3.83 2.2c.62-.93.67-1.83.17-2.7M14.25 9Q13.5 7.5 12 7.5m0-4.41c-1.13-.07-1.93.33-2.43 1.2m-3.83-2.2c-.62.93-.67 1.83-.17 2.7\"/>"}, "semgrep": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 10.5a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5m5 0a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5m5 0a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5\"/>"}, "semgrep-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 10.5a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5m5 0a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5m5 0a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5\"/>"}, "sentry": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 12.12c0 .26.07.52.22.74c.25.37.68.62 1.15.64H5.1c0-1.54-1.08-2.9-2.64-3.3l1.47-2.43A6.2 6.2 0 0 1 8 13.5h5.06c.48 0 .93-.24 1.2-.62c.26-.38.31-.85.13-1.28L9.26 3.2c-.25-.43-.74-.7-1.26-.7c-.53 0-1.01.27-1.27.7L5.4 5.39a8.91 8.91 0 0 1 5.5 8.12\"/>"}, "serverless": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m6.5 11.5l-1 2h9v-2zm2-4l-1 2h7v-2zm2-4l-1 2h5v-2zm-8 10l1-2h-2v2zm2-4l1-2h-4v2zm2-4l1-2h-6v2z\"/>"}, "shader": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6.41 12c0 .9-.47 1.72-1.23 2.17c-.76.44-1.7.44-2.45 0A2.5 2.5 0 0 1 1.5 12c0-1.38 1.1-2.5 2.46-2.5A2.48 2.48 0 0 1 6.4 12h0Zm3.78-7.23A2.85 2.85 0 0 0 4.6 3.6a2.9 2.9 0 0 0 1.77 3.5c2.19.88 2.7 1.75 2.43 4.13a2.85 2.85 0 0 0 5.58 1.17a2.9 2.9 0 0 0-1.77-3.5c-2.19-.88-2.7-1.76-2.43-4.13h0Z\"/>"}, "sketch": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m.7 6.9l6.67 7.32c.34.37.91.37 1.24 0l6.67-7.32c.26-.29.3-.72.07-1.04l-2.94-4a.83.83 0 0 0-.69-.36H4.28a.83.83 0 0 0-.68.36l-2.95 4A.84.84 0 0 0 .72 6.9zm1.8-.4h11m-1.5 0L9 3M4 6.5L7 3m0 8.5l-3-5m8 0l-3 5m3-5l-.5-3M4 6.5l.5-3\"/>"}, "snowpack": {"body": "<g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.5 13.5h13L8 2z\"/><path d=\"m5 8l1.5 1.5l1.5-2h3\"/></g>"}, "solid": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 11.575Q6.05 14.5 8 14.5c1.625 0 2.6-.975 2.6-2.275S9.625 9.95 8 9.95q-1.95 0-6.5 1.625\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.45 8.975Q8 7.35 9.95 7.35c1.625 0 2.6.975 2.6 2.275c0 .48-.133.915-.382 1.274l-1.874 2.486m4.206-8.96C11.9 2.475 9.3 1.5 8 1.5c-1.326 0-1.702.301-2.222 1.004M1.5 11.575l1.95-2.6m11.05-4.55l-1.95 2.6m-6.772-4.52l-1.92 2.411\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.02 8.43c-.981-.31-1.57-.961-1.57-2.055c0-1.625.975-2.275 2.6-2.275c1.097 0 3.307.694 5.329 2.083a75 75 0 0 1 1.171.842l-1.496.51\"/>"}, "solidity": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3 11.5l2.5 4l2.5-4l2.5 4l2.5-4l-2.5-4l-2.5 4m2.5 4h-5m7.5-4H3m10-7l-2.5-4l-2.5 4l-2.5-4l-2.5 4l2.5 4l2.5-4M5.5.5h5M3 4.5h10\"/>"}, "sonar-cloud": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 7.38a4 4 0 1 0 0 6.24\"/><path d=\"M6.5 10.5a4 4 0 1 0 5.49-3.71\"/><path d=\"M9.5 10.21A4 4 0 1 0 4 6.8\"/></g>"}, "spwn": {"body": "<g fill=\"none\"><path stroke=\"#a6da95\" d=\"M5.5 6a4.5 4.5 0 1 0 4.5 4.5\"/><path stroke=\"#f5bde6\" d=\"M10 11a5 5 0 1 0-5-5\"/><path stroke=\"#cad3f5\" d=\"m8.803 10.096l-.353-.353l-.354.353l-1.914 1.914a.5.5 0 0 1-.707 0L4.06 10.596a.5.5 0 0 1 0-.707l1.914-1.914l.353-.354l-.353-.353l-.914-.914a.5.5 0 0 1 .353-.854H10a.5.5 0 0 1 .5.5v4.586a.5.5 0 0 1-.854.353z\"/></g>"}, "stackblitz": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m3.5 9.5l7.04-9l-2.04 6h4l-7.04 9l2.04-6z\"/>"}, "stata": {"body": "<rect width=\"13\" height=\"13\" x=\"1.5\" y=\"1.5\" fill=\"none\" stroke=\"#7dc4e4\" rx=\".722\" ry=\".722\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.5 12.5h6v-9h3v6h-9v-3h9v-3h-6v9zv-3\"/>"}, "stencil": {"body": "<path fill=\"none\" stroke=\"#b7bdf8\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.5 6.5H4l-2.5 3H12zm-8 5h5l-2.5 3H4zM8 1.5h5l-2.5 3h-5z\"/>"}, "stitches": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m6.23 14.25l8.08-4.66M4.09 13.18l6.9-3.99L8.59 5\"/><path d=\"M7.54 11.2L5.1 6.96l6.96-4.01\"/><path d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8M1.66 6.64L10 1.83M6.85 6.01l2.4 4.14\"/></g>"}, "storybook": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M6.5 10.1c.45 1.05 1.23 1.4 2.46 1.4h-.21c1.35 0 2.25-.68 2.25-1.7c0-.84-.62-1.26-1.61-1.64L7.91 7.6c-.86-.33-1.41-1-1.41-1.73c0-.68.78-1.26 1.67-1.33l.36-.03c1.14-.1 2.24.53 2.47 1.39\"/><path stroke=\"#f5bde6\" d=\"M3.5 2.5L4 14l9.5.5v-13zm8-.75v1.5\"/></g>"}, "storybook-svelte": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M6.5 10.1c.45 1.05 1.23 1.4 2.46 1.4h-.21c1.35 0 2.25-.68 2.25-1.7c0-.84-.62-1.26-1.61-1.64L7.91 7.6c-.86-.33-1.41-1-1.41-1.73c0-.68.78-1.26 1.67-1.33l.36-.03c1.14-.1 2.24.53 2.47 1.39\"/><path stroke=\"#f5a97f\" d=\"M3.5 2.5L4 14l9.5.5v-13zm8-.75v1.5\"/></g>"}, "storybook-vue": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M6.5 10.1c.45 1.05 1.23 1.4 2.46 1.4h-.21c1.35 0 2.25-.68 2.25-1.7c0-.84-.62-1.26-1.61-1.64L7.91 7.6c-.86-.33-1.41-1-1.41-1.73c0-.68.78-1.26 1.67-1.33l.36-.03c1.14-.1 2.24.53 2.47 1.39\"/><path stroke=\"#a6da95\" d=\"M3.5 2.5L4 14l9.5.5v-13zm8-.75v1.5\"/></g>"}, "stylelint": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11.5 3.48L12 1.5h1.5l2 2l-1.5 1L15 6l-7 9.5l2.72-9.32M6.5 3.5l-2-1v4l2-1\"/><path d=\"m9.5 3.5l2-1v4l-2-1m-5-2.02L4 1.5H2.5l-2 2l1.5 1L1 6l7 9.5l-2.72-9.33M8.5 8a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path d=\"M6.5 3.5h3v2h-3z\"/></g>"}, "stylelint-ignore": {"body": "<g fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11.5 3.48L12 1.5h1.5l2 2l-1.5 1L15 6l-7 9.5l2.72-9.32M6.5 3.5l-2-1v4l2-1\"/><path d=\"m9.5 3.5l2-1v4l-2-1m-5-2.02L4 1.5H2.5l-2 2l1.5 1L1 6l7 9.5l-2.72-9.33M8.5 8a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path d=\"M6.5 3.5h3v2h-3z\"/></g>"}, "sublime": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m13.5 11.5l-11 3v-3l6.29-1.71M2.5 4.5l11-3v3L7.21 6.21m6.29 5.29v-3l-11-4v3z\"/>"}, "super-collider": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 4.616v6.952l5.116 2.781V7.481L3.138 5.638V9.91l1.89.971V9.122l-.455-.217m3.85 5.595V7.698L14.5 5.822v6.785l-4.086 1.24V9.524l2.161-.57v1.76l-.574.1m2.195-6.55L8.794 1.5L2.496 3.31l4.998 2.646l4.423-1.223l-3.275-1.759l-2.28.704l1.25.754l.692-.218\"/>"}, "svelte": {"body": "<g fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12.86 6.72s1.39-1.98.08-3.87C11.286.763 9.44 1.6 9.44 1.6S6.15 3.35 4.33 4.59c-1.4 1-2.24 2.26-1.03 4.37c1.22 2.1 4.58 1.21 4.58 1.21\"/><path d=\"M3.14 9.28s-1.39 1.98-.08 3.87c1.31 1.9 3.5 1.24 3.5 1.24s3.29-1.74 5.11-2.98c1.4-1 2.24-2.26 1.03-4.37c-1.22-2.1-4.58-1.21-4.58-1.21M6.3 6.96l4.14-2.56m-4.92 7.25L9.66 9.1\"/></g>"}, "svelte-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#f5a97f\" d=\"M9.92 5.32s1.26-1.83.07-3.58C8.79 0 6.8.6 6.8.6s-3 1.6-4.65 2.75C.88 4.28.13 5.44 1.23 7.39C2.33 9.33 5.39 8.5 5.39 8.5m-4.3-.82S-.19 9.51 1 11.26C2.21 13 4.2 12.4 4.2 12.4s1.76-.94 3.32-1.9m2.79-3a3.36 3.36 0 0 0-.53-1.89C8.67 3.67 5.61 4.5 5.61 4.5M3.96 5.54l3.76-2.36M3.25 9.87L7 7.5\"/><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/></g>"}, "svg": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m4.54 10l6.92-4m-6.92 4a1.5 1.5 0 1 0-2.6 1.5a1.5 1.5 0 0 0 2.6-1.5M8 4v8m0-8a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M4.54 6l6.92 4M4.54 6a1.5 1.5 0 1 0-2.6-1.5A1.5 1.5 0 0 0 4.54 6M8 12a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3m3.46-2a1.5 1.5 0 1 0 2.6 1.5a1.5 1.5 0 0 0-2.6-1.5m0-4a1.5 1.5 0 1 0 2.6-1.5a1.5 1.5 0 0 0-2.6 1.5\"/>"}, "swift": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.34 10.2c.34-1.08 1.1-5.07-4.45-8.62a.48.48 0 0 0-.6.07a.44.44 0 0 0-.02.6c.03.02 2.07 2.5 1.34 5.34c-1.26-.86-6.24-4.81-6.24-4.81L7.25 7.5L1.9 4.05S5.68 8.7 8 10.45c-1.12.4-3.56.82-6.78-1.18a.48.48 0 0 0-.58.06a.44.44 0 0 0-.08.56c.11.18 2.7 4.36 8.14 4.36c1.5 0 2.37-.42 3.08-.77c.43-.2.77-.37 1.14-.37c.93 0 1.54.92 1.54.93c.1.14.27.22.44.21a.46.46 0 0 0 .4-.28c.67-1.55-.49-3.2-.96-3.78h0Z\"/>"}, "symlink": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.5 6.5v6a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2m0-9c0-1.1.9-2 2-2h4.01m-.01 0l5 5h-4a1 1 0 0 1-1-1z\"/><path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.5 14.503V10.79c0-1.539 1.175-2.786 2.625-2.786H7\"/><path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 10.5L7.125 8L4.5 5.5\"/>"}, "tailwind": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 2.5q-3 0-3.75 3.33C5 4.73 5.88 4.31 6.87 4.58c.58.16.98.62 1.43 1.13c.74.83 1.6 1.79 3.45 1.79q3 0 3.75-3.33c-.75 1.1-1.63 1.52-2.63 1.25c-.57-.16-.97-.62-1.42-1.13C10.7 3.46 9.85 2.5 8 2.5m-3.75 6q-3 0-3.75 3.33c.75-1.1 1.63-1.52 2.63-1.25c.57.16.97.62 1.42 1.13c.74.83 1.6 1.79 3.45 1.79q3 0 3.75-3.33c-.75 1.1-1.63 1.52-2.62 1.25c-.58-.16-.98-.62-1.43-1.13c-.74-.83-1.6-1.79-3.45-1.79\"/>"}, "taskfile": {"body": "<path fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.5 11.752L8 15.5l-6.5-3.752l.002-7.5L8 .5l6.5 3.752zM1.5 4.25L8 8m6.5-3.75L8 8m.003 0v7.5\"/>"}, "tauri": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#eed49f\" d=\"M4.73 4.02a4.64 4.64 0 1 1 5.55 6.56\"/><path stroke=\"#91d7e3\" d=\"M7.5 10a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path stroke=\"#91d7e3\" d=\"M11.26 12a4.64 4.64 0 1 1-5.63-6.55\"/><path stroke=\"#eed49f\" d=\"M9.5 6a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "tauri-ignore": {"body": "<g fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4.73 4.02a4.64 4.64 0 1 1 5.55 6.56M7.5 10a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/><path d=\"M11.26 12a4.64 4.64 0 1 1-5.63-6.55M9.5 6a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "terraform": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m1.5 6l8 4.25l4-2.25m-12-2V1.5l8 4.25l4-2.25V8m-4-2.25v8.75M5.53 3.82L5.5 12.5l4 2\"/>"}, "text": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M13.5 6.5v6a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h4.01\"/><path d=\"m8.5 1.5l5 5h-4a1 1 0 0 1-1-1zm-3 10h5m-5-3h5m-5-3h1\"/></g>"}, "todo": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 8A6.5 6.5 0 0 1 8 14.5A6.5 6.5 0 0 1 1.5 8A6.5 6.5 0 0 1 8 1.5A6.5 6.5 0 0 1 14.5 8\"/><path d=\"m4.5 7.5l2.5 3l4.5-5\"/></g>"}, "toml": {"body": "<path fill=\"none\" stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.5 1.5h-2v13h2m9-13h2v13h-2m-8-11h7v3h-2v6h-3v-6h-2z\"/>"}, "turbo": {"body": "<g fill=\"none\"><circle cx=\"8\" cy=\"8\" r=\"4.5\" stroke=\"#cad3f5\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.58 14.3a6.5 6.5 0 0 1-4.55-.52\"/><path stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.52 1.52a6.5 6.5 0 0 1 5.84 5.12\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.91 10.7a6.5 6.5 0 0 1-2.78 3\"/><path stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.38 6.76a6.5 6.5 0 0 1-.42 3.83\"/><path stroke=\"#ee99a0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.63 12.8a6.5 6.5 0 0 1-2.1-4.14\"/></g>"}, "twig": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M.502 7C.876 6.703 1.87 6.356 3 6.513c.724.118 1.466.309 2.507 1.058c.52.374.896.867 1.493 1.429m1.985 6.499H4.99c.057-.826.025-1.656-.093-2.475A8 8 0 0 0 3.579 9.19C2.342 7.549.404 7.303.502 7m8.483 8.5L13 15.499c-.143-1.147-.493-2.3-.528-3.455c-.05-1.719-.205-4.044 1.621-4.081c.907.08 1.294.836 1.4 1.037c-.04-.399-.33-1.94-1.493-2.415c-1.393-.57-3.333 1.648-3.517 1.967M2 2c.14.214 1.216-.506 2.263-.267C5.367 1.986 6.553 3.656 7 9m3.483-.448A10.26 10.26 0 0 0 8.679 3.66C8.127 2.886 6.215.19 4.1.53C2.74.75 2.123 1.542 2 2\"/><path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8 11a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5m3 0a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/>"}, "twine": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M2.5 14.5v-12h4v2.29\"/><path stroke=\"#a6da95\" d=\"M6.5 13A5.5 5.5 0 0 1 12 7.5h1.5v-4H11A8.5 8.5 0 0 0 2.5 12v2.5h4z\"/></g>"}, "typescript": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 1.5h8A2.5 2.5 0 0 1 14.5 4v8a2.5 2.5 0 0 1-2.5 2.5H4A2.5 2.5 0 0 1 1.5 12V4A2.5 2.5 0 0 1 4 1.5\"/><path d=\"M12.5 8.75c0-.69-.54-1.25-1.2-1.25h-.6c-.66 0-1.2.56-1.2 1.25S10.04 10 10.7 10h.6c.66 0 1.2.56 1.2 1.25s-.54 1.25-1.2 1.25h-.6c-.66 0-1.2-.56-1.2-1.25m-3-3.75v5M5 7.5h3\"/></g>"}, "typescript-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/><path stroke=\"#8aadf4\" d=\"M6.5 11.5h-4a2 2 0 0 1-2-2v-7c0-1.1.9-2 2-2h7.97c1.1 0 2 .92 2 2V7\"/><path stroke=\"#8aadf4\" d=\"M10.5 4.5C10.17 4.17 9.67 4 9 4c-1 0-1.5.5-1.5 1S8 6 9 6s1.5.5 1.5 1S10 8 9 8c-.67 0-1.17-.17-1.5-.5M4 4v4M2.5 4h3\"/></g>"}, "typescript-def": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"M12.5 4.5h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2V10\"/><path stroke=\"#8aadf4\" d=\"M13.5 9c-.33-.33-.83-.5-1.5-.5c-1 0-1.5.5-1.5 1s.5 1 1.5 1s1.5.5 1.5 1s-.5 1-1.5 1c-.67 0-1.17-.17-1.5-.5M8 8.5v4m-1.5-4h3\"/><path stroke=\"#7dc4e4\" d=\"M2.04 7.88L.5 3.02c-.05-.15.06-.31.23-.37L7.54.52c.1-.04.21-.02.3.04l2.63 1.84c.**********.1.3L9.44 5.72a.34.34 0 0 1-.22.2l-6.8 2.13a.31.31 0 0 1-.4-.17Zm5.13-3.23a1.21 1.21 0 1 0 .62-2.35a1.21 1.21 0 0 0-.62 2.35\"/></g>"}, "typescript-react": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 11.3c4.14 0 7.5-1.28 7.5-2.86S12.14 5.58 8 5.58S.5 6.86.5 8.44s3.36 2.87 7.5 2.87Z\"/><path d=\"M5.52 9.87c2.07 3.6 4.86 5.86 6.23 5.07c1.37-.8.8-4.34-1.27-7.93S5.62 1.16 4.25 1.95s-.8 4.34 1.27 7.92\"/><path d=\"M5.52 7.01c-2.07 3.59-2.64 7.14-1.27 7.93s4.16-1.48 6.23-5.07c2.07-3.58 2.64-7.13 1.27-7.92c-1.37-.8-4.16 1.47-6.23 5.06\"/><path d=\"M8.5 8.44a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5\"/></g>"}, "typescript-test": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.5 12c-.33-.33-.83-.5-1.5-.5c-1 0-1.5.5-1.5 1s.5 1 1.5 1s1.5.5 1.5 1s-.5 1-1.5 1c-.67 0-1.17-.17-1.5-.5m-3-3.5v4m-1.5-4h3m-.28-9.75l-8.49 8.48a2.5 2.5 0 1 0 3.54 3.54l.77-.77m3.59-3.59l.59-.59l1.17-1.18l2.36-2.36M9.5.5l6 6m-3.5 1H4.98\"/>"}, "typst": {"body": "<path fill=\"none\" stroke=\"#8bd5ca\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M8.343 11.167q0 .904.252 1.214t.915.31q.687 0 1.763-.715l.457.786Q9.716 14.5 8.412 14.5t-2.06-.643q-.754-.666-.754-2.333V5.286H4.453l-.183-.881l1.328-.429V2.81L8.343 1.5v2.619l2.7-.214l-.251 1.548l-2.449-.096z\"/>"}, "unity": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m9.5 1l5 3v5.5m-1 3l-5.5 3l-5.5-3m-1-3V3.83L6.5 1m-5 3L8 8v7.5M14.5 4L8 8\"/>"}, "unocss": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M9 12a3 3 0 1 1 6 0a3 3 0 0 1-6 0\"/><path stroke=\"#cad3f5\" d=\"M7 12c0 1.75-1.25 3-3 3s-3-1.25-3-3V9h6zm2-8c0-1.75 1.25-3 3-3s3 1.25 3 3v3H9Z\"/></g>"}, "url": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m5.5 10.5l5-5M3.73 6.61L2.67 7.67a4 4 0 1 0 5.66 5.66l1.06-1.06m2.88-2.88l1.06-1.06a4 4 0 1 0-5.66-5.66L6.61 3.73\"/>"}, "uv": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-linejoin=\"round\" d=\"M1 1h5.8v9.1h2.8V1H15v14h-2v-1.4h-1.5c-.2.8-.9 1.4-1.8 1.4H3.2C1.9 15 1 14 1 12.9z\"/>"}, "v": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m1.5 1.5l4 .5L10 14.5H6z\"/><path d=\"M8 8.95L10.5 2l4-.5l-4.5 13\"/></g>"}, "vanilla-extract": {"body": "<path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5.378 6.164A2.536 2.587 0 0 0 3.54 8.65m1.838-2.486c1.255-.366 3.914-.6 4.423-2.06m-4.423 2.06C5.378 3.45 8 4.1 8 1.5c1.304 0 2.232 1.371 1.801 2.605m0 0c1.037-.091 1.732 1.125 1.184 1.996m0 0c-.174.277-.461.503-.755.676m.755-.676c1.247.296 1.475 1.44 1.475 2.549\"/><path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.549 9.917c0-1.69 2.548-1.69 2.548 0c0 .885-.48 1.643-.855 2.411c-.513 1.057-.77 1.585-1.233 1.879c-.462.293-1.036.293-2.186.293H7.177c-1.15 0-1.724 0-2.186-.293c-.462-.294-.72-.822-1.233-1.879c-.374-.768-.855-1.526-.855-2.411c0-1.69 2.548-1.69 2.548 0c0-1.69 2.549-1.69 2.549 0c0-1.69 2.549-1.69 2.549 0\"/>"}, "vento": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 1.5h4.469l5.687 13H7.594Z\"/><path fill=\"none\" stroke=\"#f5bde6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.031 1.5H14.5l-2.844 13H7.594Z\"/><path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m8.812 8l2.844 6.5H7.594Z\"/>"}, "vercel": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 13.5h13L8 2z\"/>"}, "vercel-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 13.5h13L8 2z\"/>"}, "verilog": {"body": "<g fill=\"none\" stroke=\"#cad3f5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4.5 2.5h7a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-7c0-1.1.9-2 2-2\"/><path d=\"M5.5 5.5h5v5h-5zm8.5 0h1.5M14 8h1.5M14 10.5h1.5M.5 5H2M.5 7.5H2M.5 10H2m3.5-8V.5M8 2V.5M10.5 2V.5m-5 15V14M8 15.5V14m2.5 1.5V14\"/></g>"}, "vhs": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2 3.5c-.828 0-1.5.743-1.5 1.556v5.888c0 .813.672 1.556 1.5 1.556h12c.828 0 1.5-.743 1.5-1.556V5.056c0-.813-.672-1.556-1.5-1.556Zm.405 3H4v3H2.405A3 3 0 0 1 2 8c0-.515.142-1.051.405-1.5M6 6.5h4v3H6Zm6 0h1.595c.262.449.405.985.405 1.5s-.143 1.051-.405 1.5H12Z\"/>"}, "video": {"body": "<g fill=\"none\" stroke=\"#7dc4e4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3 2.5h10c.83 0 1.5.67 1.5 1.5v9c0 .83-.67 1.5-1.5 1.5H3A1.5 1.5 0 0 1 1.5 13V4c0-.83.67-1.5 1.5-1.5m-1.5 3h13\"/><path d=\"m3.5 5.5l2-3m1.5 3l2-3m1.5 3l2-3M6.5 8v4l4-2z\"/></g>"}, "vim": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 2.5h4m-3 0v11h3l8-11H11l-6.5 9v-9m10 0H9\"/>"}, "visual-studio": {"body": "<path fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 11L3 4.5h-.5l-1 1V6l9 8.5l4-2v-9l-4-2v13m0-13L5.3 6.41M3.53 8.08L1.5 10v.5l.98 1.1l.52-.1l2.17-1.88m1.91-1.66L10.5 5\"/>"}, "vital": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#cad3f5\" d=\"M8 15S5 6.5 1 4c1 .2 3.3.5 4.5.5c1 .5 2.5 3 2.5 4c0-1 1.5-3.5 2.5-4A31 31 0 0 0 15 4c-4 2.5-7 11-7 11\"/><path stroke=\"#c6a0f6\" d=\"M3.4 2.6S5 1 7.7 1S12 2.6 12 2.6m-10.9 4S.5 8.7 2 11a6 6 0 0 0 3.4 3m4.6 0s2.2-.6 3.5-3c1.4-2.2.8-4.5.8-4.5\"/></g>"}, "vite": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#c6a0f6\" d=\"m11 5.5l3.5-1l-6.5 11l-6.5-11l3.5 1\"/><path stroke=\"#eed49f\" d=\"m6 1.5l-.5 5l2-1l-1 3L8 8v3l4-7.5l-2 .5L11.5.5Z\"/></g>"}, "vitest": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#a6da95\" d=\"m14.5 8.5l-6.5 6l-6.5-6\"/><path stroke=\"#eed49f\" d=\"M7.5 11.5L8 8L5 7l4.5-5.5L9 5l3 1z\"/></g>"}, "vs-codium": {"body": "<g fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1.63 7.61c-.08 2.45.7 3.91 2.37 4.39a5.53 5.53 0 0 1 3.5 2.5\"/><path d=\"M3.5 3.5c1.19.76 1.78 1.53 1.78 2.32S4.2 8.52 4.56 10c.45 1.82 2.94 2 2.94 4.5\"/><path d=\"M7.5 7.5c-.47.08-.96.24-1.47.5c-.76.39-1.47 1.54-1.47 2\"/><path d=\"M7.5 1.5c1.4 2.02 2.1 3.86 2.1 5.53c0 1.17-.29 2.3-.88 3.27c-.57.93-1.22 1.16-1.22 4.2m5-12c-1 0-1.51.5-2.01 1c-.34.33-.67 1-.99 2m4 1c.23 1.46-.1 2.63-1 3.5c-.9.88-1.63.45-3 1.5c-.48.37-1.15 1.37-2 3m4.5-4h2.5\"/></g>"}, "vscode": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 11L3 4.5h-.5l-1 1V6l9 8.5l4-2v-9l-4-2v13m0-13L5.3 6.41M3.53 8.08L1.5 10v.5l.98 1.1l.52-.1l2.17-1.88m1.91-1.66L10.5 5\"/>"}, "vscode-ignore": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10.5 11L3 4.5h-.5l-1 1V6l9 8.5l4-2v-9l-4-2v13m0-13L5.3 6.41M3.53 8.08L1.5 10v.5l.98 1.1l.52-.1l2.17-1.88m1.91-1.66L10.5 5\"/>"}, "vue": {"body": "<g fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 1.5h5.44L8 4.56L9.56 1.5H15l-6.99 13z\"/><path d=\"M12.05 1.73L8 9.28L3.95 1.73\"/></g>"}, "vue-config": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8087a2\" d=\"M11.5 13.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m1.75-4l1.75 3l-1.75 3h-3.5L8 12.5l1.75-3z\"/><path stroke=\"#a6da95\" d=\"M.5.5h4.67L6.5 3.09L7.83.5h4.67l-6 11zm9.47.2L6.5 7.08L3.03.7\"/></g>"}, "web-assembly": {"body": "<g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m9.5 12.5l1.5-4l1.5 4m-2.5-1h2m-8.5-3l1 4l1.5-4l1.5 4l1-4\"/><path d=\"M10.5 1.5h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2v0\"/><path d=\"M5.5 1.5c0 1.38.5 3 2.5 3s2.5-1.62 2.5-3\"/></g>"}, "webpack": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#91d7e3\" d=\"m4.5 10.02l-3 1.73M11.47 10l3.03 1.75M8 4V.5\"/><path stroke=\"#8aadf4\" d=\"M11.5 10L8 12l-3.5-2V6L8 4l3.5 2z\"/><path stroke=\"#91d7e3\" d=\"M14.5 11.75L8 15.5l-6.5-3.75v-7.5L8 .5l6.5 3.75zm-13-7.5L8 8m6.5-3.75L8 8m0 0v7.5\"/></g>"}, "windi": {"body": "<path fill=\"none\" stroke=\"#91d7e3\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 5.5H6a2 2 0 1 0-2-2m-2.5 5H12A2.5 2.5 0 1 0 9.5 6m-2 7A1.5 1.5 0 1 0 9 11.5H5.5m-4 0h2\"/>"}, "workflow": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.5 1.5h2a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-2c0-1.1.9-2 2-2m7 7h2a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-2c0-1.1.9-2 2-2m-6-1V10q0 1.5 1.5 1.5h2.5\"/>"}, "wxt": {"body": "<path fill=\"none\" stroke=\"#a6da95\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M1.5 5.45c0-1 .81-1.81 1.81-1.81h1.81v-.33a1.81 1.81 0 0 1 3.62 0v.33h1.81c1 0 1.81.81 1.81 1.81v1.81h.33a1.81 1.81 0 1 1 0 3.62h-.33v1.81c0 1-.81 1.81-1.81 1.81H8.74v-.33a1.81 1.81 0 1 0-3.62 0v.33H1.5v-3.62h.33a1.81 1.81 0 0 0 0-3.62H1.5Z\"/>"}, "xaml": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#8aadf4\" d=\"m10.25 4.5l2.25 4l-2.25 4h-4.5l-2.25-4l2.25-4z\"/><path stroke=\"#cad3f5\" d=\"m2.5 12.5l-2-4l2-4\"/><path stroke=\"#8aadf4\" d=\"m6 12l2-3.5h4m-4 0L6 5\"/><path stroke=\"#cad3f5\" d=\"m13.5 4.5l2 4l-2 4\"/></g>"}, "xcode": {"body": "<path fill=\"none\" stroke=\"#8aadf4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M13.5 8v3.714a2.78 2.78 0 0 1-2.786 2.786H8.5m-4.5 0h-.714A2.78 2.78 0 0 1 .5 11.714V4.286A2.78 2.78 0 0 1 3.286 1.5H6M3.5 12L8 4M6 4l1.477 2.625m2.742 4.875l.281.5M3 9.5h2.977\"/><g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.884\"><path stroke=\"#cad3f5\" d=\"M14 12L.34 25.703c-1.443 1.447-3.267-.728-1.996-2.003L12 10\" transform=\"matrix(.50714 -.15926 .15877 .5054 2.596 1.629)\"/><path stroke=\"#8087a2\" d=\"m21.5 11.5l-1.914-1.914A2 2 0 0 1 19 8.172V7l-2.26-2.26a6 6 0 0 0-4.202-1.756L9 2.96l.92.82A6.18 6.18 0 0 1 12 8.4V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5m-.408.569l3.854-4.031\" transform=\"matrix(.50714 -.15926 .15877 .5054 2.596 1.629)\"/></g>"}, "xmake": {"body": "<g fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"#7dc4e4\" d=\"M14.04 10.42a6.45 6.45 0 0 0-.56-5.92a6.5 6.5 0 0 0-.73-.94L8.99 6.18z\"/><path stroke=\"#8bd5ca\" d=\"M7.35 7.32L2.2 10.94A6.5 6.5 0 0 0 13 12.15z\"/><path stroke=\"#a6da95\" d=\"M3.04 3.8a6.47 6.47 0 0 0-1.47 5.14L5.72 6z\"/></g>"}, "xml": {"body": "<path fill=\"none\" stroke=\"#f5a97f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M4.5 4.5L1 8l3.5 3.5m7-7L15 8l-3.5 3.5M9.5 2l-3 12\"/>"}, "yaml": {"body": "<path fill=\"none\" stroke=\"#ed8796\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.5 1.5h3l3 4l3-4h3l-9 13h-3L7 8z\"/>"}, "yarn": {"body": "<g fill=\"none\" fill-rule=\"evenodd\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-miterlimit=\"1.5\" stroke-width=\"6.22\"><path d=\"M11.9 65.033s-1.241-16.91 11.549-24.936c0 0-8.286-12.228 0-19.036s12.645-9.145 18.736-8.634c0 0 7.247-20.251 14.643 0c0 0 9.061-7.776 7.844 17.247c-.323 6.646-4.717 16.289-7.844 20.326c0 0 8.881 6.323 8.881 25.924c0 0 14.526-7.698 18.663-8.162c4.136-.463 7.724-.011 8.572 2.799s1.245 4.744-1.062 6.558c-2.308 1.813-10.589 4.258-19.484 10.194c-8.894 5.936-11.77 4.105-14.208 5.576c-2.438 1.472-16.058 7.342-33.033.927c0 0-15.323 4.247-14.195-6.503c0 0-10.684-12.422.938-22.28\" transform=\"matrix(.1608 0 0 .16076 -.053 -.054)\"/><path d=\"M27.469 94.285c-1.525-1.407.321-8.703.321-8.703s-1.252 4.723-3.116 7.525\" transform=\"matrix(.1608 0 0 .16076 -.053 -.054)\"/></g>"}, "yarn-lock": {"body": "<path fill=\"none\" stroke=\"#8087a2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15 11.5c.27 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1-.5-.5v-3c0-.28.22-.5.5-.5zm-4 0V10a1.5 1.5 0 0 1 3 0v1.5\"/><g fill=\"none\" stroke=\"#c6a0f6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"7.179\"><path d=\"M58.19 92.889c-2.438 1.472-16.058 7.342-33.033.927c0 0-15.323 4.247-14.195-6.503c0 0-10.684-12.422.938-22.28c0 0-1.241-16.91 11.549-24.936c0 0-8.286-12.228 0-19.036s12.645-9.145 18.736-8.634c0 0 7.247-20.251 14.643 0c0 0 9.061-7.776 7.844 17.247c-.323 6.646-4.717 16.289-7.844 20.326c0 0 2.22 1.58 4.44 5.612c1.11 2.015 2.22 4.643 3.053 7.992a45 45 0 0 1 1.006 5.578\" transform=\"matrix(.13927 0 0 .13933 -.413 .02)\"/><path d=\"M27.469 94.285c-1.525-1.407.321-8.703.321-8.703s-1.252 4.723-3.116 7.525\" transform=\"matrix(.13927 0 0 .13933 -.413 .02)\"/></g>"}, "zap": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.85 9.301a.644.65 0 0 1-.502-1.06L8.72 1.605a.322.325 0 0 1 .554.3L8.039 5.82a.644.65 0 0 0 .605.878h4.506a.644.65 0 0 1 .502 1.06L7.28 14.395a.322.325 0 0 1-.554-.3l1.236-3.916a.644.65 0 0 0-.605-.878Z\"/>"}, "zig": {"body": "<path fill=\"none\" stroke=\"#eed49f\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M10 3.5H6l-1.5 2h4l-7 9l4.5-2h4l1.5-2h-4l7-9z\"/>"}, "zip": {"body": "<path fill=\"none\" stroke=\"#cad3f5\" stroke-linejoin=\"round\" d=\"M5.5 10v1m1-2v1m-1-2v1m1-2v1m-1-2v1m1-2v1m-1-2v1m0-3v1m1 0v1m7 2.5v6a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h4.01m-.01 0l5 5h-4a1 1 0 0 1-1-1z\"/>"}}, "aliases": {"maven": {"parent": "apache"}}}