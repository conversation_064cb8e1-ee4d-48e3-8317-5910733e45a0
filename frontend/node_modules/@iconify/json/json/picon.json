{"prefix": "picon", "info": {"name": "Pico-icon", "total": 824, "author": {"name": "Picon Contributors", "url": "https://github.com/yne/picon"}, "license": {"title": "Open Font License", "spdx": "OFL-1.1", "url": "https://github.com/yne/picon/blob/master/OFL.txt"}, "samples": ["bookmark", "rewind", "folder", "thunderbolt", "message", "east"], "category": "UI Other / Mixed Grid", "tags": ["Precise Shapes"], "palette": false}, "lastModified": **********, "icons": {"2g": {"body": "<path fill=\"currentColor\" d=\"M0 2V1h3v3H1v1h2v1H0V3h2V2m6 0H5v3h2V4H6V3h2v3H4V1h4\"/>"}, "3g": {"body": "<path fill=\"currentColor\" d=\"M0 2V1h3v5H0V5h2V4H0V3h2V2m6 0H5v3h2V4H6V3h2v3H4V1h4\"/>"}, "4chan": {"body": "<path fill=\"currentColor\" d=\"M5 3V1l3-1l-1 2l1 1M6 7L5 8V5h2l1 3M2 7L0 8l1-3h2v3M0 3l1-1l-1-2l3 1v2\"/>"}, "4g": {"body": "<path fill=\"currentColor\" d=\"M8 2H5v3h2V4H6V3h2v3H4V1h4M2 4H0V1h1v2h1V2h1v4H2\"/>"}, "4k": {"body": "<path fill=\"currentColor\" d=\"M4 6V1h1v2h2V1H6v5h1V4H5v2M0 4V1h1v2h1V1h1v5H2V4\"/>"}, "5g": {"body": "<path fill=\"currentColor\" d=\"M8 2H5v3h2V4H6V3h2v3H4V1h4M2 4H0V1h3v1H1v1h2v3H0V5h2\"/>"}, "8k": {"body": "<path fill=\"currentColor\" d=\"M4 6V1h1v2h2V1H6v5h1V4H5v2M1 2v1h1V2M1 4v1h1V4M0 6V1h3v5\"/>"}, "abacus": {"body": "<path fill=\"currentColor\" d=\"M0 0h1v1h6V0h1v8H7V7H1v1H0m2-3h1v3H2M1 6h6V2H1m4-2h1v3H5M3 0h1v3H3\"/>"}, "acorn": {"body": "<path fill=\"currentColor\" d=\"M7 3q0 4-3.5 5Q0 7 0 3m0-2h3V0h1v1h3v1H0\"/>"}, "ad": {"body": "<path fill=\"currentColor\" d=\"M1 6V5h1v1h1C3 .5 0 .5 0 6m4 0c4 0 4-4 0-4m1 1h1v2H5M1 3h1v1H1\"/>"}, "add": {"body": "<path fill=\"currentColor\" d=\"M8 6H2V4h6M4 8V2h2v6\"/>"}, "address": {"body": "<path fill=\"currentColor\" d=\"M1 7V1h5v6M0 0v8h7V6l1-1l-1-1l1-1l-1-1l1-1l-1-1M3 3L2 4l1 1.5h1L5 4L4 3m1 0L4 5H3L2 3l1-1h2\"/>"}, "aflat": {"body": "<path fill=\"currentColor\" d=\"M2 7q-3 0-1-1c-4-7 10-7 6 0q2 1-1 1M3 2q1 2 2 0m1 1Q4 4 6 5M5 6Q4 4 3 6M2 5q2-1 0-2\"/>"}, "africa": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m4 3h1l2-4H6L5 1H3q-4 2 0 3m4 2V5L6 7m0-6H5l1 1.5L7 2\"/>"}, "airbag": {"body": "<path fill=\"currentColor\" d=\"M2 4C-.5 4-.5 0 2 0s2.5 4 0 4m0 3L1 8L0 7l2-2l2 2m2 1L3 5h1l3 3M5 5V3q1-2 2 0v4M6 2L5 1l1-1l1 1\"/>"}, "airconditioner": {"body": "<path fill=\"currentColor\" d=\"M0 0h8v4H0m1-1h6V2H1m2 2v2c0 4-5 1-2 0v1h1V5m4 0L5 6c0 4 5 1 2 0v1H6\"/>"}, "aircraft": {"body": "<path fill=\"currentColor\" d=\"m2 1l4-1v1L2 0m6 2q0 2-2 2H2Q0 4 0 2m2 5h4L5 8H3m0-4V1h2v3L4 8\"/>"}, "airhorn": {"body": "<path fill=\"currentColor\" d=\"M0 8V4l1-1V1h2v2l1 1v4M1 2q6 0 7-2v5Q7 3 1 3\"/>"}, "airplay": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v5H5L4 5L3 6H0m2 2l2-2l2 2M1 2v3h6V2\"/>"}, "airport": {"body": "<path fill=\"currentColor\" d=\"M3 6v2H2L0 6V5h2l5-5q1 0 1 1M0 2l1-1l5 1l1 5l-1 1l-2-4\"/>"}, "alarm": {"body": "<path fill=\"currentColor\" d=\"M1 4c0-4 6-4 6 0S1 8 1 4m7-1.5Q8 0 5.5 0M0 2.5Q0 0 2.5 0M4 4l3 3l-1 1l-2-2l-2 2l-1-1\"/>"}, "album": {"body": "<path fill=\"currentColor\" d=\"M1 4c0-4 6-4 6 0S1 8 1 4m3 1l1-1l-1-1l-1 1m5 4V0H0v8\"/>"}, "albums": {"body": "<path fill=\"currentColor\" d=\"M1 6c0-2 6-2 6 0S1 8 1 6m4 0l-1-.5L3 6l1 .5M8 8V4H0v4m2-8h4l1 1H1m0 1h6l1 1H0\"/>"}, "alert": {"body": "<path fill=\"currentColor\" d=\"M5 1H3v4h2M3 6v1h2V6M0 2l2-2h4l2 2v4L6 8H2L0 6\"/>"}, "alien": {"body": "<path fill=\"currentColor\" d=\"M3 8C-8-3 16-3 5 8M1 2q0 2 2 2q0-2-2-2m4 2q2 0 2-2q-2 0-2 2\"/>"}, "aliexpress": {"body": "<path fill=\"currentColor\" d=\"m5 7l1-1l1 1l-1 1M2 7l1-1l1 1l-1 1m4-3L6 6H2L0 0h1.5L2 1h5v3H4L3 3h3V2H2l1 3\"/>"}, "alipay": {"body": "<path fill=\"currentColor\" d=\"M1 4V3h6v1C3 12-3 5 3 5l5 1v1L3 6c-4 0 1 3 3-2M1 2V1h6v1M3 0h1v3H3\"/>"}, "amazon": {"body": "<path fill=\"currentColor\" d=\"M5 3H3v1h2m0-3H2V0h4v5H2V2h3m1 3q4 1 0 3l1-2M0 6q3 2 6 0q-2 4-6 1\"/>"}, "ambulance": {"body": "<path fill=\"currentColor\" d=\"M0 1h5l3 3v3H7Q6 9 5 7H3Q2 9 1 7H0m7-3L5 2v2M3 2H2v1H1v1h1v1h1V4h1V3H3\"/>"}, "america": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m4 0V3h1l1-2H2l3 4v2.5L7 4\"/>"}, "americanfootball": {"body": "<path fill=\"currentColor\" d=\"M8 0v2L6 0M0 8V6l2 2M.5 5Q1 1 5 .5L7.5 3Q7 7 3 7.5M2 4l1 1l2-2l-1-1\"/>"}, "ammo": {"body": "<path fill=\"currentColor\" d=\"M0 6.5v-4h2v4m1 0v-4h2v4m1 0v-4h2v4M0 2q1-4 2 0m1 0q1-4 2 0m1 0q1-4 2 0M0 8V7h2v1m1 0V7h2v1m1 0V7h2v1\"/>"}, "amp": {"body": "<path fill=\"currentColor\" d=\"M0 2h8v6H0m1-4h6V3H1m0 4h6V6H1m1 1h1V5H2m3 2h1V5H5M1 2q3-4 6 0H6Q4 0 2 2\"/>"}, "ampersand": {"body": "<path fill=\"currentColor\" d=\"M5 1H1v2h6v1H1v2h4V2h1v4h1v1H2V0h3\"/>"}, "anaglyph": {"body": "<path fill=\"currentColor\" d=\"M5 3v1h2V3M1 3v1h2V3m2 2L4 4L3 5H0V2h8v3\"/>"}, "anchor": {"body": "<path fill=\"currentColor\" d=\"M3 8Q0 8 0 4h2L1 5q0 2 2 2V3Q1 1 4 0q3 1 1 3v4q2 0 2-2L6 4h2q0 4-3 4M3 1v1h2V1\"/>"}, "and": {"body": "<path fill=\"currentColor\" d=\"M0 6V0h6v2h2v6H2V6m1-3v2h2V3\"/>"}, "android": {"body": "<path fill=\"currentColor\" d=\"M2 2C2-.5 6-.5 6 2m1 4V3h1v3M2 8V3h4v5H5V6H3v2M0 6V3h1v3\"/>"}, "angle": {"body": "<path fill=\"currentColor\" d=\"m0 7l7-7h1L2 6h6v1M0 2q6 0 6 6H5c0-4-1-5-5-5\"/>"}, "angry": {"body": "<path fill=\"currentColor\" d=\"M7 0Q4 2 1 0q3 4 6 0m1 1Q6 4 8 7Q4 4 8 1M7 8Q4 4 1 8q3-2 6 0M0 7q2-3 0-6q4 3 0 6\"/>"}, "angular": {"body": "<path fill=\"currentColor\" d=\"m1 6l3 2l3-2l1-4l-4-2l-4 2m4 1l.5 1h-1M4 1l2 5H5V5H3v1H2\"/>"}, "antenna": {"body": "<path fill=\"currentColor\" d=\"M8 6Q1 7 2 0M0 8l3-3l3 3M5 3c4-1 1-4 0 0\"/>"}, "anvil": {"body": "<path fill=\"currentColor\" d=\"M1 7V6q4-1 1-3V1h6v2Q5 4 8 6v1M1.5 1.5V3Q1 3 0 1.5\"/>"}, "apart": {"body": "<path fill=\"currentColor\" d=\"M6 2v1H3v1L0 2.5L3 1v1m2 3H2v1h3v1l3-1.5L5 4\"/>"}, "app": {"body": "<path fill=\"currentColor\" d=\"M6 6v2h2V6M3 6v2h2V6M0 6v2h2V6M0 3v2h2V3m1 0v2h2V3m1 0v2h2V3M6 0v2h2V0M3 0v2h2V0M0 0v2h2V0\"/>"}, "apple": {"body": "<path fill=\"currentColor\" d=\"M4 2q0-2 2-2q0 2-2 2M3 8Q1 7 1 4q1-3 3-1.5Q6 1 7 3Q5 5 7 6Q6 9 4 7.5\"/>"}, "application": {"body": "<path fill=\"currentColor\" d=\"M4 2V1h1v1m1 0V1h1v1M1 7V3h6v4m1 1V0H0v8\"/>"}, "archery": {"body": "<path fill=\"currentColor\" d=\"M0 5V4h8L6 5M1 0c7 0 7 8 0 8m1-1c5 0 5-6 0-6z\"/>"}, "archive": {"body": "<path fill=\"currentColor\" d=\"M1 8V2h6v6M3 3v1h2V3M0 1V0h8v1\"/>"}, "archlinux": {"body": "<path fill=\"currentColor\" d=\"m0 8l2.5-5L4 2H3l1-2l2.5 5L6 6h1l1 2l-3-1Q4 2 3 7\"/>"}, "armchair": {"body": "<path fill=\"currentColor\" d=\"M0 8V4h1v4m6 0V4h1v4M1 3c-2-4 8-4 6 0H6v2H2V3m0 5V6h4v2\"/>"}, "asc": {"body": "<path fill=\"currentColor\" d=\"M1.5 8L0 6h1V0h1v6h1m0-5V0h1v1M3 3V2h3v1M3 5V4h5v1\"/>"}, "asia": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m7-2C5 0 0 1 1 4l1 2l1-2l1 2l.5-1Q6 5 6 3l1 1M5 7.5L6 7L4 6\"/>"}, "at": {"body": "<path fill=\"currentColor\" d=\"M7 5V2H1v4h6v1H2V1h4v3H5V3H3v2\"/>"}, "atm": {"body": "<path fill=\"currentColor\" d=\"M1 4H0V1h8v3H7V2H1m1 6V3h4v5M4 7l1-1l-1-1h1L4 4L3 5l1 1H3\"/>"}, "atom": {"body": "<path fill=\"currentColor\" d=\"m3 4l1-1l1 1l-1 1M3 5q-6-8 2-2q-6-4-2 2q6 4 2-2q6 8-2 2m2 0q-8 6-2-2q-4 6 2 2q4-6-2-2q8-6 2 2\"/>"}, "attachment": {"body": "<path fill=\"currentColor\" d=\"M4 1v4h1V2h1v4H3V0h5v8H1V2h1v5h5V1\"/>"}, "australia": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m6 2.5L7 6V5M2 5q2-.5 3 1q2-1 0-3v1L4 3l-2 .5M4 2l2 1V2\"/>"}, "awd": {"body": "<path fill=\"currentColor\" d=\"M0 3h1V2h3l2 2h2v3H7L6 8L5 7H3L2 8L1 7V6H0m5-2L4 3v1M2 4h1V3H2\"/>"}, "axe": {"body": "<path fill=\"currentColor\" d=\"M3 1h1v7H3M2 2h3l1-1q2 2 0 4L5 4H2\"/>"}, "baby": {"body": "<path fill=\"currentColor\" d=\"M5 2c3-3-5-3-2 0M0 1h1q3 4 6 0h1L6 3v1H2V3m0 2l1 1H2l1 2H2L0 6m5 0l1-1l2 1l-2 2H5l1-2\"/>"}, "babyroom": {"body": "<path fill=\"currentColor\" d=\"M0 8V7h8v1M2 6V4H1V3h2v1h2V2h1v4m0-1l1-1l1 1l-1 1\"/>"}, "back": {"body": "<path fill=\"currentColor\" d=\"M4 2h4v6H4l1-2h1V4H4v2L0 3l4-3\"/>"}, "backlight": {"body": "<path fill=\"currentColor\" d=\"M6 5V3h2v2M3 5V3h2v2M3 2V0h1v1M0 4V3h2L1 4M0 1V0l2 2H1\"/>"}, "backpack": {"body": "<path fill=\"currentColor\" d=\"M1 8V4c0-4 6-4 6 0v4M2 5v1h3v1h1V5M3 2H2V0h1m2 2V0h1v2\"/>"}, "badge": {"body": "<path fill=\"currentColor\" d=\"M3 1q1-2 2 0q3-1 2 2q2 1 0 2q1 3-2 2q-1 2-2 0q-3 1-2-2q-2-1 0-2q-1-3 2-2\"/>"}, "badminton": {"body": "<path fill=\"currentColor\" d=\"M2 3C1-1 7-1 6 3M2 4h4l1 4l-1-1l-1 1l-1-1l-1 1l-1-1l-1 1\"/>"}, "baguette": {"body": "<path fill=\"currentColor\" d=\"M4 6q-5 4-3-1q3 .5 1-1q4 .5 1-1q3 .5 1-1q6-3 2 2\"/>"}, "balance": {"body": "<path fill=\"currentColor\" d=\"M7 4H6L5 3h3M4.5 7l2 1h-5l2-1L4 1M2 4H1L0 3h3\"/>"}, "ball": {"body": "<path fill=\"currentColor\" d=\"M4 8q1-3 4-4q-4 0-4 4c-5.5 0-5-8 0-8Q3 3 0 4q4 0 4-4c5.5 0 5 8 0 8\"/>"}, "ballot": {"body": "<path fill=\"currentColor\" d=\"M1 1v6h6V1M0 8V0h8v8\"/>"}, "bandaid": {"body": "<path fill=\"currentColor\" d=\"M2 7h1V6M2 5H1v1m3-4L2 4l2 2l2-2m1-2L6 3h1M5 1v1l1-1M3 8H2L0 6V5l5-5h1l2 2v1\"/>"}, "bank": {"body": "<path fill=\"currentColor\" d=\"M0 7h7v1H0m3-2V3h1v3m1 0V3h1v3M1 6V3h1v3M0 2l3-2h1l3 2\"/>"}, "barcode": {"body": "<path fill=\"currentColor\" d=\"M7 7V2h1v5M2 7V2h1v5m1 0V2h2v5M0 7V2h1v5\"/>"}, "barrel": {"body": "<path fill=\"currentColor\" d=\"M2 7V1h4v6M1 1V.5h6V1M1 7v-.5h6V7M1.5 4v-.5h5V4\"/>"}, "bars": {"body": "<path fill=\"currentColor\" d=\"M6 8V0h2v8M3 8V5h2v3M0 8V3h2v5\"/>"}, "basket": {"body": "<path fill=\"currentColor\" d=\"M1 7L0 3h1l2-3h1L2 3h4L4 0h1l2 3h1L7 7\"/>"}, "bat": {"body": "<path fill=\"currentColor\" d=\"M3 1q1 1 2 0v2q1 0 1-1h2Q6 4 7 6Q5 5 4 7Q3 5 1 6q1-2-1-4h2q0 1 1 1\"/>"}, "bath": {"body": "<path fill=\"currentColor\" d=\"M2 3V1h2M0 6V1h2L1 2v3h7v1L7 8L6 7H2L1 8\"/>"}, "bathroom": {"body": "<path fill=\"currentColor\" d=\"M4 5H3v3H1V5H0V2h4m0 4l1-4h2l1 4H7v2H5V6m0-5l1-1l1 1l-1 1M1 1l1-1l1 1l-1 1\"/>"}, "battery": {"body": "<path fill=\"currentColor\" d=\"M2 8V1h1V0h2v1h1v7\"/>"}, "battery0": {"body": "<path fill=\"currentColor\" d=\"M5 7V2H3v5M2 8V1h1V0h2v1h1v7\"/>"}, "battery1": {"body": "<path fill=\"currentColor\" d=\"M5 6V2H3v4M2 8V1h1V0h2v1h1v7\"/>"}, "battery2": {"body": "<path fill=\"currentColor\" d=\"M5 5V2H3v3M2 8V1h1V0h2v1h1v7\"/>"}, "battery3": {"body": "<path fill=\"currentColor\" d=\"M5 4V2H3v2M2 8V1h1V0h2v1h1v7\"/>"}, "battery4": {"body": "<path fill=\"currentColor\" d=\"M5 3V2H3v1M2 8V1h1V0h2v1h1v7\"/>"}, "battery5": {"body": "<path fill=\"currentColor\" d=\"M2 8V1h1V0h2v1h1v7\"/>"}, "battleaxe": {"body": "<path fill=\"currentColor\" d=\"M3 1h1v7H3m2-6l1-1q2 2 0 4L5 4H2L1 5q-2-2 0-4l1 1\"/>"}, "beach": {"body": "<path fill=\"currentColor\" d=\"M7 1C3-2-2 3 1 7m3.5-3.5L8 7H7L4 4M3 5L2 6Q0 4 1 2m1-1q2-1 4 1L5 3\"/>"}, "bean": {"body": "<path fill=\"currentColor\" d=\"M4 0C0 0 0 8 4 8s4-8 0-8m0 0C2 5 7 3 4 8c2-5-3-3 0-8\"/>"}, "bed": {"body": "<path fill=\"currentColor\" d=\"M2 4L1 3l1-1l1 1M0 7V1h1v3h2V2h3q2 0 2 5L7 6H1\"/>"}, "bedbunk": {"body": "<path fill=\"currentColor\" d=\"M2 6L1 5l1-1l1 1M0 8V0h1v2h2V0h5v8L7 7H1m1-7l1 1l-1 1l-1-1m0 5h2V4h4V3H1\"/>"}, "bee": {"body": "<path fill=\"currentColor\" d=\"M4 1c2-3 6 1 3 3q1 6-3 3c-2 3-6-1-3-3q-3-4 3-3m0 5q3 3 2-2M4 2q-5-1-2 2\"/>"}, "beer": {"body": "<path fill=\"currentColor\" d=\"M2 2v5h1V2m1 0v5h1V2m1 1v2h1V3M0 8l1-2V0h1q2 3 3 0h1v2c3-1 3 5 0 4l1 2\"/>"}, "bell": {"body": "<path fill=\"currentColor\" d=\"m1 6l1-1c0-5 4-5 4 0l1 1M3 7h2v1H3\"/>"}, "belt": {"body": "<path fill=\"currentColor\" d=\"M5 2v4h1V2M1 7V1l2 1v4M0 5V3h1v2m6 0v3L4 7V1l3-1v3h1v2\"/>"}, "bike": {"body": "<path fill=\"currentColor\" d=\"M4 6L2 4l2-2l1 1h2v1H4l1 1v3H4m1-8l1 1l-1 1l-1-1M0 6.5L1.5 5L3 6.5L1.5 8m5-3L8 6.5L6.5 8L5 6.5\"/>"}, "bill": {"body": "<path fill=\"currentColor\" d=\"M1 4c0-2 6-2 6 0S1 6 1 4m7 2V2H0v4m4-1h1V3H3l1 1\"/>"}, "binocular": {"body": "<path fill=\"currentColor\" d=\"M5 1.5h2V0H5M1 1.5h2V0H1m2.5 4h1V2h-1M8 7V4L7 2H5v2l1 3M2 7l1-3V2H1L0 4v3\"/>"}, "bird": {"body": "<path fill=\"currentColor\" d=\"M6 2H5v1h1m1 0q1 3-3 3v2H3V6H0c5-2 3-7 8-4\"/>"}, "birthday": {"body": "<path fill=\"currentColor\" d=\"M.5 5v.5h7V5M0 5q0-2 2-2V1h1v2h2V1h1v2q2 0 2 2v3H0\"/>"}, "bitcoin": {"body": "<path fill=\"currentColor\" d=\"M4 0v1H3V0m1 7v1H3V7M2 0v8H1V0m5 7H0V6h5V4H2V3h3V2H0V1h6\"/>"}, "blame": {"body": "<path fill=\"currentColor\" d=\"M1 3V0h1v3M1 8V6h1v2m1-5v3H0V3m6-3v4h2V0M6 6v2h2V6M1 4v1h1V4\"/>"}, "bluetooth": {"body": "<path fill=\"currentColor\" d=\"M7 5.5L1 2h1l6 3.5L5 8H4V0h1l3 2.5L2 6H1l6-3.5L5 1v6\"/>"}, "board": {"body": "<path fill=\"currentColor\" d=\"M4 8V3h4v5M0 8V5h3v3m1-6V0h4v2M0 4V0h3v4\"/>"}, "boat": {"body": "<path fill=\"currentColor\" d=\"m0 3l3-3v3m1 1V0l4 4M0 5l1 3h4l3-3M2 7L0 8V7l2-1l2 1l2-1l2 1v1L6 7L4 8\"/>"}, "bold": {"body": "<path fill=\"currentColor\" d=\"m3 4l2-2H3v4h2M1 0q10 0 4 4q6 4-4 4\"/>"}, "bolt": {"body": "<path fill=\"currentColor\" d=\"M4 6C1.5 6 1.5 2 4 2s2.5 4 0 4M2.5 0L0 2.5v3L2.5 8h3L8 5.5v-3L5.5 0\"/>"}, "bone": {"body": "<path fill=\"currentColor\" d=\"M3 5q-4 3-2-1q-2-4 2-1h2q4-3 2 1q2 4-2 1\"/>"}, "book": {"body": "<path fill=\"currentColor\" d=\"M2 6v1h4V6m.5 1H7v1H3Q1 8 1 6V2q0-2 2-2h4v6h-.5\"/>"}, "bookmark": {"body": "<path fill=\"currentColor\" d=\"M4 6L1 8V1h6v7\"/>"}, "boomerang": {"body": "<path fill=\"currentColor\" d=\"M5 3c-7-1-7-3 2-2c1 9-1 9-2 2M4 3L3 1H2m3 3l2 2V5\"/>"}, "boot": {"body": "<path fill=\"currentColor\" d=\"M0 2h5l-.5 2Q8 4 8 6.5H0M0 7h8v1H0m0-8h5v1H0\"/>"}, "bottle": {"body": "<path fill=\"currentColor\" d=\"M2 8V3l1.5-1V0h1v2L6 3v5M5 4H3v2h2\"/>"}, "bowling": {"body": "<path fill=\"currentColor\" d=\"M4 6c0-2.5 4-2.5 4 0S4 8.5 4 6M1 8q-2-2 0-5h1q2 3 0 5M1 2.5Q0 1 1 0h1q1 1 0 2.5\"/>"}, "boxer": {"body": "<path fill=\"currentColor\" d=\"M0 2v5h4V4l1 3h3V2\"/>"}, "boxes": {"body": "<path fill=\"currentColor\" d=\"M0 5h1v1h1V5h1v3H0m4-3h1v1h1V5h1v3H4M2 1h1v1h1V1h1v3H2\"/>"}, "boy": {"body": "<path fill=\"currentColor\" d=\"M4 6L2 5h4M5 4V3h1v1M2 4V3h1v1M2 2c-4 7 8 7 4 0m1 0c4 8-10 8-6-1l7-1\"/>"}, "bra": {"body": "<path fill=\"currentColor\" d=\"M7 4c5 6-11 6-6 0V1q3 9 6 0\"/>"}, "branch": {"body": "<path fill=\"currentColor\" d=\"M1 2v4h1V2M1 1v6h1V1m3 2V0h3v3M3 5v3H0V5m0-2V0h3v3m3-1h1V1H6m0 2h1L3 7V6\"/>"}, "break": {"body": "<path fill=\"currentColor\" d=\"M1 7q-2-3 0-6l1 1Q0 4 2 6m5-5q2 3 0 6L6 6q2-2 0-4M3 1h2v3H3m0 1h2v2H3\"/>"}, "bridge": {"body": "<path fill=\"currentColor\" d=\"M6 7c0-4-4-4-4 0H0V2h8v5\"/>"}, "briefcase": {"body": "<path fill=\"currentColor\" d=\"M3 1h2v1H3M0 7h8V2H6V0H2v2H0\"/>"}, "brightness": {"body": "<path fill=\"currentColor\" d=\"m0 8l2-3l-2-1l2-1l-2-3l4 2l4-2l-2 3l2 1l-2 1l2 3l-4-2m0 0c3 1 3-5 0-4\"/>"}, "broadcast": {"body": "<path fill=\"currentColor\" d=\"M3 3h2l2 5H5V6H3v2H1m4-4H3v1h2M4 0l1 1l-1 1l-1-1M2 0q-4 1 0 3q-2-2 0-3m4 0q4 1 0 3q2-2 0-3\"/>"}, "broom": {"body": "<path fill=\"currentColor\" d=\"M5 4H4l3-4h1M5 5L4 8V6L3 7L0 6l3-2\"/>"}, "brush": {"body": "<path fill=\"currentColor\" d=\"M1 4v4h1V7l2 1h1V5l1 3h1V4M0 3V2h3V0h2v2h3v1\"/>"}, "bucket": {"body": "<path fill=\"currentColor\" d=\"M1 4h6L6 8H2M0 4c0-5.33 8-5.33 8 0H7c0-4-6-4-6 0\"/>"}, "bug": {"body": "<path fill=\"currentColor\" d=\"M0 5V4h8v1M0 8V6h8v2L7 7H1m7-7v3H0V0l1 2h6M3 8C1 8 1 0 4 0s3 8 1 8\"/>"}, "bull": {"body": "<path fill=\"currentColor\" d=\"M5 3v1h1V3M3 3H2v1h1m1 3L1 5l1-4l2-1l2 1l1 4m0-3l1-2v3H0V0l1 2\"/>"}, "bulldozer": {"body": "<path fill=\"currentColor\" d=\"M1 8q-2-1 0-2h3q2 1 0 2m2 0q0-9 1-1q2 1-1 1M0 5.5V0h3l1 3h1v2.5M3 3L2 1H1v2\"/>"}, "burger": {"body": "<path fill=\"currentColor\" d=\"M0 5V4h8v1M0 3c0-4 8-4 8 0M0 6c0 3 8 3 8 0\"/>"}, "burn": {"body": "<path fill=\"currentColor\" d=\"M3 8Q0 6 4 0q4 6 1 8m0-1q1-1-1-4q-2 3-1 4\"/>"}, "bus": {"body": "<path fill=\"currentColor\" d=\"M6 5v1h1V5M1 5v1h1V5M1 2v2h6V2m0 6H6V7H2v1H1L0 7V1l2-1h4l2 1v6\"/>"}, "business": {"body": "<path fill=\"currentColor\" d=\"M3 4c-4-5.5 6-5.5 2 0M3 5h2v3l1-3l2 1v2H0V6l2-1l1 3\"/>"}, "bust": {"body": "<path fill=\"currentColor\" d=\"M3 5C-1-.5 9-.5 5 5M1 8q3-4 6 0\"/>"}, "butterfly": {"body": "<path fill=\"currentColor\" d=\"M4 4c0-9 9 0 0 0c0 9 7 0 0 0c0 9-7 0 0 0c-9 0 0-9 0 0\"/>"}, "cablecar": {"body": "<path fill=\"currentColor\" d=\"M3 1v2h2V1M0 1v1l8-1V0M4 4v2h1V4M1 4v2h6V4M0 8V3h8v5\"/>"}, "cactus": {"body": "<path fill=\"currentColor\" d=\"M3 7L0 6V2h2v3h1m2-2h1V1h2v3L5 5M3 8V0h2v8\"/>"}, "cake": {"body": "<path fill=\"currentColor\" d=\"M1 4v1h7V4M1 6v1h7V6M0 8V3l2-2l6 2v5\"/>"}, "calc": {"body": "<path fill=\"currentColor\" d=\"M4 7V4h1v3M2 7V4h1v3M1 6V5h3v1M1 3V1h5v2M1 7V4h5v3M0 0v8h7V0\"/>"}, "calendar": {"body": "<path fill=\"currentColor\" d=\"M1 3v4h6V3M0 8V1h2V0h1v1h2V0h1v1h2v7\"/>"}, "call": {"body": "<path fill=\"currentColor\" d=\"M0 0h3v3H1l4 4V5h3v3H5Q1 7 0 3\"/>"}, "camcorder": {"body": "<path fill=\"currentColor\" d=\"M0 7V1h5v6m0-2V3l3-2v6\"/>"}, "camera": {"body": "<path fill=\"currentColor\" d=\"M4 6c2 0 2-3 0-3S2 6 4 6M0 7V2h2V1h4v1h2v5\"/>"}, "cameraroll": {"body": "<path fill=\"currentColor\" d=\"M0 8V1h1V0h2v1h1v1h4v6M4 3v1h1V3m1 0v1h1V3M6 6v1h1V6M4 6v1h1V6\"/>"}, "camping": {"body": "<path fill=\"currentColor\" d=\"m4 2l1-1h1L5 3l3 4v1H0V7l3-4l-1-2h1m1 3L2 7h4\"/>"}, "campingcar": {"body": "<path fill=\"currentColor\" d=\"M0 0h8v2H6l2 2v3H7L6 8L5 7H3L2 8L1 7H0m7-3L5 2v2M2 2H1v1h1\"/>"}, "candle": {"body": "<path fill=\"currentColor\" d=\"M2 4h4v4H2m3-4q2-2-1-4q-3 2-1 4m1 0Q2 3 4 1q2 2 0 3M3 4q0 5 1 0\"/>"}, "candy": {"body": "<path fill=\"currentColor\" d=\"m0 2l2 1l1-1h2l1 1l2-1v4L6 5L5 6H3L2 5L0 6\"/>"}, "car": {"body": "<path fill=\"currentColor\" d=\"M7 5Q3 6 7 6M1 6q4 0 0-1m6-1c0-3-6-3-6 0m1 3q-2 3-2-3c0-4 8-4 8 0q0 6-2 3\"/>"}, "carousel": {"body": "<path fill=\"currentColor\" d=\"M2 8V0h4v8M0 7V1h1v6m6 0V1h1v6\"/>"}, "carrot": {"body": "<path fill=\"currentColor\" d=\"M4 2q-9 11 2 2M5 3h3L6 2L5 0M1.5 5.5L2 7L1 6m3-1L3 4l2 1\"/>"}, "cart": {"body": "<path fill=\"currentColor\" d=\"m7 6l1 1l-1 1l-1-1M2 7l1-1l1 1l-1 1M0 1V0h2l1 1h5L6 4H3v1h4l1 1H1l1-2l-1-3\"/>"}, "cashier": {"body": "<path fill=\"currentColor\" d=\"M5 3L4 5L3 3H1V0h6v3M2 1v1h4V1M0 8l1-4h6l1 4M1 6.5V7h6v-.5\"/>"}, "cassette": {"body": "<path fill=\"currentColor\" d=\"M2 5.5L1 7l1-1h4l1 1l-1-1.5M2 3v1h1V3m3 0H5v1h1M0 7V1h8v6M1 5h6V2H1\"/>"}, "cast": {"body": "<path fill=\"currentColor\" d=\"M1 7q1-2-1-1V5q3-1 2 2M1 2v1q3 0 3 3h3V2M3 7q1-4-3-3V1h8v6\"/>"}, "cat": {"body": "<path fill=\"currentColor\" d=\"M5 3v1h1V3M2 3v1h1V3m5 2c0 4-8 4-8 0V0l2 2h4l2-2\"/>"}, "cctv": {"body": "<path fill=\"currentColor\" d=\"M2 4L1 1l7-1v3m0 2v1H5V3h1v2M0 4V2h1v2\"/>"}, "celcius": {"body": "<path fill=\"currentColor\" d=\"M2 1H1v1h1m1 1H0V0h3m4 7H4V2h3l1 1H5v3h3\"/>"}, "center": {"body": "<path fill=\"currentColor\" d=\"M7 6v1H1V6m7-2v1H0V4m6-2v1H2V2m5-2v1H1V0\"/>"}, "certified": {"body": "<path fill=\"currentColor\" d=\"M2 1v3h3V1m0 7L3.5 6L2 8V5L1 4V1l1-1h3l1 1v3L5 5\"/>"}, "chain": {"body": "<path fill=\"currentColor\" d=\"M2 4v1h4V4M5 6h2V3H5l1-1h1l1 1v3L7 7H4M1 7L0 6V3l1-1h3L3 3H1v3h2L2 7\"/>"}, "chalk": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v6H0m1-1h3V5h2v1h1V2H1\"/>"}, "champagne": {"body": "<path fill=\"currentColor\" d=\"M2 8h4L4.5 7L4 0l-.5 7M2 0c0 7 4 7 4 0\"/>"}, "charge": {"body": "<path fill=\"currentColor\" d=\"M1 4h2v1H1m3-1h1V3h1v1h1v1H6v1H5V5H4M0 7h8V2H7V1H5v1H3V1H1v1H0\"/>"}, "charger": {"body": "<path fill=\"currentColor\" d=\"M0 0h5v8H0m4-7L0 4h2m0 0L0 7l4-3m2-3v1h1V1h1L7 3v3H3l3-1V3L5 1\"/>"}, "charging": {"body": "<path fill=\"currentColor\" d=\"M5 4H4V1L3 5h1v3M2 8V1h1V0h2v1h1v7\"/>"}, "chart": {"body": "<path fill=\"currentColor\" d=\"M7 6H6V3h1M5 6H4V1h1M3 6H2V4h1M0 8V0h1v7h7v1\"/>"}, "chat": {"body": "<path fill=\"currentColor\" d=\"M2 6L0 8V2h3V0h5v6L6 4H5v2M1 5h3V3H1m3 0h3V1H4\"/>"}, "chatbot": {"body": "<path fill=\"currentColor\" d=\"M6 6H0V1h2V0h1v1h2V0h1v1h2v7M2 4v1h4V4M2 3h1V2H2m3 1h1V2H5\"/>"}, "check": {"body": "<path fill=\"currentColor\" d=\"M3 7L0 4l1-1l2 2l4-4l1 1\"/>"}, "checked": {"body": "<path fill=\"currentColor\" d=\"M3 5L1 3v2l2 2l4-4V1M0 8V0h8v8\"/>"}, "checklist": {"body": "<path fill=\"currentColor\" d=\"M4 5v1h4V5M4 1v1h4V1M1 1v1h1V1M1 5v1h1V5M0 7V4h3v3M0 3V0h3v3\"/>"}, "checkout": {"body": "<path fill=\"currentColor\" d=\"M1 3V0h1v3M1 8V6h1v2m1-5v3H0V3m4 1v1h2v2l2-2.5L6 2v2M1 4v1h1V4\"/>"}, "cheese": {"body": "<path fill=\"currentColor\" d=\"M0 5h8v3H0m0-4l5-3q3 0 3 3M2 8l1-1l-1-1l-1 1m4 0l1-1l-1-1l-1 1\"/>"}, "chef": {"body": "<path fill=\"currentColor\" d=\"M2 5C0 5 0 2 2 2q2-2 4 0c2 0 2 3 0 3v3H2\"/>"}, "chess": {"body": "<path fill=\"currentColor\" d=\"m6 3l1 3v2H4V3l1-2l3 2M2 5l1 3H0l1-3l-1-2h1V0h1v3h1M0 2V1h3v1\"/>"}, "chick": {"body": "<path fill=\"currentColor\" d=\"m0 3l1-1c0-3 3-2 3 0l4 1c0 5-8 4-7 0m2-1Q2.5.5 2 2m1 6V6h2v2L4 6\"/>"}, "chili": {"body": "<path fill=\"currentColor\" d=\"M2 2q1 4 6 5q-8 1-7-4m1-3v1L1 2\"/>"}, "chip": {"body": "<path fill=\"currentColor\" d=\"m0 1l1-1h6l1 1M0 3V2h2v1M0 5V4h2v1M1 7L0 6h8L7 7M3 5V2h5v1H5v2m1 0V4h2v1\"/>"}, "chrome": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m8-2Q3 1 2 3L1 1q0 5 3 5L3 8q4-2 3-5M3 5V3h2v2\"/>"}, "church": {"body": "<path fill=\"currentColor\" d=\"M4 8V6H3v2m4 0H0V5l3-2V0h1v3l3 2M2 2V1h3v1\"/>"}, "circle": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5.25 8-5.25 8 0S0 9.25 0 4m7 0c0-4-6-4-6 0s6 4 6 0\"/>"}, "cli": {"body": "<path fill=\"currentColor\" d=\"M4 5v1h3V5M1 1l2 2.5L1 6h1l2-2.5L2 1M0 7V0h8v7\"/>"}, "climb": {"body": "<path fill=\"currentColor\" d=\"m0 1l3-1l-1 3m6-3v2L2 8H0V6h2V4h2V2h2V0\"/>"}, "clipboard": {"body": "<path fill=\"currentColor\" d=\"M1 5v1h3V5M1 3v1h5V3M3 0L2 2h4L5 0M0 8V1h8v7\"/>"}, "clock": {"body": "<path fill=\"currentColor\" d=\"M4 4h4L5 3L4 0c5 0 5 8 0 8s-5-8 0-8\"/>"}, "cloud": {"body": "<path fill=\"currentColor\" d=\"M1 5C0 5-1 2 2 2c0-3 4-2 4-1c3 1 2 4 0 4\"/>"}, "clover": {"body": "<path fill=\"currentColor\" d=\"M4 3q1-5 2.5-1.5Q10 3 5 4q5 0 1.5 2.5Q5 10 4 5q-1 5-2.5 1.5Q-2 5 3 4q-5-1-1.5-2.5Q3-2 4 3\"/>"}, "cocktail": {"body": "<path fill=\"currentColor\" d=\"M4 8H1l1-1V5L0 2h5L3 5v2m3-5.5H4C6-2 9 3 5 3\"/>"}, "code": {"body": "<path fill=\"currentColor\" d=\"M6 6H5l2-2l-2-2h1l2 2M3 6H2L0 4l2-2h1L1 4\"/>"}, "coffee": {"body": "<path fill=\"currentColor\" d=\"M5 2v1h1V2M0 8V7h7v1M1 6V1h4c3 0 3 3 0 3v2\"/>"}, "cog": {"body": "<path fill=\"currentColor\" d=\"M5 0q0 3 2 1l1 2Q5 4 8 5L7 7Q5 5 5 8H3q0-3-2-1L0 5q3-1 0-2l1-2q2 2 2-1m0 5h2V3H3\"/>"}, "collumn": {"body": "<path fill=\"currentColor\" d=\"M3 8V0h2v8M0 8V0h2v8m4 0V0h2v8\"/>"}, "colon": {"body": "<path fill=\"currentColor\" d=\"M4 3V2h1v1m0 3H4V5h1\"/>"}, "colors": {"body": "<path fill=\"currentColor\" d=\"M0 5h8v3H0m5-8h3v4H5m1 3h1V6H6M2 4L0 2l2-2l2 2v2\"/>"}, "comma": {"body": "<path fill=\"currentColor\" d=\"M5 7H3l1-1V5h1\"/>"}, "commit": {"body": "<path fill=\"currentColor\" d=\"M2 4v1h1V4m0 2v2H2V6H1V3h1V0h1v3h1v3\"/>"}, "compass": {"body": "<path fill=\"currentColor\" d=\"m0 8l3-6h2l3 6l-4-5m3 0l1-1v3L7 4H6V3M3 1V0h2v1\"/>"}, "cone": {"body": "<path fill=\"currentColor\" d=\"M0 7h8v1H0m2.5-5h3L6 5H2m1.5-5h1L5 2H3M1.5 6h5L7 7H1\"/>"}, "config": {"body": "<path fill=\"currentColor\" d=\"M5 8V0h2v8M1 8V0h2v8m1-1V5h4v2M0 4V2h4v2\"/>"}, "connected": {"body": "<path fill=\"currentColor\" d=\"M0 5V3h8v2M4.5 1q4.5 3 0 6M4 7q-5-3 0-6\"/>"}, "construction": {"body": "<path fill=\"currentColor\" d=\"M0 5h1q0-3 1-4l1 3V1h2v3l1-3q1 1 1 4h1v1H0\"/>"}, "container": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v6H0m2-5v4h.5V2M4 2v4h.5V2M6 2v4h.5V2\"/>"}, "contrast": {"body": "<path fill=\"currentColor\" d=\"M4 1c4 0 4 6 0 6v1c5 0 5-8 0-8s-5 8 0 8\"/>"}, "cookie": {"body": "<path fill=\"currentColor\" d=\"M6 2Q5 5 8 4q0 4-4 4c-5 0-5-8 0-8Q3 3 6 2M2 3h1V2H2m1 5h1V6H3m2-1h1V4H5\"/>"}, "couch": {"body": "<path fill=\"currentColor\" d=\"M1 5q-2-2 1-1v1.5h4V4q3-1 1 1v2H1m1.5-3L1 3q0-1 2-1h2q2 0 2 1L5.5 4v1h-3\"/>"}, "coupon": {"body": "<path fill=\"currentColor\" d=\"M2 6h1l3-4H5m0 3v1h1V5M2 2v1h1V2m5 1l-1 .5v1L8 5v2H0V5l1-.5v-1L0 3V1h8\"/>"}, "cow": {"body": "<path fill=\"currentColor\" d=\"M1 6v2H0V2h3v1l3 3v2H5V6m0-1V3H4V0l1 2h2l1-2v3H7v2\"/>"}, "cowboy": {"body": "<path fill=\"currentColor\" d=\"M0 3q4 2 8 0L6 5Q4 6 2 5m2-3q2-3 2 1q-2 1-4 0q0-4 2-1\"/>"}, "cpp": {"body": "<path fill=\"currentColor\" d=\"M0 2v4l4 2l4-2V2L4 0M3 5h3v1H2V2h4v1H3\"/>"}, "cpu": {"body": "<path fill=\"currentColor\" d=\"M0 7V6l8-1v1M0 5V4l8-1v1M6 0l1 8H6L5 0M4 0l1 8H4L3 0M2 0l1 8H2L1 0M0 3V2l8-1v1M7 7H1V1h6\"/>"}, "crate": {"body": "<path fill=\"currentColor\" d=\"M0 8V1h1v5h6V1h1v7M1 5V3h6v2\"/>"}, "credit": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v6H0m0-4h8V2H0m1 4h2V5H1\"/>"}, "croissant": {"body": "<path fill=\"currentColor\" d=\"M4 5L2 2h4m2 4L5 5l2-2M0 6l1-3l2 2\"/>"}, "crop": {"body": "<path fill=\"currentColor\" d=\"M2 2V0h1v2h3v3h2v1H6v2H5V6H2V4h1v1h2V3H0V2\"/>"}, "crossed": {"body": "<path fill=\"currentColor\" d=\"M4 3L2 1L1 2l2 2l-2 2l1 1l2-2l2 2l1-1l-2-2l2-2l-1-1M0 8V0h8v8\"/>"}, "crown": {"body": "<path fill=\"currentColor\" d=\"M1 6L0 1l2 2h1l1-2l1 2h1l2-2l-1 5\"/>"}, "crystalball": {"body": "<path fill=\"currentColor\" d=\"M4 6C0 6 0 0 4 0s4 6 0 6M0 7l3-2h2l3 2M3 4q0-2 2-2q-2 0-2-2q0 2-2 2q2 0 2 2\"/>"}, "cs": {"body": "<path fill=\"currentColor\" d=\"M3 5V4h5v1M3 3V2h5v1M6 6V1h1v5M1 6h2l1 1H0V0h4L3 1H1m4 5H4V1h1\"/>"}, "css": {"body": "<path fill=\"currentColor\" d=\"m1 7l3 1l3-1l1-7H0m1.5 3H5l.5-1H1V1h6L6 6H2V5h3V4H1.5\"/>"}, "cube": {"body": "<path fill=\"currentColor\" d=\"m4 0l4 2v4L4 8L0 6V2m4 5.5l3.5-1.75V2L4 3.5\"/>"}, "curling": {"body": "<path fill=\"currentColor\" d=\"M0 6c0-3 8-3 8 0S0 9 0 6m0 0v.5h8V6M2 3V2h3v2L4 3\"/>"}, "cursor": {"body": "<path fill=\"currentColor\" d=\"M0 7V0l5 5H3l1 2l-1 1l-1.5-2.5\"/>"}, "curve": {"body": "<path fill=\"currentColor\" d=\"M6 8V6h2v2M0 2V0h2v2m0-1c8 1-2 4 3 5l1 1c-8-2 2-4-3-5\"/>"}, "cut": {"body": "<path fill=\"currentColor\" d=\"M4 8H0V0h4M3 7V5H1v2m2-4V1H1v2m4 1l3 3l-1 1l-4-4l4-4l1 1\"/>"}, "dashed": {"body": "<path fill=\"currentColor\" d=\"M1 3v2h6V3M0 8V0h8v8\"/>"}, "database": {"body": "<path fill=\"currentColor\" d=\"M1 2c0-2 6-2 6 0S1 4 1 2m0 2q3 2 6 0v1Q4 7 1 5m0 1q3 2 6 0v1Q4 9 1 7\"/>"}, "debian": {"body": "<path fill=\"currentColor\" d=\"M5 8q-7-4-2-8q5-1 4 4q-4 2-2-2q-2 1-1 2q2 1 2-2q-3-3-4 2\"/>"}, "desc": {"body": "<path fill=\"currentColor\" d=\"M1.5 8L0 6h1V0h1v6h1m0 0V5h1v1M3 4V3h3v1M3 2V1h5v1\"/>"}, "descent": {"body": "<path fill=\"currentColor\" d=\"m5 2l2-2l1 3m0 3v2H6L0 2V0h2v2h2v2h2v2\"/>"}, "desktop": {"body": "<path fill=\"currentColor\" d=\"M0 8V3h8v5M1 2V0h3v2M1 4v4h4V4m2 3V4H6v3\"/>"}, "devices": {"body": "<path fill=\"currentColor\" d=\"M0 5h1V0h6v2H6V1H2v4h1v1H0m4-3h4v5H4m1-1h2V4H5\"/>"}, "dice": {"body": "<path fill=\"currentColor\" d=\"M5 4v3h1V4M4 5v1h3V5M4 4v3h3V4M0 6V.5l2 2V8m.5-6l-2-2H6l2 2M2.5 8V2.5H8V8\"/>"}, "dice1": {"body": "<path fill=\"currentColor\" d=\"M3 3h1v1H3M0 7h7V0H0\"/>"}, "dice2": {"body": "<path fill=\"currentColor\" d=\"M1 5h1v1H1m4-5h1v1H5M0 7h7V0H0\"/>"}, "dice3": {"body": "<path fill=\"currentColor\" d=\"M1 5h1v1H1m2-3h1v1H3m2-3h1v1H5M0 7h7V0H0\"/>"}, "dice4": {"body": "<path fill=\"currentColor\" d=\"M1 5h1v1H1m4-1h1v1H5m0-5h1v1H5M1 1h1v1H1M0 7h7V0H0\"/>"}, "dice5": {"body": "<path fill=\"currentColor\" d=\"M1 5h1v1H1m4-1h1v1H5M3 3h1v1H3m2-3h1v1H5M1 1h1v1H1M0 7h7V0H0\"/>"}, "dice6": {"body": "<path fill=\"currentColor\" d=\"M2 5h1v1H2m2-1h1v1H4M2 3h1v1H2m2-1h1v1H4m0-3h1v1H4M2 1h1v1H2M0 7h7V0H0\"/>"}, "diff": {"body": "<path fill=\"currentColor\" d=\"M1 3V2h5v1M3 5V0h1v5M1 7V6h5v1\"/>"}, "digging": {"body": "<path fill=\"currentColor\" d=\"m6 4l2 4H4M3 1l1-1l1 1l-1 1M1 3l3 2v3H3V6L2 5L1 8H0m0-7l5 4V4L0 0\"/>"}, "direction": {"body": "<path fill=\"currentColor\" d=\"M5 3H3v3h1V4h1v1l2-1l-2-2M0 4l4-4l4 4l-4 4\"/>"}, "disc": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m2 0l2 2l2-2l-2-2m0 3L3 4l1-1l1 1\"/>"}, "disconnected": {"body": "<path fill=\"currentColor\" d=\"M2 6V5h2v1M2 3V2h2v1m2-2q4 3 0 6M2 7q-4-3 0-6\"/>"}, "docker": {"body": "<path fill=\"currentColor\" d=\"M7 5c1 3-8 5-7-1h6l2-2v3M4 3.5V2h2v1.5M2 2V.5h2V2M0 3.5V2h2v1.5M2 5v1h1V5\"/>"}, "dog": {"body": "<path fill=\"currentColor\" d=\"M0 8V2l1 1h4V0l1 1h2v2H7v5H6L5 5H2L1 8\"/>"}, "dollar": {"body": "<path fill=\"currentColor\" d=\"M2 8V0h1v8m1 0V0h1v8M1 4V1h6v1H0v1h6v4H0V6h7V4\"/>"}, "dolly": {"body": "<path fill=\"currentColor\" d=\"m4 7l4-2V4L4 6L2 2H0v1h2m1 0l2-1l1 2l-2 1m1 2L4 6L3 7l1 1\"/>"}, "door": {"body": "<path fill=\"currentColor\" d=\"M5 5V4H4v1M0 8V7h1V0h5v7h1v1\"/>"}, "dot": {"body": "<path fill=\"currentColor\" d=\"M4 5V4h1v1m1-4V0h1v1M2 3V2h1v1M0 8V0h1v7h7v1\"/>"}, "down": {"body": "<path fill=\"currentColor\" d=\"m2 3l2 2l2-2l1 1l-3 3l-3-3\"/>"}, "downdouble": {"body": "<path fill=\"currentColor\" d=\"m2 0l2 2l2-2l1 1l-3 3l-3-3m1 3l2 2l2-2l1 1l-3 3l-3-3\"/>"}, "download": {"body": "<path fill=\"currentColor\" d=\"M7 7v1H1V7m2-3V1h2v3h2L4 7L1 4\"/>"}, "downmost": {"body": "<path fill=\"currentColor\" d=\"M1 6h6v1H1m1-5l2 2l2-2l1 1l-3 3l-3-3\"/>"}, "downright": {"body": "<path fill=\"currentColor\" d=\"M4 4H2V0H0v6h4v2l4-3l-4-3\"/>"}, "downward": {"body": "<path fill=\"currentColor\" d=\"M3 4V1h2v3h2L4 7L1 4\"/>"}, "dress": {"body": "<path fill=\"currentColor\" d=\"M1 8q3-4 1-6L1 3L0 2q1-2 3-2q1 2 2 0q2 0 3 2L7 3L6 2Q4 4 7 8\"/>"}, "drink": {"body": "<path fill=\"currentColor\" d=\"M6 8H3L2 2h5M1 1V0h3v2H3V1\"/>"}, "drive": {"body": "<path fill=\"currentColor\" d=\"m0 3l2-3h4l2 3v5H0m1-5h6L6 1H2M1 7h6V4H1m1 1h1v1H2\"/>"}, "droplet": {"body": "<path fill=\"currentColor\" d=\"M4 1c7 8-7 8 0 0M3 4v1h1V3\"/>"}, "drums": {"body": "<path fill=\"currentColor\" d=\"M1 0H0l3 3h1m4-3H7L4 3h1m3 0c0-3-8-3-8 0v3c0 3 8 3 8 0M2 4Q0 3 2 2h4q2 1 0 2\"/>"}, "dry": {"body": "<path fill=\"currentColor\" d=\"m2 1l2 2l2-2M2 7L0 8V7l2-1l2 1l2-1l2 1v1L6 7L4 8M2 5L0 6V5l2-1l2 1l2-1l2 1v1L6 5L4 6\"/>"}, "duck": {"body": "<path fill=\"currentColor\" d=\"M4 1h1V0M3 8V6L0 3h4Q1 0 5 0l2 2H5q4 4-1 4v1l2 1\"/>"}, "east": {"body": "<path fill=\"currentColor\" d=\"M5 4L3 6V2\"/>"}, "eatin": {"body": "<path fill=\"currentColor\" d=\"M2 3V2h4v1M1 5v2H0V1h1v3h2v3H2V5m3 2V4h2V1h1v6H7V5H6v2\"/>"}, "eject": {"body": "<path fill=\"currentColor\" d=\"M0 7V5h8v2M0 4l4-4l4 4\"/>"}, "elbow": {"body": "<path fill=\"currentColor\" d=\"M6 8V6h2v2M0 2V0h2v2m1 0L2 1h3v5l1 1H4V2\"/>"}, "electricbass": {"body": "<path fill=\"currentColor\" d=\"M0 8V5l3 3m3-5l2 1l-1 1M6 6L5 7L4 5M3 1l1-1l1 2M1 3l1-1l1 2M1 5l5-5l2 2l-5 5\"/>"}, "electricguitar": {"body": "<path fill=\"currentColor\" d=\"M0 8V5l2 2m4-6l1-1v1M4 2l1-1v1M2 3l1-1v1M0 4l1-1v1m0 1l7-4v2L6 4L5 6H2\"/>"}, "elevator": {"body": "<path fill=\"currentColor\" d=\"m5 5l1.5 2L8 5M5 3l1.5-2L8 3M0 0v8h5V0M2 1h1v1H2m0 5V5H1V3h3v2H3v2\"/>"}, "ellipsis": {"body": "<path fill=\"currentColor\" d=\"M6 3h2v2H6M3 3h2v2H3M0 5V3h2v2\"/>"}, "engine": {"body": "<path fill=\"currentColor\" d=\"M2 1h4v1H5v1h1v1h1V3h1v3H7V5H6v2H4L3 6H2V5H1v1H0V2h1v1h2V2H2\"/>"}, "enlarge": {"body": "<path fill=\"currentColor\" d=\"M4 1v1H2v2H1V1m5 3v2H4v1h3V4\"/>"}, "enter": {"body": "<path fill=\"currentColor\" d=\"M4 5V4H3v1M0 8V7h1V0h6v7h1v1H6V1H5v7\"/>"}, "envelope": {"body": "<path fill=\"currentColor\" d=\"m1 2l3 2l3-2l-3 1m3 2V2H1v3M0 6V1h8v5\"/>"}, "equals": {"body": "<path fill=\"currentColor\" d=\"M1 2h6v1H1m0 2h6v1H1\"/>"}, "eraser": {"body": "<path fill=\"currentColor\" d=\"m1 5l2 2l2-1.5l-2-2M0 5l5-4l3 3l-5 4\"/>"}, "escalator": {"body": "<path fill=\"currentColor\" d=\"M3 4V1h1v2M2 8H0V6h2l4-4h2v2H6\"/>"}, "ethereum": {"body": "<path fill=\"currentColor\" d=\"M1 4.5L4 6l3-1.5L4 8M1 4l3-4l3 4l-3 1.5\"/>"}, "europe": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m2-2l1 2H1l1 2l2-2l1 2V4l2 1v1H1c4 4 9-1 5-5M2 1l1 .5v-1\"/>"}, "even": {"body": "<path fill=\"currentColor\" d=\"M7.5 6L6 2L4.5 6m-1 0L2 2L.5 6M7 7H5L4 6l2-4l2 4M3 7H1L0 6l2-4l2 4M0 2l3-1h2l3 1\"/>"}, "exit": {"body": "<path fill=\"currentColor\" d=\"M4 3L3 5h1v3H3V6H2v1H0V6h1l1-3H1L0 4V2h4l1-1l-1-1l-1 1l2 2h1v1H5m2-3H6L5 0h3v8H7\"/>"}, "external": {"body": "<path fill=\"currentColor\" d=\"M1 1v6h6V5h1v3H0V0h3v1m4 1L3 6L2 5l4-4H4V0h4v4H7\"/>"}, "eye": {"body": "<path fill=\"currentColor\" d=\"M5 3H3v2h2M4 2C1 2 1 6 4 6s3-4 0-4m4 2c0 4-8 4-8 0s8-4 8 0\"/>"}, "facebook": {"body": "<path fill=\"currentColor\" d=\"M0 0v8h8V0M4 8V4H3V3h1V2l1-1h2v1H5v1h2l-.5 1H5v4\"/>"}, "factory": {"body": "<path fill=\"currentColor\" d=\"M0 0h2v4l3-3v3l3-3v7H0\"/>"}, "false": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4\"/>"}, "fan": {"body": "<path fill=\"currentColor\" d=\"M4 0L2 2.5L4 4m0 0l2 1.5L4 8m0-4l1.5-2L8 4M0 4l2.5 2L4 4\"/>"}, "fandom": {"body": "<path fill=\"currentColor\" d=\"M7 5V3L3 7l1 1M1 3v2l1.5 1.5l1-1M6 1L4.5 2.5l1 1L7 2M2 1L1 2l3 3l1-1M0 5V2l2-2l2 2l2-2l2 2v3L4 9\"/>"}, "fareneight": {"body": "<path fill=\"currentColor\" d=\"M2 1H1v1h1m1 1H0V0h3m4 5H5v2H4V2h4L7 3H5v1h2\"/>"}, "fastforward": {"body": "<path fill=\"currentColor\" d=\"M8 4L4 0v8m0-4L0 0v8\"/>"}, "fastrewind": {"body": "<path fill=\"currentColor\" d=\"m4 4l4-4v8M0 4l4-4v8\"/>"}, "faucet": {"body": "<path fill=\"currentColor\" d=\"M0 3h3l1-1l1 1h2v3H5V5H0m2-4h4v1H2\"/>"}, "feather": {"body": "<path fill=\"currentColor\" d=\"m0 8l7-7l-7 6m1 0c1-9 13-7 2-1h1\"/>"}, "featherpen": {"body": "<path fill=\"currentColor\" d=\"m0 8l2-7l4-1l2 2l-1 4M3 3v1L0 8l4-3h1V3\"/>"}, "feeder": {"body": "<path fill=\"currentColor\" d=\"M7 3c0-3-6-3-6 0m0 1h6v4H1m5-5Q4-3 2 3\"/>"}, "female": {"body": "<path fill=\"currentColor\" d=\"M2.5 8C-1 8-1 3 2.5 3s3.5 5 0 5M1 5.5c0 2 3 2 3 0s-3-2-3 0M3 4l4-4l1 1l-4 4m0-5l4 4l-1 1l-4-4\"/>"}, "fence": {"body": "<path fill=\"currentColor\" d=\"M1 8V2l1-2l1 2v6m2 0V2l1-2l1 2v6M0 4V3h8v1M0 7V6h8v1\"/>"}, "file": {"body": "<path fill=\"currentColor\" d=\"M4 0v2h2M1 8V0h4l2 2v6\"/>"}, "files": {"body": "<path fill=\"currentColor\" d=\"M2 7v1h6V2H7v5M3 0v2h2M1 6V0h3l2 2v4\"/>"}, "filler": {"body": "<path fill=\"currentColor\" d=\"M6 0H5v4h1M1 4v4H0V4l4-2h1M4 8L2 4l4-2l2 4\"/>"}, "film": {"body": "<path fill=\"currentColor\" d=\"M0 8V1h8v7M1 3h6v3H1m0-1h6V4H1m0 3h1V2H1m5 5h1V2H6\"/>"}, "filter": {"body": "<path fill=\"currentColor\" d=\"m0 1l3 3v3l2-1V4l3-3\"/>"}, "fingerprint": {"body": "<path fill=\"currentColor\" d=\"M2 6C1 0 7 1 7 5H6c0-3-4-3-3 1m3 2V6h1v2M0 4c-1-5 8-5 8-2H7c0-2-7-2-6 2m0 1c0 3 4 3 3-1h1c1 5-5 5-5 1\"/>"}, "firefox": {"body": "<path fill=\"currentColor\" d=\"M2 1h1l1-1v2h1L3 3l2 1C0 4 8 9 6 2l1 1l-1-3c8 10-12 11-4 0\"/>"}, "fish": {"body": "<path fill=\"currentColor\" d=\"M0 6c10-12 10 8 0-4m6 1v1l1-1\"/>"}, "fist": {"body": "<path fill=\"currentColor\" d=\"M2 8V6c-3-1-3-5 2-4l1 1H2v1h5L6 6v2m0-5V0l1 1v2M2 1V0h1v1m1 0V0h1v2M0 1V0h1v1\"/>"}, "fitness": {"body": "<path fill=\"currentColor\" d=\"M5 6V1h2v5M1 6V1h2v5M0 4V3h8v1\"/>"}, "fix": {"body": "<path fill=\"currentColor\" d=\"M2 8L0 6l6-6c-9-1 3 11 2 2\"/>"}, "flag": {"body": "<path fill=\"currentColor\" d=\"M1 8V0c6 0 0 1 6 1v5c-6 0-.5-1-5-1v3\"/>"}, "flame": {"body": "<path fill=\"currentColor\" d=\"M4 5L2 4C0 9 9 8 5 2m1-2c5 14-12 7-3 0v3\"/>"}, "flask": {"body": "<path fill=\"currentColor\" d=\"M0 8V7l2-4V1H1V0h2v3L2 5h4L5 3V0h2v1H6v2l2 4v1\"/>"}, "flight": {"body": "<path fill=\"currentColor\" d=\"M0 3V2h1l1 1h2L3 1h1l2 2h2v1H1\"/>"}, "flood": {"body": "<path fill=\"currentColor\" d=\"m2 3l2-2l2 2M2 7L0 8V7l2-1l2 1l2-1l2 1v1L6 7L4 8M2 5L0 6V5l2-1l2 1l2-1l2 1v1L6 5L4 6\"/>"}, "floppy": {"body": "<path fill=\"currentColor\" d=\"M5 3V1h1v2M3 .5V3h4V.5M1 4v3.5h6V4M0 8V0h8v8\"/>"}, "flush": {"body": "<path fill=\"currentColor\" d=\"M4 6v2H0V6m6-3v2H2V3m6-3v2H4V0\"/>"}, "folder": {"body": "<path fill=\"currentColor\" d=\"M0 7V1h3l1 1h4v5\"/>"}, "folder2": {"body": "<path fill=\"currentColor\" d=\"M2 3L0 7l2-3h5l.5-1M0 7V1h3l1 1h4L6 7\"/>"}, "folders": {"body": "<path fill=\"currentColor\" d=\"M2 8V7h5V4h1v4M0 6V1h3l1 1h2v4\"/>"}, "football": {"body": "<path fill=\"currentColor\" d=\"M3 8q2.5-4 .5-3Q1 7 4 3L0 2q12-.5 6 2L4 8M2 7L1 8L0 7l1-1m4-5q2 2 1-1\"/>"}, "forbidden": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m2-1v2h4V3\"/>"}, "forest": {"body": "<path fill=\"currentColor\" d=\"M6 8H5V3L4 2l1-2l2 2H6l2 2H6l2 2H6M3 8H2V6H0l2-2H0l2-2H1l1.5-2L4 2H3l2 2H3l2 2H3\"/>"}, "fort": {"body": "<path fill=\"currentColor\" d=\"M3 8V6c0-2 2-2 2 0v2h3V1H6v2H5V1H3v2H2V1H0v7\"/>"}, "forum": {"body": "<path fill=\"currentColor\" d=\"M2 6V5h5V1h1v6L7 6M0 5V0h6v4H1\"/>"}, "forward": {"body": "<path fill=\"currentColor\" d=\"M4 2H0v6h4L3 6H2V4h2v2l4-3l-4-3\"/>"}, "fox": {"body": "<path fill=\"currentColor\" d=\"M8 5Q5 6 4 8Q3 6 0 5l1-2l-1-3l3 2h2l3-2l-1 3M2 3l1 1V3m2 0v1l1-1M1 5q2 1 3 2q1-1 3-2M4 6l1-1l-1-1l-1 1\"/>"}, "fragile": {"body": "<path fill=\"currentColor\" d=\"M4 0H3v2h1l1 2V1H4m1 4v2l2 1H1l2-1V5L1 3V0h6v3\"/>"}, "fridge": {"body": "<path fill=\"currentColor\" d=\"M2 2V1h1v1M2 6V4h1v2M1 3.5V8h6V3.5M1 0v3h6V0\"/>"}, "frosty": {"body": "<path fill=\"currentColor\" d=\"M2 0h4v2H2M0 2h8v1H0m4 4L3 5l1-1l1 1M1 3C0 9 8 9 7 3M2 3h1v1H2m3-1h1v1H5\"/>"}, "fuel": {"body": "<path fill=\"currentColor\" d=\"m6 1l2 2v5H6V6Q3 6 4 2H1V1m4 1Q4 5 6 5M1.5 3C4 7-1 7 1.5 3\"/>"}, "fullscreen": {"body": "<path fill=\"currentColor\" d=\"M3 0v1H1v2H0V0m5 1V0h3v3H7V1m0 4v2H5v1h3V5M1 5v2h2v1H0V5\"/>"}, "function": {"body": "<path fill=\"currentColor\" d=\"M1 0h2v1H2v7H0V7h1M0 3h3v1H0m4 3l3-3v1L4 8m0-4l3 3v1L4 5\"/>"}, "gamepad": {"body": "<path fill=\"currentColor\" d=\"M5 5c4 6 4-7-1-3c-5-4-5 9-1 3M2 5L1 4l1-1l1 1m3 1L5 4l1-1l1 1\"/>"}, "gamesave": {"body": "<path fill=\"currentColor\" d=\"m3 2l1.5-1L6 2M1 0v6q0 2 2 2h3q2 0 2-2V0\"/>"}, "gas": {"body": "<path fill=\"currentColor\" d=\"M1 0h5v7l1 1H0l1-1m1-4h3V1H2m4 4h1V3L6 2V1h2v5H6\"/>"}, "gdrive": {"body": "<path fill=\"currentColor\" d=\"M3 3L1 7L0 5l2-4m5 4L6 7H2l1-2m0-5h2l2 4H5\"/>"}, "genie": {"body": "<path fill=\"currentColor\" d=\"M8 8c0-4-4-1-3-4.5L2 4C1 8 8 6 8 8M1 4c-3-3 9-3 5 0M4 2C1-1 7-1 4 2\"/>"}, "gentleman": {"body": "<path fill=\"currentColor\" d=\"M4 0L3 1l1 1l1-1M3 8V5H2V2h4v3H5v3\"/>"}, "geometric": {"body": "<path fill=\"currentColor\" d=\"M0 8V0l8 8M1 7h5L1 2m2 0c3-3 7 1 4 4m0-1c1-3-2-4-3-3\"/>"}, "ghost": {"body": "<path fill=\"currentColor\" d=\"M5 2v1h1V2M3 2H2v1h1m5 5L6 7L4 8L2 7L0 8C-1-3 9-3 8 8\"/>"}, "gif": {"body": "<path fill=\"currentColor\" d=\"M0 2h3v1H1v3h1V4h1v3H0m4-5h1v5H4m2-5h2v1H7v1h1v1H7v2H6\"/>"}, "girl": {"body": "<path fill=\"currentColor\" d=\"M4 6L2 5h4M5 4V3h1v1M2 4V3h1v1M2 2c-4 7 8 7 4 0m1-2c5 11-11 11-6 0m3 1L2 0L1 2m3-1l3 1l-1-2\"/>"}, "git": {"body": "<path fill=\"currentColor\" d=\"M4 8L0 4l2-2l1 1v3h1V4l1 1l1-1l-3-3l1-1l4 4\"/>"}, "github": {"body": "<path fill=\"currentColor\" d=\"M5.5 8C9 8 9 0 4 0s-5 8-1 8L1 6V5l2 1l.5-1Q1 4 2 2V1l1 .5h2L6 1v1q1 2-1.5 3l1 1\"/>"}, "gitlab": {"body": "<path fill=\"currentColor\" d=\"M4 8L0 4l1-4zl3-8l1 4M.5 3L4 8l3.5-5\"/>"}, "glass": {"body": "<path fill=\"currentColor\" d=\"m1 1l1 2h4l1-2M2 8L0 0h8L6 8\"/>"}, "golf": {"body": "<path fill=\"currentColor\" d=\"M0 8V7h1v1m2 0h3l1-2H2M0 1.5L4 0h1v7H4V3\"/>"}, "golfer": {"body": "<path fill=\"currentColor\" d=\"M0 8V7l1-1l1-2H0V0h7v.5H0L1 3h2l1 1l-1 2l1 2H3L2 6L1 7v1m6 0V7h1v1M4 3L3 2l1-1l1 1\"/>"}, "gondola": {"body": "<path fill=\"currentColor\" d=\"M5 1v4H3V1M1 0v1h6V0M1 3v2h6V3M1 8L0 7V3l1-1h6l1 1v4L7 8\"/>"}, "google": {"body": "<path fill=\"currentColor\" d=\"M6.25 1.75C4 0 1 1.5 1 4c0 4 6 4 6 0h1c0 5.5-8 5.5-8 0c0-4 5-5 7-3M4 4h3v1H4\"/>"}, "gps": {"body": "<path fill=\"currentColor\" d=\"M0 5V3h1l2-2V0h2v1l2 2h1v2H7L5 7v1H3V7L1 5m3 1c2.5 0 2.5-4 0-4S1.5 6 4 6m0-1L3 4l1-1l1 1\"/>"}, "graph": {"body": "<path fill=\"currentColor\" d=\"M0 8V0h1v7h7v1M0 5l3-3l2 2l3-3v1L5 5L3 3L0 6\"/>"}, "grocery": {"body": "<path fill=\"currentColor\" d=\"M6 0c6 10-10 11-4 0h1v2h2V0\"/>"}, "group": {"body": "<path fill=\"currentColor\" d=\"M8 6v2H5V6L4 4l1-1V1L4 0h3l1 1v2L7 4M1 4L0 3V1l1-1h2l1 1v2L3 4l1 2v2H0V6\"/>"}, "gsm0": {"body": "<path fill=\"currentColor\" d=\"M2 7h5V2M0 8l8-8v8\"/>"}, "gsm1": {"body": "<path fill=\"currentColor\" d=\"M4 5v2h3V2M0 8l8-8v8\"/>"}, "gsm2": {"body": "<path fill=\"currentColor\" d=\"M5 4v3h2V2M0 8l8-8v8\"/>"}, "gsm3": {"body": "<path fill=\"currentColor\" d=\"M6 3v4h1V2M0 8l8-8v8\"/>"}, "gsm4": {"body": "<path fill=\"currentColor\" d=\"m0 8l8-8v8\"/>"}, "gui": {"body": "<path fill=\"currentColor\" d=\"M7 2H6v4h1M1 4v2h4V4M1 2v1h4V2M0 7V0h8v7\"/>"}, "guitar": {"body": "<path fill=\"currentColor\" d=\"M3 4v1h1V4m3-2L5 4q2 1-1 2c-1 5-7-1-2-2q1-3 2-1l2-2V0h2v2\"/>"}, "gun": {"body": "<path fill=\"currentColor\" d=\"M1 2h7v1H4v1L2 5v2H0m2-3h1V3H2\"/>"}, "gyrophare": {"body": "<path fill=\"currentColor\" d=\"M0 8V7h8v1M5 5V3L4 2v4M1 6l1-4h4l1 4\"/>"}, "hammer": {"body": "<path fill=\"currentColor\" d=\"M8 7L7 8L2 3v2L0 3l2-1q1-2 5-1q-4 0-3 2\"/>"}, "handbag": {"body": "<path fill=\"currentColor\" d=\"M2 4V0h4v4H5V1H3v3M1 3v4h6V3M0 8V2h8v6\"/>"}, "handle": {"body": "<path fill=\"currentColor\" d=\"M1 3v2h2V3M1 0v2h2V0m2 0v2h2V0M5 3v2h2V3M5 6v2h2V6M1 6v2h2V6\"/>"}, "handle2": {"body": "<path fill=\"currentColor\" d=\"M3 6v2h2V6M3 3v2h2V3m1-3v2h2V0M6 3v2h2V3M6 6v2h2V6M0 6v2h2V6\"/>"}, "happy": {"body": "<path fill=\"currentColor\" d=\"M2 5h4L5 6H3m2-2V2h1v2M0 8h8V0H0m2 4V2h1v2M1 7V1h6v6\"/>"}, "hash": {"body": "<path fill=\"currentColor\" d=\"M1 6V5h6v1M1 3V2h6v1M5 7V1h1v6M2 7V1h1v6\"/>"}, "haskell": {"body": "<path fill=\"currentColor\" d=\"M8 4.5H6.5L6 4h2m0-1H5l.5.5H8M2 6l2-2l-2-2h1l4 4H6L4.5 4.5L3 6M0 6l2-2l-2-2h1l2 2l-2 2\"/>"}, "hd": {"body": "<path fill=\"currentColor\" d=\"M4 6V1h2l1 1v3L6 6M5 2v3h1V2M0 6V1h1v2h1V1h1v5H2V4H1v2\"/>"}, "head": {"body": "<path fill=\"currentColor\" d=\"M6 8H3V7Q1 7 1 5H0l1-2V2c3-5 10 1 5 4\"/>"}, "header": {"body": "<path fill=\"currentColor\" d=\"M8 6V5H0v1m8 2V7H0v1m7-5V1H1v2M0 4V0h8v4\"/>"}, "headphone": {"body": "<path fill=\"currentColor\" d=\"M3 5v3H0C-2-3 10-3 8 8H5V5h2c1-5-7-5-6 0\"/>"}, "headset": {"body": "<path fill=\"currentColor\" d=\"M3 4v2H0V4c0-5.5 8-5.5 8 0v4H4V7h3V6H5V4h2c0-4-6-4-6 0\"/>"}, "heart": {"body": "<path fill=\"currentColor\" d=\"m4 8l2-2c4-4-1-6-2-3c-1-3-6-1-2 3\"/>"}, "heartbroken": {"body": "<path fill=\"currentColor\" d=\"M4 8V5H3l1.5-3H6L5 4h1zl2-2c4-4-1-6-2-3c-1-3-6-1-2 3\"/>"}, "heavier": {"body": "<path fill=\"currentColor\" d=\"M7.5 4L6 1L4.5 4m-1 3L2 2L.5 7M7 5H5L4 4l2-4l2 4M3 8H1L0 7l2-6l2 6M0 2l4-2h4\"/>"}, "height": {"body": "<path fill=\"currentColor\" d=\"M3 5V3H1l3-3l3 3H5v2h2L4 8L1 5\"/>"}, "helicopter": {"body": "<path fill=\"currentColor\" d=\"M4 1v1h1V1m0 4h2q0-2-2-2m1-1q2 0 2 4H3L2 4H0V1l1 1m1 6L1 7h7L7 8M2 1L1 0h7L7 1\"/>"}, "hibernate": {"body": "<path fill=\"currentColor\" d=\"M4 0c5 0 5 8 0 8s-5-8 0-8v1C0 1 0 7 4 7s4-6 0-6M3 2h2v4H3\"/>"}, "hierarchy": {"body": "<path fill=\"currentColor\" d=\"M8 8H6V6h2M2 8H0V6h2m3-4H3V0h2m2 5H6V4H2v1H1V3h6\"/>"}, "highlight": {"body": "<path fill=\"currentColor\" d=\"M1 7v1h7V7M3 6H1V4l4-4l2 2\"/>"}, "highlighter": {"body": "<path fill=\"currentColor\" d=\"M4 7L2 5l1-2l3 3M3 7L2 8H0l2-2m4.5-.5l-3-3L6 0h2v4\"/>"}, "hiking": {"body": "<path fill=\"currentColor\" d=\"M4 3L3 5l1 1v2H3V6L2 5L1 8H0l2-6h2l1-1l-1-1l-1 1l2 2h1v1H5m2-2L6 8H5l1-6M0 2h1v2H0\"/>"}, "hockey": {"body": "<path fill=\"currentColor\" d=\"M6 7v1h2V7M7 1L4 8H0V6h3l3-6\"/>"}, "horn": {"body": "<path fill=\"currentColor\" d=\"M3 6c-4 1-4-5 0-4q3 3 5 0v4Q6 3 3 6\"/>"}, "horse": {"body": "<path fill=\"currentColor\" d=\"M4 3q1-3 2-3l2 1v1H6q2 2 0 3v3H5V5L3 6v2H2V6L1 4v1L0 6q0-3 2-3\"/>"}, "hospital": {"body": "<path fill=\"currentColor\" d=\"M2 6V5h1V4h2v1h1v1H5v1H3V6M0 3l4-2l4 2V2L4 0L0 2m0 6h8V4L4 2L0 4\"/>"}, "hotel": {"body": "<path fill=\"currentColor\" d=\"M0 6h8v1H0m1-2C0 1 8 1 7 5M4 0l1 1l-1 1l-1-1\"/>"}, "hourglass0": {"body": "<path fill=\"currentColor\" d=\"M2 6v1h4V6L4 4m0 0l2-2V1H2v1M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass1": {"body": "<path fill=\"currentColor\" d=\"M2 6v1h4V6L4 4M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass2": {"body": "<path fill=\"currentColor\" d=\"M2 6v1h4V6L4 4m2-2V1H2v1M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass3": {"body": "<path fill=\"currentColor\" d=\"M2 6h4L4 4m2-2V1H2v1M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass4": {"body": "<path fill=\"currentColor\" d=\"M3 5h2L4 4m1-1l1-1V1H2v1l1 1M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass5": {"body": "<path fill=\"currentColor\" d=\"m4 4l2-2V1H2v1M1 8V6l2-2l-2-2V0h6v2L5 4l2 2v2\"/>"}, "hourglass6": {"body": "<path fill=\"currentColor\" d=\"M4 4L3 1L1 3m0 1L0 3l3-3l1 1l.5 2.5L7 4l1 1l-3 3l-1-1l-.5-2.5\"/>"}, "hourglass7": {"body": "<path fill=\"currentColor\" d=\"M2 2H1v4h1l2-2m0 1L2 7H0V1h2l2 2l2-2h2v6H6\"/>"}, "hourglass8": {"body": "<path fill=\"currentColor\" d=\"M4 4L1 5l2 2m1 0L3 8L0 5l1-1l2.5-.5L4 1l1-1l3 3l-1 1l-2.5.5\"/>"}, "house": {"body": "<path fill=\"currentColor\" d=\"M1 8h2V5h2v3h2V4h1L4 0L0 4h1\"/>"}, "html": {"body": "<path fill=\"currentColor\" d=\"m1 7l3 1l3-1l1-7H0m1 1h6v1H2.5L3 3h3.5L6 6H2V5h3V4H2\"/>"}, "icecream": {"body": "<path fill=\"currentColor\" d=\"M5.5 4Q9 3 6 2c1-2-5-2-4 0q-3 1 .5 2L4 8M3 4h2L4 7\"/>"}, "icicles": {"body": "<path fill=\"currentColor\" d=\"M8 1q0 13-2 1H5q-.5 5-1 0H3q-.5 10-1 0H1Q0 8 0 1\"/>"}, "inbox": {"body": "<path fill=\"currentColor\" d=\"M2 1L1 6h1l1 1h2l1-1h1L6 1M0 8V5l1-5h6l1 5v3\"/>"}, "incognito": {"body": "<path fill=\"currentColor\" d=\"M4 8L1 7V4l1 1h1l1-1l1 1h1l1-1v3m1-3H0l2-2V1h4v1\"/>"}, "indent": {"body": "<path fill=\"currentColor\" d=\"M8 6v1H0V6m8-2v1H3V4m-1-.5L0 5V2m8 0v1H3V2m5-2v1H0V0\"/>"}, "infinity": {"body": "<path fill=\"currentColor\" d=\"M4 2c7-1 4 7 0 3c-4-5-4 1-1 0l1 1c-7 1-4-7 0-3c4 5 4-1 1 0\"/>"}, "info": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m3-1v4h2V3M3 1v1h2V1\"/>"}, "instagram": {"body": "<path fill=\"currentColor\" d=\"m5 2l1-1l1 1l-1 1M0 8V0h8v8M1 1v6h6V1M2 4l2 2l2-2l-2-2m0 3L3 4l1-1l1 1\"/>"}, "ipphone": {"body": "<path fill=\"currentColor\" d=\"M8 8H3V0h5M7 3V1H4v2m0 1v1h3V4M4 6v1h3V6M0 8V2h2v6m3-1V4h1v3\"/>"}, "italic": {"body": "<path fill=\"currentColor\" d=\"M2 2V1h5v1H5L4 6h2v1H1V6h2l1-4\"/>"}, "jackhammer": {"body": "<path fill=\"currentColor\" d=\"M0 2V1h8v1M3.5 8L3 7l.5-1V4L2 3V0h4v3L4.5 4v2L5 7l-.5 1\"/>"}, "java": {"body": "<path fill=\"currentColor\" d=\"M4 3Q1 0 5 0Q2 1 4 3m3 0q2 0 0 3V4q-9 0-5-2q-3 2 5 1M5 6Q0 7 2 4Q1 6 6 5M5 8q-7 0-4-2q-1 2 7 1M5 3q0-2 2-2q-3 0-2 2\"/>"}, "jean": {"body": "<path fill=\"currentColor\" d=\"M1 8V0h6v8H5c0-8-2-8-2 0\"/>"}, "jerrican": {"body": "<path fill=\"currentColor\" d=\"M0 1h4l1 1l1-1h2v1H7L6 3l1 1v4H0m1-5h3L3 2H1\"/>"}, "jewelry": {"body": "<path fill=\"currentColor\" d=\"M3 4c-4 4 6 4 2 0m0-1c7 7-9 7-2 0m1 1L2 1l2-1l2 1\"/>"}, "join": {"body": "<path fill=\"currentColor\" d=\"m4 0l3 3H5v1l3 3v1H6V7L4 5L2 7v1H0V7l3-3V3H1\"/>"}, "js": {"body": "<path fill=\"currentColor\" d=\"m1 7l3 1l3-1l1-7H0m3 1v5H1.5L1 4l1 1V1m2 0h3v1H5v1h1.5L6 6H4V5h1V4H4\"/>"}, "json": {"body": "<path fill=\"currentColor\" d=\"M3 1C1 1 3 4 1 4c2 0 0 3 2 3v1C0 8 3 5 0 4c3-1 0-4 3-4m2 0c3 0 0 3 3 4c-3 1 0 4-3 4V7c2 0 0-3 2-3c-2 0 0-3-2-3\"/>"}, "jujutsu": {"body": "<path fill=\"currentColor\" d=\"M1 0v3L0 8h3l1-2l1 2h3L7 3V0M1 3V2h6v1M5 2L3 6L2 5l2-3m0 0l2 2v2L4 3\"/>"}, "jump": {"body": "<path fill=\"currentColor\" d=\"M2 5h2L2 2l4-2l2 2v1L6 1L4 2l2 3v1H3v2H2M1 0l1 1l-1 1l-1-1\"/>"}, "justify": {"body": "<path fill=\"currentColor\" d=\"M8 6v1H0V6m8-2v1H0V4m8-2v1H0V2m8-2v1H0V0\"/>"}, "kaaba": {"body": "<path fill=\"currentColor\" d=\"M4 0L0 1.5L4 3l4-1.5m-8 0l4-1l4 1V7L4 8L0 7m7-2H6v2h1\"/>"}, "kart": {"body": "<path fill=\"currentColor\" d=\"M0 5h1V4h1l1 2h4L6 3L5 4h1l2 2v1Q7 9 6 7H3Q2 9 1 7H0\"/>"}, "key": {"body": "<path fill=\"currentColor\" d=\"M2 2H1v1h1m2 1c-5 7-5-8 0-1h4v3L6 5V4\"/>"}, "keyboard": {"body": "<path fill=\"currentColor\" d=\"M2 1v1h1V1m3 0v1h1V1M4 1v1h1V1m0 2v1h1V3M3 3v1h1V3M1 3v1h1V3m0 2v1h4V5\"/>"}, "kitten": {"body": "<path fill=\"currentColor\" d=\"M3 5V4H2v1m2 2l1-1H3m2-1h1V4H5m3 1c0 4-8 4-8 0V0l3 2h2l3-2\"/>"}, "kotlin": {"body": "<path fill=\"currentColor\" d=\"m1 8l3-3l3 3M0 5V1h4M0 8V6l5-5h2\"/>"}, "lab": {"body": "<path fill=\"currentColor\" d=\"M1 8L0 7l3-5l-2-2h6L5 2l3 5l-1 1\"/>"}, "label": {"body": "<path fill=\"currentColor\" d=\"M1 3h2V1H1m3 7L0 4V0h4l4 4\"/>"}, "ladder": {"body": "<path fill=\"currentColor\" d=\"M6 8V7H2v1m4-4V3H2v1m4 2V5H2v1m4-4V0H2v2M1 8V0h6v8\"/>"}, "lady": {"body": "<path fill=\"currentColor\" d=\"M4 0L3 1l1 1l1-1M3 8V6H2l1-3l-1 2H1l2-3h2l2 3H6L5 3l1 3H5v2\"/>"}, "lamp": {"body": "<path fill=\"currentColor\" d=\"M6 6V4h1v2M2 8l1-1V4h2v3l1 1M0 4l2-4h4l2 4\"/>"}, "landing": {"body": "<path fill=\"currentColor\" d=\"M0 7h8v1H0m0-6l1-2l1 1v1l2 1l1-2l1 1v2l2 1v1H7L1 3\"/>"}, "landscape": {"body": "<path fill=\"currentColor\" d=\"M3 3H2V2h1m0 3l3-3l2 4H0l2-2\"/>"}, "latex": {"body": "<path fill=\"currentColor\" d=\"M5 8H4l1.5-3L4 2h1l1 2l1-2h1L6.5 5L8 8H7L6 6M2 5V1H0V0h5v1H3v4m0 1v1H0V2h1v4\"/>"}, "laudry": {"body": "<path fill=\"currentColor\" d=\"M7 4c0-4-6-4-6 0s6 4 6 0M0 0h8v8H0m2-3c0 2 5 1 4-1\"/>"}, "leaf": {"body": "<path fill=\"currentColor\" d=\"M0 7q0-7 8-7q0 8-7 8l5-6\"/>"}, "left": {"body": "<path fill=\"currentColor\" d=\"M5 2L3 4l2 2l-1 1l-3-3l3-3\"/>"}, "leftdouble": {"body": "<path fill=\"currentColor\" d=\"M8 2L6 4l2 2l-1 1l-3-3l3-3M4 2L2 4l2 2l-1 1l-3-3l3-3\"/>"}, "leftmost": {"body": "<path fill=\"currentColor\" d=\"M1 1v6h1V1m4 1L4 4l2 2l-1 1l-3-3l3-3\"/>"}, "leftward": {"body": "<path fill=\"currentColor\" d=\"M4 2h4v4H4v2L0 4l4-4\"/>"}, "lego": {"body": "<path fill=\"currentColor\" d=\"M2 5h4L5 6H3m2-2V3h1v1M0 6q0 2 2 2h4q2 0 2-2V3q0-2-2.5-2V0h-3v1Q0 1 0 3m2 1V3h1v1\"/>"}, "letter": {"body": "<path fill=\"currentColor\" d=\"M4 5L2 4V2h4v2m2-1L4 1L0 3v4h8\"/>"}, "lifter": {"body": "<path fill=\"currentColor\" d=\"M1 1h2l1 4H1m2-5H0v8l1-1l1 1h2l1-3m0 3V1h1v6h1l1 1\"/>"}, "ligature": {"body": "<path fill=\"currentColor\" d=\"M0 8V7h3v1M0 4V3h6v4H5V4M1 7V2C1-.5 6-.5 6 2H5C5 .5 2 .5 2 2v5m5 0v1H4V7\"/>"}, "lightbulb": {"body": "<path fill=\"currentColor\" d=\"M3 8c1-4-4-6 1-8c5 2 0 4 1 8M3 6v1h2V6\"/>"}, "lighter": {"body": "<path fill=\"currentColor\" d=\"M2 4L0 2l1-1l2 2m0 5V3h2v1h1v4M4 3c3-4-3-4 0 0c-1-3 1-3 0 0\"/>"}, "linkedin": {"body": "<path fill=\"currentColor\" d=\"M3 8V2h2v1q2-2 3 0v5H6V4L5 5v3M0 8V3h2v5M0 1l1-1l1 1l-1 1\"/>"}, "linux": {"body": "<path fill=\"currentColor\" d=\"M4 3L3 2h2M2 7V6h4v1M3 4h2v4q3 0 1-4l2 1l-2-3c0-3-4-3-4 0L0 5l2-1Q0 8 3 8\"/>"}, "list": {"body": "<path fill=\"currentColor\" d=\"M3 8V6h5v2M3 5V3h5v2M0 8V6h2v2M0 5V3h2v2M0 2V0h2v2m1 0V0h5v2\"/>"}, "list2": {"body": "<path fill=\"currentColor\" d=\"M3 3v1h4V3M3 1v1h4V1M3 5v1h4V5M1 5v1h1V5M1 3v1h1V3M1 1v1h1V1\"/>"}, "localisation": {"body": "<path fill=\"currentColor\" d=\"M3 5V3h2v2M2 2v4h4V2M0 4l4-4l4 4l-4 4\"/>"}, "lock": {"body": "<path fill=\"currentColor\" d=\"M2 3V0h4v3H5V1H3v2M1 8V3h6v5\"/>"}, "log": {"body": "<path fill=\"currentColor\" d=\"M1 3V0h1v3M1 8V6h1v2m1-5v3H0V3m4 2v1h4V5M4 3v1h4V3M4 7v1h4V7M1 4v1h1V4\"/>"}, "login": {"body": "<path fill=\"currentColor\" d=\"M3 0h5v8H3l1-1h3V1H4M0 3h2V1l3 3l-3 3V5H0\"/>"}, "logout": {"body": "<path fill=\"currentColor\" d=\"M4 0H0v8h4L3 7H1V1h2m0 2h2V1l3 3l-3 3V5H3\"/>"}, "loop": {"body": "<path fill=\"currentColor\" d=\"M6.5 3L8 5H7v2H1V6h5V5H5M0 3h1V1h6v1H2v1h1L1.5 5\"/>"}, "louder": {"body": "<path fill=\"currentColor\" d=\"M5 6q2-2 0-4m1 6q2-4 0-8q4 4 0 8M0 5V3h2l2-2v6L2 5\"/>"}, "ltr": {"body": "<path fill=\"currentColor\" d=\"M5 6v1H0V6m7-2v1H0V4m4-2v1H0V2m6-2v1H0V0\"/>"}, "lua": {"body": "<path fill=\"currentColor\" d=\"M6 2c3.5 3.5-2 8-5 5c-3-4 2-8 5-5H4v2h2V0h2v2\"/>"}, "luggage": {"body": "<path fill=\"currentColor\" d=\"M6 3h1v3H6M1 3h1v3H1m1 2h1V1h2v7h1V0H2M0 7h8V2H0\"/>"}, "magic": {"body": "<path fill=\"currentColor\" d=\"M1 2v.5h1v-1M0 3V2l8-2v1M2 8V5L1 4h6L6 5v3\"/>"}, "magnet": {"body": "<path fill=\"currentColor\" d=\"M5 1V0h2v1M0 1V0h2v1m5 4c0 4-7 4-7 0V2h2v2c0 2.5 3 2.5 3 0V2h2\"/>"}, "magnifier": {"body": "<path fill=\"currentColor\" d=\"M4 5C2 7-1.5 4 1 1c3-2.5 6 1 4 3l3 3l-1 1M1.5 1.5C0 3 2 6 4 4S3 0 1.5 1.5\"/>"}, "mail": {"body": "<path fill=\"currentColor\" d=\"m0 3l4-2l4 2l-4 2m4 2V3L4 4L0 3v4\"/>"}, "maki": {"body": "<path fill=\"currentColor\" d=\"M4 4.5L2 4l2-.5L6 4M2 3Q0 4 2 5h4q2-1 0-2m2 4Q4 9 0 7V3q4-2 8 0\"/>"}, "male": {"body": "<path fill=\"currentColor\" d=\"M2.5 8C-1 8-1 3 2.5 3s3.5 5 0 5M1 5.5c0 2 3 2 3 0s-3-2-3 0M3 4l3-3H4V0h4v4H7V2L4 5\"/>"}, "man": {"body": "<path fill=\"currentColor\" d=\"M2 2c-4 8 8 8 4 0M3 7C-7-2 15-2 5 7m0-4h1v1H5M2 3h1v1H2m1 1.5h2V6H3\"/>"}, "map": {"body": "<path fill=\"currentColor\" d=\"M6 7V3L3 1v4M0 8V2l3-2l3 2l2-2v6L6 8L3 6\"/>"}, "markdown": {"body": "<path fill=\"currentColor\" d=\"M6 3V1h1v2h1L6.5 6L5 3M0 6V1h1l1.5 1L4 1h1v5H4V3L2.5 4L1 3v3\"/>"}, "marker": {"body": "<path fill=\"currentColor\" d=\"M7 3c1-4-7-4-6 0l3 5M3 3l1-1l1 1l-1 1\"/>"}, "matrix": {"body": "<path fill=\"currentColor\" d=\"M3 6H2V2h4v4H5V3L4 6L3 3m5-3v8H6V7h1V1H6V0M1 1v6h1v1H0V0h2v1\"/>"}, "medical": {"body": "<path fill=\"currentColor\" d=\"M3 1h2v1H3m0 3H2V4h1V3h2v1h1v1H5v1H3M0 7h8V2H6V0H2v2H0\"/>"}, "medication": {"body": "<path fill=\"currentColor\" d=\"M2 0h4v1H2M1 4q0-2 2-2h2q2 0 2 2v4H1m2-3H2v1h1v1h2V6h1V5H5V4H3\"/>"}, "meditation": {"body": "<path fill=\"currentColor\" d=\"m3 1l1-1l1 1l-1 1M2 2h4v1l2 2v1L6 4v2l2 1v1H3V7l2-1l-3 1v1H0V7l2-1V4L0 6V5l2-2\"/>"}, "medium": {"body": "<path fill=\"currentColor\" d=\"M2 6C-.5 6-.5 2 2 2s2.5 4 0 4m3.5 0C4 6 4 2 5.5 2s1.5 4 0 4m2 0C7 6 7 2 7.5 2s.5 4 0 4\"/>"}, "megaphone": {"body": "<path fill=\"currentColor\" d=\"m2 3l6-3v7L5 5Q2 7 2 4L0 6V1m3 3v1h1V4\"/>"}, "menu": {"body": "<path fill=\"currentColor\" d=\"M0 1V0h8v1M0 7V6h8v1M0 4V3h8v1\"/>"}, "merge": {"body": "<path fill=\"currentColor\" d=\"M2 5c4 4-5 4-1 0V3c-4-4 5-4 1 0M1 6v1h1V6m4 0v1h1V6M1 1v1h1V1m0 2L1 2l6 3c4 4-5 4-1 0\"/>"}, "message": {"body": "<path fill=\"currentColor\" d=\"M6 6H0V1h8v7\"/>"}, "messenger": {"body": "<path fill=\"currentColor\" d=\"M3 7q5 1 5-3q0-3-4-3q-5 1-3 5L0 8m3-4L1 5l2-2l2 1l2-1l-2 2\"/>"}, "metronome": {"body": "<path fill=\"currentColor\" d=\"M7 8H1l2-8h2M4 7l3-7H6L3 7\"/>"}, "microphone": {"body": "<path fill=\"currentColor\" d=\"M3 5V1h2v4M1 6V4l1-1v3h4V3l1 1v2L5 7v1H3V7\"/>"}, "microscope": {"body": "<path fill=\"currentColor\" d=\"M1 6V5h4v1M4 3V0H2v3m1-1c4 0 4 5 0 5v1c5 0 5-7 0-7\"/>"}, "microwave": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v6H7v1H6V7H2v1H1V7H0m1-1h4V2H1m6 0H6v1h1M6 5h1V4H6\"/>"}, "milk": {"body": "<path fill=\"currentColor\" d=\"M2 0h4v1H5q0 2 3 2v1H7v4H1V4H0V3q3 0 3-2H2\"/>"}, "monitor": {"body": "<path fill=\"currentColor\" d=\"M6 4V3h1v1M1 4V3h1v1m1 1v1h1V5m1 0q0 2-1.5 2Q0 6 5 2M1 7h6l1-2c0-7-8-7-8 0m3-4h1v1H3\"/>"}, "moon": {"body": "<path fill=\"currentColor\" d=\"M5 8c-6 0-6-8 0-8c-3 0-3 8 0 8\"/>"}, "mouse": {"body": "<path fill=\"currentColor\" d=\"M1 3c0-4 6-4 6 0v2c0 4-6 4-6 0m3-4Q2 2 4 4\"/>"}, "move": {"body": "<path fill=\"currentColor\" d=\"M1 5V3h5v2M5 2v4h1V2M2 2v4h1V2m1 6L0 4l4-4l4 4\"/>"}, "movie": {"body": "<path fill=\"currentColor\" d=\"M0 4V2l8-2v2M0 8V4h8v4\"/>"}, "mr": {"body": "<path fill=\"currentColor\" d=\"M8 7H5V4h3M6 4V2H5v1L3 1.5L5 0v1h2v3M1 8V3h1v5m1-5H0V0h3m3 5v1h1V5M1 1v1h1V1\"/>"}, "msoffice": {"body": "<path fill=\"currentColor\" d=\"m0 6l6 1V1.5l-4 1V5zV2l6-2l2 1v6L6 8\"/>"}, "muaythai": {"body": "<path fill=\"currentColor\" d=\"M3 7V5L1 3l1-1h4V0l1 1v2H5v1h2v3L6 6V5H4v3m0-6c2-1 0-3-1-1m0 2H2l1 1\"/>"}, "muffin": {"body": "<path fill=\"currentColor\" d=\"M1 3.5L0 2l2-2l3 1l2-1l1 2l-1 1.5M2 8L1 4h6L6 8\"/>"}, "mug": {"body": "<path fill=\"currentColor\" d=\"M5 2v1h1V2M0 8V7h7v1M0 6V0h5v1c3 0 3 3 0 3v2\"/>"}, "mushroom": {"body": "<path fill=\"currentColor\" d=\"M3 4c-4 3-4-4 1-4s5 7 1 4l1 4H2\"/>"}, "music": {"body": "<path fill=\"currentColor\" d=\"M5 5c-5 0-2 6 1 1V2h2L6 0H5\"/>"}, "mute": {"body": "<path fill=\"currentColor\" d=\"M0 5V3h2l2-2v6L2 5m3-3h1l2 3H7m0-3h1L6 5H5\"/>"}, "na": {"body": "<path fill=\"currentColor\" d=\"M5 6V5h2v1M4 7l2-6l2 6H7L6 3L5 7M0 7V1h1l2 4V1h1v6H3L1 3v4\"/>"}, "nas": {"body": "<path fill=\"currentColor\" d=\"M1 1c1-1 5-1 6 0c-1 1.5-5 1.5-6 0m0 1q3 2 6 0v1Q4 5 1 3m0 1q3 2 6 0v1Q4 7 1 5M0 8V7h4V6l1-1v2h3v1\"/>"}, "navigation": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m6-2L1 4h3v3\"/>"}, "netflix": {"body": "<path fill=\"currentColor\" d=\"M1 0h2l4 8l-2-.5m-2 0L1 8V1l2 4m4 2L5 3V0h2\"/>"}, "network": {"body": "<path fill=\"currentColor\" d=\"M2 3V0h4v3M0 8V6h3v2m2 0V6h3v2M0 4v1h8V4M1 4v3h1V4m4 0v3h1V4M3 1v3h2V1\"/>"}, "newspaper": {"body": "<path fill=\"currentColor\" d=\"M7 6H4v1h3m0-3H3v1h4m0-4H6v2h1M5 1H3v2h2M2 7V0h6v7L7 8H1L0 7V1h2L1 2v5\"/>"}, "next": {"body": "<path fill=\"currentColor\" d=\"M7 8V0H5v8m0-4L1 0v8\"/>"}, "nfc": {"body": "<path fill=\"currentColor\" d=\"M6 8q2-4 0-8q4 4 0 8M4 7q2-3 0-6q4 3 0 6M1 6q-2-2 0-4l2 2V2q2 2 0 4L1 4\"/>"}, "ninja": {"body": "<path fill=\"currentColor\" d=\"M3 5C-1-.5 9-.5 5 5M1 8q3-6 6 0M2 2.5q2 1.5 4 0M3 8L0 3h1l3 5\"/>"}, "noentry": {"body": "<path fill=\"currentColor\" d=\"M4 8c-5 0-5-8 0-8s5 8 0 8m0-7C0 1 0 7 4 7s4-6 0-6M1 6l5-5l1 1l-5 5\"/>"}, "north": {"body": "<path fill=\"currentColor\" d=\"m2 5l2-2l2 2\"/>"}, "not": {"body": "<path fill=\"currentColor\" d=\"m1 7l6-6v1L2 7\"/>"}, "notes": {"body": "<path fill=\"currentColor\" d=\"M8 6C6 9 3 4 7 4V1.5L3 3v4c-2 3-5-2-1-2V2l6-2\"/>"}, "nswitch": {"body": "<path fill=\"currentColor\" d=\"M3 6H0V0h3m2 8V2h3v6M1 1v1h1V1m5 5H6v1h1\"/>"}, "numbered": {"body": "<path fill=\"currentColor\" d=\"M3 3v1h4V3M3 1v1h4V1M3 5v1h4V5M2 6H1l.5-.5L1 5h1m0-4h-.5v1H2M1 3l1 1H1l1-1\"/>"}, "oak": {"body": "<path fill=\"currentColor\" d=\"M5 8H3V5C-8-1 16-1 5 5\"/>"}, "octopus": {"body": "<path fill=\"currentColor\" d=\"M6 1v1h1V1M1 1v1h1V1m5 2q2 8-1 2q0 6-2 0q-2 6-2 0q-3 6-1-2c-5-4 11-4 6 0\"/>"}, "off": {"body": "<path fill=\"currentColor\" d=\"M1 4c0-2 3-2 3 0S1 6 1 4m4.5 2c3 0 3-4 0-4h-3c-3 0-3 4 0 4\"/>"}, "office": {"body": "<path fill=\"currentColor\" d=\"M0 8h7V0H0m1 6V5h5v1M1 2V1h5v1M3 8V6h1v2m1-2V1H4v5M3 6V1H2v5M1 4V3h5v1\"/>"}, "oil": {"body": "<path fill=\"currentColor\" d=\"M2 1h3v1H4v1h4v1H6L5 6H1V4H0V2h1l1 1h1V2H2m5 4l1-1v3L7 7\"/>"}, "oillamp": {"body": "<path fill=\"currentColor\" d=\"M3 2h5L6 0H5m1 5h1V4H6m0-1c3 0 3 3 0 3l2 2H2l2-2l-4-3\"/>"}, "ok": {"body": "<path fill=\"currentColor\" d=\"M1 8L0 6V3l2-3l1 1l-1 3l2-1l2 1l-1 1l-1-1l-1 2l2 1V5l1 1v1L5 8\"/>"}, "on": {"body": "<path fill=\"currentColor\" d=\"M4 4c0-2 3-2 3 0S4 6 4 4m1.5 2c3 0 3-4 0-4h-3c-3 0-3 4 0 4\"/>"}, "options": {"body": "<path fill=\"currentColor\" d=\"m4 6l1 1l-1 1l-1-1m1-4l1 1l-1 1l-1-1m2-3L4 2L3 1l1-1\"/>"}, "or": {"body": "<path fill=\"currentColor\" d=\"M0 6V0h6v2h2v6H2V6M1 1v4h2v2h4V3H5V1\"/>"}, "outbox": {"body": "<path fill=\"currentColor\" d=\"m2 4l2-2l2 2M2 1L1 6h1l1 1h2l1-1h1L6 1M0 8V5l1-5h6l1 5v3\"/>"}, "oven": {"body": "<path fill=\"currentColor\" d=\"M0 0h8v8H0m1-1h6V2H1m1 1h4v1H2M1 1l1 1l1-1l-1-1m1 1l1 1l1-1l-1-1m1 1l1 1l1-1l-1-1\"/>"}, "package": {"body": "<path fill=\"currentColor\" d=\"M0 4h3v2h2V4h3v4H0m2-8h1.5L3 3H0m4.5-3H6l2 3H5\"/>"}, "pagebreak": {"body": "<path fill=\"currentColor\" d=\"M1 3V0h6v3M2 0v2h4V0M0 5V4h2v1m1 0V4h2v1m1 0V4h2v1M1 8V6h6v2M2 7v1h4V7\"/>"}, "paintbrush": {"body": "<path fill=\"currentColor\" d=\"M0 8c3-7 8-2 3 0m2-4l2-4l1 1l-2 4\"/>"}, "palette": {"body": "<path fill=\"currentColor\" d=\"M8 3C8-2-6 2 3 7c9 0-2-3 3-4M2 2h1v1H2M1 4h1v1H1m3-4h1v1H4\"/>"}, "panorama": {"body": "<path fill=\"currentColor\" d=\"M0 1q4 2 8 0v6Q4 5 0 7m5-5L3 4L2 3L1 5h6\"/>"}, "panty": {"body": "<path fill=\"currentColor\" d=\"M0 2v1q3 1 3 3h2q0-2 3-3V2\"/>"}, "pause": {"body": "<path fill=\"currentColor\" d=\"M3 8V0H1v8m4 0V0h2v8\"/>"}, "pawn": {"body": "<path fill=\"currentColor\" d=\"M6 2q0-4 1 0M4 2q0-4 1 0M0 5q0-4 1 0m2 3c-5-5 8-9 3 0M2 2q0-4 1 0\"/>"}, "paycheck": {"body": "<path fill=\"currentColor\" d=\"M0 1h8v5H0m3-4H1v1l1 1H1v1h2V4L2 3h1m1 0h3V2H4\"/>"}, "paypal": {"body": "<path fill=\"currentColor\" d=\"M8 2q0 4-4 4v2H3V5q5 0 4-4M0 7V0h5q3 4-3 4v3\"/>"}, "pc": {"body": "<path fill=\"currentColor\" d=\"M8 8L7 6H1L0 8m6-4V1H2v3M1 5V0h6v5\"/>"}, "pen": {"body": "<path fill=\"currentColor\" d=\"M2 8H0V6m7-3L5 1l1-1l2 2M3 7L1 5l3-3l2 2\"/>"}, "perl": {"body": "<path fill=\"currentColor\" d=\"M7 4L6 8V4L5 8V4H4L3 8V4Q2 4 2 2q-3 0 0-1l1-1v3q2-4 5 1\"/>"}, "phone": {"body": "<path fill=\"currentColor\" d=\"M6 7V1H5v.5H3V1H2v6m0 1L1 7V1l1-1h4l1 1v6L6 8\"/>"}, "php": {"body": "<path fill=\"currentColor\" d=\"M3 5V1h1v2h1v2L4 4m3-1v2H6V1h2v2M1 3v2H0V1h2v2\"/>"}, "pi": {"body": "<path fill=\"currentColor\" d=\"m0 2l1-1h7v1H6v3c0 1 1 2 2 0c0 3-3 3-3 0V2H3v5H2V2\"/>"}, "piano": {"body": "<path fill=\"currentColor\" d=\"M0 6V3c0-4 5-4 5 0c1 2 3 0 3 3H7v1.5H1V6m1 1h4V6H2\"/>"}, "picker": {"body": "<path fill=\"currentColor\" d=\"M5 2L3 4.5l.5.5L6 3M1 8H0V7l5-6V0h3v3H7\"/>"}, "picture": {"body": "<path fill=\"currentColor\" d=\"M3 3H2V2h1m0 3l3-3l1 4H1l1-2m6 3V1H0v6\"/>"}, "pictures": {"body": "<path fill=\"currentColor\" d=\"m3 4l1-2l1 3H1l1-2m5 4V2h1v6H1V7m5-1V1H0v5\"/>"}, "pie": {"body": "<path fill=\"currentColor\" d=\"m4 4l3-3c1 1 2 7-3 7s-5-8 0-8\"/>"}, "pig": {"body": "<path fill=\"currentColor\" d=\"M6 3H5v1h1m2 1Q6 5 6 8H5Q3 6 1 8H0V5q0-3 4-3l2-2Q5 3 8 3\"/>"}, "piggy": {"body": "<path fill=\"currentColor\" d=\"M3 0C0 3 6 3 3 0m1 4H1v1h3m2-2H5v1h1m2 1Q6 4 6 8H5Q3 6 1 8H0V5q0-2 4-2l2-2q0 2 2 2\"/>"}, "pill": {"body": "<path fill=\"currentColor\" d=\"M3 2c4-4 7-1 3 3L5 6c-4 4-7 1-3-3m3 2c4-4 2-6-2-2\"/>"}, "pills": {"body": "<path fill=\"currentColor\" d=\"M2 6v.5h2V6M1 0v2h6V0M2 4v.5h2V4m2 4H2V2.5h4\"/>"}, "pin": {"body": "<path fill=\"currentColor\" d=\"M5 5L4 8V5H0q4-2 1-5h6Q4 3 8 5\"/>"}, "pine": {"body": "<path fill=\"currentColor\" d=\"M5 8H3V6H0l3-2H0l3-2H1l3-2l3 2H5l3 2H5l3 2H5\"/>"}, "piston": {"body": "<path fill=\"currentColor\" d=\"m2 3l3-3l3 3l-3 3V5l2-2l-2-2l-2 2m0 3v1L1 8L0 7l1-2h1l3-3l1 1M1 6v1h1V6\"/>"}, "pizza": {"body": "<path fill=\"currentColor\" d=\"M2 5v1h1V5M0 8L4.5.5q2.5.5 3 3M4 1q3 0 3 3l1-.5Q8 0 4.5 0M4 3v1h1V3\"/>"}, "play": {"body": "<path fill=\"currentColor\" d=\"M6 4L2 0v8\"/>"}, "playlist": {"body": "<path fill=\"currentColor\" d=\"M1 8V7h6V3h1v5M0 6V0h6v6M2 1v4l2.5-2\"/>"}, "playstation": {"body": "<path fill=\"currentColor\" d=\"M2 6v1q-4 0 0-3v1L1 6m4 1l1-1H5V5q4 0 0 3M4 8L3 7V0q5 0 2 4V2L4 1\"/>"}, "playstore": {"body": "<path fill=\"currentColor\" d=\"M7 5L5.5 4L7 3l1 1M6 5L0 8l5-3.5m0-1L0 0l6 3M0 8V0l4 4\"/>"}, "plugin": {"body": "<path fill=\"currentColor\" d=\"M0 8V6c2.5 0 2.5-2 0-2V2h2C2-.5 4-.5 4 2h2v2c2.5 0 2.5 2 0 2v2\"/>"}, "plunger": {"body": "<path fill=\"currentColor\" d=\"M1 7C0 3 8 3 7 7M3 6V0h2v6M0 8V7h8v1\"/>"}, "plus": {"body": "<path fill=\"currentColor\" d=\"M1 5V4h5v1M3 7V2h1v5\"/>"}, "plus1": {"body": "<path fill=\"currentColor\" d=\"M7 1L5 3V2l2-2h1v8H7M5 5H0V4h5M2 7V2h1v5\"/>"}, "podium": {"body": "<path fill=\"currentColor\" d=\"M0 6V3h2V1h4v2h2v3M4.5 4V2H3l1 1v1\"/>"}, "pokeball": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5.25 8-5.25 8 0S0 9.25 0 4m1 0c0 4 6 4 6 0H6Q4 1 2 4m1 0l1-1l1 1l-1 1\"/>"}, "post": {"body": "<path fill=\"currentColor\" d=\"M3 1V0h1v1M3 8V5h1v3M1 4V2h5l1 1l-1 1\"/>"}, "power": {"body": "<path fill=\"currentColor\" d=\"M6 1c3 0 3 7-2 7s-5-7-2-7v1C0 2 0 7 4 7s4-5 2-5M3 0h2v4H3\"/>"}, "pray": {"body": "<path fill=\"currentColor\" d=\"M4 0v2H2V0m0 8l1-1h3v-.5H2V2l1 1h2V1h1v3H4L3 5h3l1 1v2\"/>"}, "presentation": {"body": "<path fill=\"currentColor\" d=\"M1 6V2Q0 2 0 0h8q0 2-1 2v4M4 6C2 8 6 8 4 6\"/>"}, "pressure": {"body": "<path fill=\"currentColor\" d=\"M3 0h2v3H3m0 1h2v1H3M2 1c-4 7 8 7 4 0l1-1q3 4-1 8L5 7L4 8L3 7L2 8q-4-4-1-8\"/>"}, "previous": {"body": "<path fill=\"currentColor\" d=\"M3 8V0H1v8m2-4l4-4v8\"/>"}, "printer": {"body": "<path fill=\"currentColor\" d=\"M3 1v1h2V1M2 7V5h1v1h2V5h1v2m1-2V4H1v1H0V2h2V0h4v2h2v3\"/>"}, "profile": {"body": "<path fill=\"currentColor\" d=\"M4 8c-5.5 0-5.5-8 0-8s5.5 8 0 8m3-3Q4 3 1 5c0 3 6 3 6 0M4 0L2 2l2 2l2-2\"/>"}, "proposal": {"body": "<path fill=\"currentColor\" d=\"M5 6H3v2H0V7h2V2h5v1H4v2h2v3H5M3 2L2 1l1-1l1 1\"/>"}, "protect": {"body": "<path fill=\"currentColor\" d=\"m0 1l1 1v3l1 1V4l1 1v3H2L0 6m8-5v5L6 8H5V5l1-1v2l1-1V2M2 2l2-2l2 2l-2 2\"/>"}, "psp": {"body": "<path fill=\"currentColor\" d=\"M6 2c2.5 0 2.5 4 0 4H2C-.5 6-.5 2 2 2m0 3.5h4v-3H2M2 4L1 3L0 4l1 1m6 0l1-1l-1-1l-1 1\"/>"}, "puppy": {"body": "<path fill=\"currentColor\" d=\"M5 5L4 7L3 5l1-1m1-1V2h1v1M3 3H2V2h1m4 1L5 0h3M3 7h2l2-4l-2 5H3L1 3m0 0L0 0h3\"/>"}, "pyramid": {"body": "<path fill=\"currentColor\" d=\"m0 6l4-6l4 6l-4 2m0-7v6l3-1\"/>"}, "python": {"body": "<path fill=\"currentColor\" d=\"m7 2l1 1v2H4v1h2L5 7H3V4h4M2 1l1-1h3v3H2v3L1 5V2h4V1\"/>"}, "qrcode": {"body": "<path fill=\"currentColor\" d=\"M3 0v3h5V0M0 3v5h3V3M0 5V0h5v5m2 2V4H4v3m3-6v1H6V1M2 2V1H1v1m0 5V6h1v1\"/>"}, "quadcopter": {"body": "<path fill=\"currentColor\" d=\"M7 2L6 4v1l1 3H6L5 5L4 6L3 5L2 8H1l1-3V4L1 2h1v1h4V2m2 0H5V1h3M3 2H0V1h3\"/>"}, "question": {"body": "<path fill=\"currentColor\" d=\"M1 2h5v1H4v1h3V1H1m3 5h1v1H4\"/>"}, "quiet": {"body": "<path fill=\"currentColor\" d=\"M5 6q2-2 0-4M0 5V3h2l2-2v6L2 5\"/>"}, "rabbit": {"body": "<path fill=\"currentColor\" d=\"M7 8H5q1-3-2-3q2 1 1 3H0q0-5 4-5q0-1-3-1q3-2 4-1c4 0 4 4 0 3m1-1h1V2H6\"/>"}, "radio": {"body": "<path fill=\"currentColor\" d=\"M0 3h1l6-3h1L2 3h6v5H0m1-3h6V4H1m3 4l1.5-1.5L4 5L2.5 6.5\"/>"}, "radioactif": {"body": "<path fill=\"currentColor\" d=\"M3 5V3h2v2M2 8l2-4l2 4m2-4H4l2-4M4 4H0l2-4\"/>"}, "rain": {"body": "<path fill=\"currentColor\" d=\"M1 5C0 5-1 2 2 2c0-3 4-2 4-1c3 1 2 4 0 4M1 6h1v2H1m2-3h1v2H3m2-1h1v2H5\"/>"}, "rainbow": {"body": "<path fill=\"currentColor\" d=\"M0 7c0-8 8-8 8 0H5c1-3-3-3-2 0m4 0C7 .5 1 .5 1 7h1c0-5 4-5 4 0\"/>"}, "ram": {"body": "<path fill=\"currentColor\" d=\"M5 5V4H4v1H0V4q2-1 0-2V1h8v1Q6 3 8 4v1\"/>"}, "rat": {"body": "<path fill=\"currentColor\" d=\"M5 2c0-4 5 0 2 1c2 6-8 6-6 0c-3-1 2-5 2-1m0 2Q2 2 2 4m4 0q0-2-1 0M3 7h2V6H3\"/>"}, "razor": {"body": "<path fill=\"currentColor\" d=\"M0 3V1h8v2L3 8L2 7l5-5H4L3 3\"/>"}, "read": {"body": "<path fill=\"currentColor\" d=\"M4 2v5l3-1V1M1 1v5l2 1V2m5 5L3.5 8L0 7V0l3.5 1L8 0\"/>"}, "receive": {"body": "<path fill=\"currentColor\" d=\"m0 5l2-2q5 0 1 1q-2 1 2 1q6-4 0 2H0\"/>"}, "recycle": {"body": "<path fill=\"currentColor\" d=\"M1 6h6L4 1M0 6l3.5-6h1L8 6v1H0m4-3l3-3v3M3 5L0 2h3m2 3v3L3 6.5\"/>"}, "reddit": {"body": "<path fill=\"currentColor\" d=\"M5 1c3 3 2-2 1 0H4v2c-5 0-5 5 0 5s5-5 0-5M2 5c1-2 2 2 0 0c-1-3.5-4 0 0 1m4 0c4.5-1 0-4.5 0-1c-2 2-1-2 0 0\"/>"}, "redhat": {"body": "<path fill=\"currentColor\" d=\"M8 5C6 9-1 3 1 3q4 3 6 1m-.5 0Q5 5 2 3l.5-2Q4 2 4 1l2 1\"/>"}, "refresh": {"body": "<path fill=\"currentColor\" d=\"m7 3l1 2H7c0 4-6 4-6 1h1c0 2 4 2 4-1H5m2-3c0-3-6-3-6 1H0l1 2l2-2H2c0-3 4-3 4-1\"/>"}, "reload": {"body": "<path fill=\"currentColor\" d=\"M8 0v4H4l2-2c-6-5-7 9 0 4l1 1c-9 5-9-11 0-6\"/>"}, "repeat": {"body": "<path fill=\"currentColor\" d=\"M3 7h4V4H6v2H3V5L1 6.5L3 8m4-6.5L5 0v1H1v3h1V2h3v1\"/>"}, "repeat1": {"body": "<path fill=\"currentColor\" d=\"M3 7h4V4H6v2H3V5L1 6.5L3 8m0-4h1v1h1V3H4m3-1.5L5 0v1H1v3h1V2h3v1\"/>"}, "revert": {"body": "<path fill=\"currentColor\" d=\"M4 4V2h1v3H2l1-1M1 2c0-3 7-3 7 2S1 9 1 6h1c0 2 5 2 5-2S2 0 2 2h1L1.5 4L0 2\"/>"}, "revolver": {"body": "<path fill=\"currentColor\" d=\"m0 5l1-1V3L0 2h8v1H5v1L3 5v2H0m3-3h1V3H3\"/>"}, "rewind": {"body": "<path fill=\"currentColor\" d=\"m2 4l4-4v8\"/>"}, "rfid": {"body": "<path fill=\"currentColor\" d=\"M3 5h2V2H4v1H3M1 1v6h6V1M2 6V2h4v4\"/>"}, "rice": {"body": "<path fill=\"currentColor\" d=\"M1 3c0-4 6-4 6 0c-1-3-5-3-6 0m1 5l1-1Q0 6 0 3h8q0 3-3 4l1 1\"/>"}, "right": {"body": "<path fill=\"currentColor\" d=\"m3 2l2 2l-2 2l1 1l3-3l-3-3\"/>"}, "rightdouble": {"body": "<path fill=\"currentColor\" d=\"m0 2l2 2l-2 2l1 1l3-3l-3-3m3 1l2 2l-2 2l1 1l3-3l-3-3\"/>"}, "rightmost": {"body": "<path fill=\"currentColor\" d=\"M6 1v6h1V1M2 2l2 2l-2 2l1 1l3-3l-3-3\"/>"}, "rightward": {"body": "<path fill=\"currentColor\" d=\"M4 2H0v4h4v2l4-4l-4-4\"/>"}, "rise": {"body": "<path fill=\"currentColor\" d=\"m0 5l3-3l1 1l1.5-1.5L4 0h4v4L6.5 2.5L4 5L3 4L1 6\"/>"}, "rj": {"body": "<path fill=\"currentColor\" d=\"M0 2h2l1-2h2l1 2h2v6H0m1.5-1H2V5h-.5m3 2H5V5h-.5M6 7h.5V5H6M3 7h.5V5H3\"/>"}, "road": {"body": "<path fill=\"currentColor\" d=\"m0 8l2-8h3l2 8M4 3V1H3v2m1 4V5H3v2\"/>"}, "roadwork": {"body": "<path fill=\"currentColor\" d=\"M6 7V1h1v6M1 7V1h1v6M0 4V2h8v2\"/>"}, "robot": {"body": "<path fill=\"currentColor\" d=\"M3 5V4H2v1m4 0V4H5v1M0 7c0-7 8-7 8 0M4 4L1 0h1l2 3l2-3h1\"/>"}, "rocket": {"body": "<path fill=\"currentColor\" d=\"M3 4L1 7l3-2m2-3H5v1h1M3 6L0 8l2-3H0l1-2h1l2-2l4-1l-1 4l-2 2v1L3 8\"/>"}, "roll": {"body": "<path fill=\"currentColor\" d=\"M5 3c0-4 3-4 3 0S5 7 5 3m0-3C3 0 5 8 3 8H0c1 0 0-8 1-8m5 4h1V2H6\"/>"}, "roller": {"body": "<path fill=\"currentColor\" d=\"M6 2V1h2v4H4v3H2V4h5V2M0 3V0h6v3\"/>"}, "rollerblade": {"body": "<path fill=\"currentColor\" d=\"m1 6l2 2l2-2l2 2l1-1l-1-1l-2 2l-2-2l-2 2l-1-1m1-7h3v2q4 0 3 3H1\"/>"}, "rostrum": {"body": "<path fill=\"currentColor\" d=\"M2 8L1 2l1-2h1L2 2h5L6 0h1l1 2l-1 6M4 2V0h1v2\"/>"}, "router": {"body": "<path fill=\"currentColor\" d=\"M7 3c1-3-4-3-3 0c-4-4 7-4 3 0M6 6H5v1h1M4 6H3v1h1M2 6H1v1h1M0 8V5h5V2h1v3h2v3\"/>"}, "rss": {"body": "<path fill=\"currentColor\" d=\"M0 0q8 0 8 8H7q0-7-7-7m0 2q4 0 5 5H4Q3 4 0 4m0 4V6h2v2\"/>"}, "rtl": {"body": "<path fill=\"currentColor\" d=\"M8 6v1H3V6m5-2v1H1V4m7-2v1H4V2m4-2v1H2V0\"/>"}, "ruby": {"body": "<path fill=\"currentColor\" d=\"M3 1L1 3h1m3-2v2h2M2 3l2 3l1-3M4 7L0 3l2-2h4l2 2\"/>"}, "rugby": {"body": "<path fill=\"currentColor\" d=\"M0 7q0-7 7-7l1 1q0 7-7 7m5-7Q1 1 1 6m1 1q5 0 5-5\"/>"}, "run": {"body": "<path fill=\"currentColor\" d=\"M5 3L4 5h2v3H5V6H3v1H0V6h2l1-3H2v1H1V2h4l1-1l-1-1l-1 1l2 2h2v1H5\"/>"}, "sad": {"body": "<path fill=\"currentColor\" d=\"M3 5h2l1 1H2m3-2V2h1v2M0 8h8V0H0m2 4V2h1v2M1 7V1h6v6\"/>"}, "safe": {"body": "<path fill=\"currentColor\" d=\"M2 7v1H1V7H0V0h7v7H6v1H5V7M4 3c4-2-5-2-1 0v2h1\"/>"}, "satellite": {"body": "<path fill=\"currentColor\" d=\"m2 5l4-4l1 1l-4 4v2L0 5m2-3l4 4l1-1l-4-4M1 2l2-2l5 5l-2 2\"/>"}, "saw": {"body": "<path fill=\"currentColor\" d=\"M0 0h2l6 6l-2 2V6L4 7V5L2 6V4L0 5m1-4v2h1V1\"/>"}, "sax": {"body": "<path fill=\"currentColor\" d=\"M5 5L4 4h2m1 0L5 3h2M3 6h2l1-1l1 3H5L1 7V6l4-4l-1-1V0l3 2\"/>"}, "scale": {"body": "<path fill=\"currentColor\" d=\"M8 0c0 3-8 3-8 0m3 4V2h2v2M0 8c0-6 8-6 8 0M7 7c0-4-6-4-6 0m2 0L2 5l3 2\"/>"}, "scan": {"body": "<path fill=\"currentColor\" d=\"M8 4v1H0V4m3-4v1H1v2H0V0m5 1V0h3v3H7V1m0 5v1H5v1h3V6M1 6v1h2v1H0V6\"/>"}, "scanner": {"body": "<path fill=\"currentColor\" d=\"M1 8V7H0V5h8v2H7v1m1-3L0 1V0l8 4M2 5L1 6l1 1l1-1\"/>"}, "scooter": {"body": "<path fill=\"currentColor\" d=\"M6 8L5 7l1-1l1 1M3 1H0V0h3M2 8L1 7l1-1l1 1M1 6V1h1v4h5v1\"/>"}, "screen": {"body": "<path fill=\"currentColor\" d=\"M7 5V2H1v3m1 2l1-1H0V1h8v5H5l1 1\"/>"}, "screw": {"body": "<path fill=\"currentColor\" d=\"M3 6v1l2-2M3 4v1l2-2M3 2v1l2-2m0 6L4 8L3 7V1L1 0h6L5 1\"/>"}, "script": {"body": "<path fill=\"currentColor\" d=\"M2 8V2H1v2H0V0h7v5H3v2h1V6h4v2\"/>"}, "sd": {"body": "<path fill=\"currentColor\" d=\"M4 6V1h2l1 1v3L6 6M5 2v3h1V2M1 6L0 5h2L0 2l1-1h1l1 1H1l2 3l-1 1\"/>"}, "sdcard": {"body": "<path fill=\"currentColor\" d=\"M6 1v2h1V1M2 2v2h1V2m1-1v2h1V1M1 8V2l2-2h5v8\"/>"}, "seat": {"body": "<path fill=\"currentColor\" d=\"m5 6l2 2H2l2-2H2L1 0h2l1 2h2v1H4v1h3v2\"/>"}, "seatbelt": {"body": "<path fill=\"currentColor\" d=\"m3 1l1-1l1 1l-1 1m4-2v1L1 8H0m4-3h5v1H3m3 2c2-8-6-8-4 0m1-1h2v1H3\"/>"}, "selectbox": {"body": "<path fill=\"currentColor\" d=\"M0 3V0h8v3M4 1l1.5 2L7 1M2 5V4h4v1M2 7V6h4v1M1 3v5h6V3\"/>"}, "semicolon": {"body": "<path fill=\"currentColor\" d=\"M4 3V2h1v1m0 4H3l1-1V5h1\"/>"}, "send": {"body": "<path fill=\"currentColor\" d=\"m0 0l8 3.5L0 7l1-3q5-.5 0-1\"/>"}, "server": {"body": "<path fill=\"currentColor\" d=\"M0 8V6h8v2M5 7l1 1l1-1l-1-1M5 1l1 1l1-1l-1-1M5 4l1 1l1-1l-1-1M0 5V3h8v2M0 2V0h8v2\"/>"}, "serving": {"body": "<path fill=\"currentColor\" d=\"M1 4h7v.5H1M2 3c0-3 5-3 5 0M4 0h1v1H4M2 5h1l1 1l3-1h1L5 7v1H3V7\"/>"}, "sewing": {"body": "<path fill=\"currentColor\" d=\"M0 7V5h5V2H3v1H1V0h6v1h1v2H7v4\"/>"}, "shake": {"body": "<path fill=\"currentColor\" d=\"m4 4l1-1l-2-2H2L0 0v4l1 1l2-2m5 2v1L6 8H5M0 5v2l4 1l4-4V0L6 1L3 0l3 3l-2 2l-1-1l-2 2\"/>"}, "shark": {"body": "<path fill=\"currentColor\" d=\"m0 7l1-3l-1-3l2 2c4 1 0-5 4 0q5 2-4 2\"/>"}, "shaver": {"body": "<path fill=\"currentColor\" d=\"M0 3V0h8v3H5v5H3V3M1 1v1h6V1\"/>"}, "shield": {"body": "<path fill=\"currentColor\" d=\"M4 4v3q2 0 3-3M4 4V1L1 2v2m3-4l4 2c0 8-8 8-8 0\"/>"}, "ship": {"body": "<path fill=\"currentColor\" d=\"m7 6l1-2H3V2H1v2H0l1 2\"/>"}, "shoe": {"body": "<path fill=\"currentColor\" d=\"M2 6H1L0 3l1-2l4 3l3 1l-1 1H4L2 4\"/>"}, "shop": {"body": "<path fill=\"currentColor\" d=\"M2 1.5V1h4v.5M2 5v2h2V5M1 8V5H0l2-3h4l2 3H7v3H6V5H5v3\"/>"}, "shopping": {"body": "<path fill=\"currentColor\" d=\"M2 2V0h4v2H5V1H3v1m2 1v1h1V3M2 3v1h1V3M1 8L0 7V2h8v5L7 8\"/>"}, "shovel": {"body": "<path fill=\"currentColor\" d=\"M5 4L1 0L0 1l4 4l3 2M3 6l3-3l2 2v3H5\"/>"}, "shower": {"body": "<path fill=\"currentColor\" d=\"M4 6v1h4V6M4 4v1h4V4m0 4H7V4h1M6 8H5V3h1M1 8V1l2 1h2L3 5V2L2 3v5\"/>"}, "shrimp": {"body": "<path fill=\"currentColor\" d=\"M8 0q0 3-4 3C1 3 1 7 4 7l3-1l1 2H4c-5 0-5-7 0-8m1 2h1V1H5m1 1l2 2l-1-2.5L6 5\"/>"}, "shrine": {"body": "<path fill=\"currentColor\" d=\"M4.5 2L4 4l-.5-2M5 7V2h1v6M2 8V2h1v5M1 4V3h6v1M1 2L0 0l4 1l4-1l-1 2\"/>"}, "shrink": {"body": "<path fill=\"currentColor\" d=\"M8 3H5V0h1v2h2M2 8V6H0V5h3v3\"/>"}, "shuffle": {"body": "<path fill=\"currentColor\" d=\"M3 1H0v1h2.5M8 5.5L6 4v1H4.5L4 6h2v1m2-5.5L6 0v1H4L2 5H0v1h3l2-4h1v1\"/>"}, "shutter": {"body": "<path fill=\"currentColor\" d=\"M6 0q2 0 2 2L5 4m0 1l3-2v2M4 5l4 1q0 2-2 2M3 5l2 3H3M2 8Q0 8 0 6l3-2M0 3h3L0 5m0-3q0-2 2-2l2 3M3 0h2v3\"/>"}, "sidelist": {"body": "<path fill=\"currentColor\" d=\"M5 0h3v8H5M0 0h4v2H0m0 1h4v2H0m0 1h4v2H0\"/>"}, "sidenav": {"body": "<path fill=\"currentColor\" d=\"M3 0h5v8H3M0 0h2v2H0m0 1h2v2H0m0 1h2v2H0\"/>"}, "silverware": {"body": "<path fill=\"currentColor\" d=\"M7 8V5Q4 1 8 0v8M3 3V0H2v3H1V0H0v4l2 1v3h1V5l2-1V0H4v3\"/>"}, "sim": {"body": "<path fill=\"currentColor\" d=\"M1 8V0h4l3 3v5M3 3h3v3H3M2 5h5V4H2m0 3h1V2H2m4 5h1V2H6M4 7h1V2H4\"/>"}, "sink": {"body": "<path fill=\"currentColor\" d=\"m2 8l1-3Q0 5 0 2h8q0 3-3 3l1 3M3 2V0h1v2m2 0V1h1v1M1 2V1h1v1\"/>"}, "size": {"body": "<path fill=\"currentColor\" d=\"M5 7V5H4V4h3v1H6v2M2 2H0V1h5v1H3v5H2\"/>"}, "skate": {"body": "<path fill=\"currentColor\" d=\"M4 4L3 6H2l1-3H1V2h6v1H5l1 3H5M4 2L3 1l1-1l1 1M0 6.5h8L7 7Q6 9 5 7H3Q2 9 1 7\"/>"}, "ski": {"body": "<path fill=\"currentColor\" d=\"M6 8L0 5V4l6 3h2v1M5 7L4 6l1-1l-4-2V2h5l2 2l-5-1l4 2M6 2V0h2v2\"/>"}, "skull": {"body": "<path fill=\"currentColor\" d=\"M7 4H5v2h2M1 4v2h2V4m5 3H6v1H2V7H0V3q0-3 4-3t4 3\"/>"}, "slash": {"body": "<path fill=\"currentColor\" d=\"M3 5h2V1h1v2H4v4H3\"/>"}, "smartwatch": {"body": "<path fill=\"currentColor\" d=\"M6 6V2H2v4m3 1v1H3V7H2L1 6V2l1-1h1V0h2v1h1l1 1v4L6 7\"/>"}, "smooking": {"body": "<path fill=\"currentColor\" d=\"M5 3Q2 3 4 0v2q3 0 1 2m2 0q0-3-2-3V0q3 0 3 4m0 3V5H7v2M0 7V5h6v2\"/>"}, "snooker": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m5-3H2v5h3M4 5L3 2h1L3 5\"/>"}, "snowboard": {"body": "<path fill=\"currentColor\" d=\"M5 6L4 5H1L0 4h3l2-2l2 1l-2 1l2 2l-2 2l-1-1m0 1L0 5V4l6 4m2-4L3 1l1-1l4 3M6 2V0h2v2\"/>"}, "sofa": {"body": "<path fill=\"currentColor\" d=\"M7 5V4h1v1M0 5V4h1v1m0 3V6h6v2H6V7H2v1m4-3H2V2l1-1h2l1 1\"/>"}, "soup": {"body": "<path fill=\"currentColor\" d=\"M1 1H0l2 3h1m0-3H2l2 3h1m3 0c0 5-8 5-8 0\"/>"}, "south": {"body": "<path fill=\"currentColor\" d=\"m2 3l2 2l2-2\"/>"}, "speaker": {"body": "<path fill=\"currentColor\" d=\"M2 5c0-3 4-3 4 0S2 8 2 5M1 8h6V0H1m2 2V1h2v1M3 5l1-1l1 1l-1 1\"/>"}, "spellcheck": {"body": "<path fill=\"currentColor\" d=\"M1 7h6V6H6v2H5V6H4v2H3V6H2v2H1m1-3V0h4v5H5V3H3v2m0-4v1h2V1\"/>"}, "sphere": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m4-3Q1 1 1 4q1-2 3-2\"/>"}, "spinner": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5.25 8-5.25 8 0S0 9.25 0 4c1 5 7 4 7 0S1 0 1 4\"/>"}, "spinner2": {"body": "<path fill=\"currentColor\" d=\"M0 2.5C1-1 8-1 8 4S1 9 0 5.5L1 5c0 3 6 3 6-1S1 0 1 3\"/>"}, "split": {"body": "<path fill=\"currentColor\" d=\"M3 0h2v2l2 2l1-1v3H5l1-1l-2-2l-2 2l1 1H0V3l1 1l2-2\"/>"}, "spray": {"body": "<path fill=\"currentColor\" d=\"M7 3v1h1V3m0-2H5v1h3M7 1H6v2h1M3 1h5v5M1 2V0h2v2M0 8V4l1-1h2l1 1v4\"/>"}, "stack": {"body": "<path fill=\"currentColor\" d=\"m4 0l4 2l-4 2l-4-2m0 1l4 2l4-2v1L4 6L0 4m0 1l4 2l4-2v1L4 8L0 6\"/>"}, "stacked": {"body": "<path fill=\"currentColor\" d=\"M0 8V6h8v2M0 5V3h8v2M0 2V0h8v2\"/>"}, "stackoverflow": {"body": "<path fill=\"currentColor\" d=\"M3 0h2l2 2v1M2 3V2l4 1v1M2 6V5h4v1M0 8V4h1v3h6V4h1v4\"/>"}, "stamp": {"body": "<path fill=\"currentColor\" d=\"M0 4h3V2q-4-1 1-2q5 1 1 2v2h3v2H0m1 1h6v1H1\"/>"}, "star": {"body": "<path fill=\"currentColor\" d=\"M0 2h8L4 5M1 7l3-7l3 7l-3-2\"/>"}, "steadycam": {"body": "<path fill=\"currentColor\" d=\"m6 4l2-1v4L6 6M0 7V1h5v1H1v1h4v4M1 5h3V4H1\"/>"}, "steam": {"body": "<path fill=\"currentColor\" d=\"M3 4v1h1V4m2-3L5 2l1 1l1-1M2 7L0 5V3l2 2l2-3l2-2l2 2l-2 2\"/>"}, "stop": {"body": "<path fill=\"currentColor\" d=\"M8 8V0H0v8\"/>"}, "straighten": {"body": "<path fill=\"currentColor\" d=\"m1 3l5-1l1 3l-5 1M0 1h8v1H0m0 4h8v1H0\"/>"}, "stroller": {"body": "<path fill=\"currentColor\" d=\"m4 3l1-3H2c-6 6 6 8 6 3M3 8L2 7l1-1l1 1m2 1L5 7l1-1l1 1\"/>"}, "student": {"body": "<path fill=\"currentColor\" d=\"M0 3h1v4m1-1V4h4v2M0 3l4-2l4 2l-4 2\"/>"}, "stylo": {"body": "<path fill=\"currentColor\" d=\"M1.5 7.5L0 8l.5-1.5M7 2L6 1l1-1l1 1M2 7L1 6l4.5-4.5l1 1\"/>"}, "sublimtext": {"body": "<path fill=\"currentColor\" d=\"m1 2l6-2v2L4 3l3 1v2L1 8V6l3-1l-3-1\"/>"}, "subtitle": {"body": "<path fill=\"currentColor\" d=\"M0 8V0h8v8M5 5V4H1v1m6 2V6H3v1m4-2V4H6v1M2 7V6H1v1\"/>"}, "sun": {"body": "<path fill=\"currentColor\" d=\"m3 3l1-3l1 3l3 1l-3 1l-1 3l-1-3l-3-1m1-3l3 2l3-2l-2 3l2 3l-3-2l-3 2l2-3\"/>"}, "sunglasses": {"body": "<path fill=\"currentColor\" d=\"M8 2Q7 8 4 3Q1 8 0 2\"/>"}, "sunrise": {"body": "<path fill=\"currentColor\" d=\"m2 2l2-2l2 2M0 8V7h8v1M0 6l2-1l-1-2l2 1l1-1l1 1l2-1l-1 2l2 1\"/>"}, "sunset": {"body": "<path fill=\"currentColor\" d=\"m2 0l2 2l2-2M0 8V7h8v1M0 6l2-1l-1-2l2 1l1-1l1 1l2-1l-1 2l2 1\"/>"}, "swift": {"body": "<path fill=\"currentColor\" d=\"m0 8l2-3l-1-2l3-3l-2 3l1 1l5-4l-4 5l1 1l3-2l-3 3l-2-1\"/>"}, "swim": {"body": "<path fill=\"currentColor\" d=\"M4 4V2h2v2M3 4L1 5l2-5h4v1H4M0 8V7l2-1l2 1l2-1l2 1v1M2 5L0 6V5l2-1l2 1l2-1l2 1v1L6 5L4 6\"/>"}, "switch": {"body": "<path fill=\"currentColor\" d=\"M8 5v1H6v2L3 5.5L6 3v2M2 2H0v1h2v2l3-2.5L2 0\"/>"}, "sword": {"body": "<path fill=\"currentColor\" d=\"M1 8L0 7l2-2l-2-2h1l2 1l3-3l2-1l-1 2l-3 3l1 2v1L3 6\"/>"}, "tab": {"body": "<path fill=\"currentColor\" d=\"M0 8V5l1-2h3l1 2h3v3\"/>"}, "tabletennis": {"body": "<path fill=\"currentColor\" d=\"m0 2l1 1l1-1l-1-1m7 2C8-.5 3-.5 3 3s5 3.5 5 0M2 7l1.5-1.5l-1-1L1 6\"/>"}, "taekwondo": {"body": "<path fill=\"currentColor\" d=\"M3 8V5L1 4v1h1v1H0V4l3-3l2-1v1L3 2l1 1l3-3v1.5l-3 3V8M0 1c1-2 3 0 1 1\"/>"}, "tag": {"body": "<path fill=\"currentColor\" d=\"M2 3V0h1v3M2 8V6h1v2m5-6L4 6H1V4l4-4M2 4v1h1V4\"/>"}, "takeoff": {"body": "<path fill=\"currentColor\" d=\"M0 8V7h8v1M1 5L0 3h1l1 1l2-1l-2-2h1l2 1l2-1h1v1L2 5\"/>"}, "takeout": {"body": "<path fill=\"currentColor\" d=\"M2 8L1 4L0 1V0l2 2l1-2h2l1 2l2-2v1L7 4L6 8M2 2L1 3.5h6L6 2\"/>"}, "taxi": {"body": "<path fill=\"currentColor\" d=\"M6 5L5 6h2V5M1 5v1h2L2 5m0-3L1 4h6L6 2M0 8V3l1-2h1.5L3 0h2l.5 1H7l1 2v5H6V7H2v1\"/>"}, "tea": {"body": "<path fill=\"currentColor\" d=\"M2 0v2L1 3l1 1l1.5-1l-1-1V0M5 2v1h1V2M0 8V7h7v1M0 6V0h5v1c3 0 3 3 0 3v2\"/>"}, "tel": {"body": "<path fill=\"currentColor\" d=\"M7 2H6v5h2V6H7M2 1H1v6h1m1-7H0v1h3m2 4H4v1h1v1H3V2h2v1H4v1h1\"/>"}, "telescope": {"body": "<path fill=\"currentColor\" d=\"m5 0l3 3l-7 3.5L0 5m4.5-1L7 8H6L5 6v2H4V6L3 8H2\"/>"}, "temperature": {"body": "<path fill=\"currentColor\" d=\"M3 0h1l1 1H4v1l1 1H4v1c3 2.5-4 2.5-1 0M0 6l3 1l3-1l2 1v1L6 7L3 8L0 7\"/>"}, "tennis": {"body": "<path fill=\"currentColor\" d=\"M1 2L0 1l1-1l1 1M1 8L0 7l2-2C3-5 14 4 3 6m1-1c6-2 1-7-1-1\"/>"}, "test": {"body": "<path fill=\"currentColor\" d=\"M8 1L0 5V1m0 6h8V3M7 8L3 0h4M1 0v8h4M2 6V5h1v1m3-3V2H5v1\"/>"}, "tetris": {"body": "<path fill=\"currentColor\" d=\"M2 8V6h4V4h2v4M0 4V2h2V0h4v2H4v2\"/>"}, "thermometer": {"body": "<path fill=\"currentColor\" d=\"M6 1v1h1V1M4 4C0 8 9 8 5 4V1H4m2 3c4 5-7 5-3 0V0h4v3H6\"/>"}, "throw": {"body": "<path fill=\"currentColor\" d=\"m5 1l1-1l1 1l-1 1M2 3l3-1h2l1 2.5H6L5 3m1 2h2v4L7 6L6 9M0 4l1 3h2l1-3l-1 4H1m1-4h1v1H2\"/>"}, "thumbdown": {"body": "<path fill=\"currentColor\" d=\"M0 5V1h1v4m3 0l1 3H4L2 5V1q6-1 5 4\"/>"}, "thumbup": {"body": "<path fill=\"currentColor\" d=\"M0 7V3h1v4m1-4l1-3h1v3h3q1 5-5 4\"/>"}, "thunderbolt": {"body": "<path fill=\"currentColor\" d=\"m2 0l4 4H2l4 4\"/>"}, "thundercloud": {"body": "<path fill=\"currentColor\" d=\"M1 5C0 5-1 2 2 2c0-3 4-2 4-1c3 1 2 4 0 4M4 4v4l1-2H3\"/>"}, "ticket": {"body": "<path fill=\"currentColor\" d=\"M3 5h1V4H3m0 2v1h1V6M3 2v1h1V2m4 1l-1 .5v1L8 5v2H0V5l1-.5v-1L0 3V1h8\"/>"}, "tie": {"body": "<path fill=\"currentColor\" d=\"M3 1V0h2v1M4 8L2 6l1-4h2l1 4\"/>"}, "tile": {"body": "<path fill=\"currentColor\" d=\"M4 7V4h3v3M0 7V4h3v3m1-4V0h3v3M0 3V0h3v3\"/>"}, "timer": {"body": "<path fill=\"currentColor\" d=\"M0 5c0-5 8-5 8 0c0 4-8 4-8 0m2-5h4v1H2m5 0h1v1H7M0 1h1v1H0m4 3h3L5 4L4 1\"/>"}, "times": {"body": "<path fill=\"currentColor\" d=\"m0 1l1-1l7 7l-1 1M1 8L0 7l7-7l1 1\"/>"}, "tm": {"body": "<path fill=\"currentColor\" d=\"M4 4V1h1l1 1l1-1h1v3H7V2L6 3L5 2v2M1 4V2H0V1h3v1H2v2\"/>"}, "toast": {"body": "<path fill=\"currentColor\" d=\"M1 8V4q-3-4 3-4t3 4v4\"/>"}, "todo": {"body": "<path fill=\"currentColor\" d=\"M0 0h6L5 1H1v6h6V5l1-1v4H0m2-6l2 2l4-4v2L4 6L2 4\"/>"}, "toilet": {"body": "<path fill=\"currentColor\" d=\"M1 6V1h2v3h4q0 2-2 2l1 2H2V6\"/>"}, "toolbox": {"body": "<path fill=\"currentColor\" d=\"M0 3h8v1H0m3-3h2v1H3m0 3h2v1H3M0 7h8V2H6V0H2v2H0\"/>"}, "torch": {"body": "<path fill=\"currentColor\" d=\"M0 3h5l2-1h1v4H7L5 5H0m3-2L2 4l1 1l1-1\"/>"}, "tornado": {"body": "<path fill=\"currentColor\" d=\"M2 4L1 3h4l1 1M4 6L3 5h4v1M0 2V1h7L6 2M3 8l1-1h2L5 8\"/>"}, "touch": {"body": "<path fill=\"currentColor\" d=\"M0 3h1l2 2V0h1v3l4 2l-1 3H3\"/>"}, "traffic": {"body": "<path fill=\"currentColor\" d=\"m4 8l1-1l-1-1l-1 1m2-3L4 3L3 4l1 1m1-4L4 0L3 1l1 1M2 8L0 5h2L0 2h2L0 0h8L6 2h2L6 5h2L6 8\"/>"}, "train": {"body": "<path fill=\"currentColor\" d=\"M5 3v1h1V3M2 3v1h1V3M2 1v1h4V1M3 5L0 8V7l2-2l-1-1V1l1-1h4l1 1v3L6 5l2 2v1L5 5\"/>"}, "tram": {"body": "<path fill=\"currentColor\" d=\"M6 6v1h1V6M3 1v1h2V1M1 6v1h1V6M1 0v1h6V0M1 3v2h6V3M1 8L0 7V3l1-1h6l1 1v4L7 8\"/>"}, "translate": {"body": "<path fill=\"currentColor\" d=\"M4 0q0 4-4 4V3q3 0 3-2H0V0m7 4H5v1h2M4 8V5c0-3 4-3 4 0v3H7V6H5v2\"/>"}, "transport": {"body": "<path fill=\"currentColor\" d=\"M0 1v6h8V1M5 6V3l2 1.5M1 6V2h6v4\"/>"}, "trash": {"body": "<path fill=\"currentColor\" d=\"M5 4v3h1V4M2 4v3h1V4M0 2V1h2V0h4v1h2v1M7 8H1V3h6\"/>"}, "tree": {"body": "<path fill=\"currentColor\" d=\"M2 8H1V2l1 2h2L2 5v2h2m0 1V6h2v2M4 5V3h2v2M0 2V0h2v2\"/>"}, "trolley": {"body": "<path fill=\"currentColor\" d=\"m7 6l1 1l-1 1l-1-1M2 7l1-1l1 1l-1 1M0 1V0h2l1 3h3l1-2h1L7 4H3v1h4l1 1H1l1-2l-1-3\"/>"}, "trophy": {"body": "<path fill=\"currentColor\" d=\"M6 4q2-2 0-2M2 2Q0 2 2 4m3 3h2v1H1V7h2V6q-5-5-1-5V0h4v1q4 0-1 5\"/>"}, "truck": {"body": "<path fill=\"currentColor\" d=\"M0 4V0h4v4m3 2L6 8H5L4 6H3L2 8H1L0 6V5h5V1h1l2 2v3\"/>"}, "true": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5 8-5 8 0S0 9 0 4m4-2L2 4l2 2l2-2\"/>"}, "tshirt": {"body": "<path fill=\"currentColor\" d=\"M2 8V3H0V1h2l2 1l2-1h2v2H6v5\"/>"}, "tty": {"body": "<path fill=\"currentColor\" d=\"M3 8V5h1v3m1 0V5h1v3M1 6V5h1v2h5V5h1v1M1 4c0-4 7-4 7 0H6V3H3v1\"/>"}, "tube": {"body": "<path fill=\"currentColor\" d=\"M3 1v4h2V1m1 0v5c0 3-4 3-4 0V1H1V0h6v1\"/>"}, "tuning": {"body": "<path fill=\"currentColor\" d=\"M2 0h1v4q1.5 2 3 0V0h1v4c0 3-5 3-5 0m2 1h1v3H4\"/>"}, "turtle": {"body": "<path fill=\"currentColor\" d=\"M2 6Q1 8 0 6c0-5 5-5 5 0Q4 8 3 6m3-3h1V2H6M5 5c-2-8 7-2 1-1\"/>"}, "tv": {"body": "<path fill=\"currentColor\" d=\"M5 0h1L4 2L2 0h1l1 1m3 4V2H1v3m1 2l1-1H0V1h8v5H5l1 1\"/>"}, "twitch": {"body": "<path fill=\"currentColor\" d=\"M5 4V2h1v2M3 4V2h1v2M2 1v4h1v1l1-1h2l1-1V1m1 4L6 7H3L2 8V7H0V2l2-2h6\"/>"}, "twitter": {"body": "<path fill=\"currentColor\" d=\"M8 2L4 3l3-3l-.5 1.5M2 6Q0 5 0 1l3 2q0-5 4-.5Q8 8 0 8\"/>"}, "ufo": {"body": "<path fill=\"currentColor\" d=\"M8 4c0 3-8 3-8 0l1.5-1C2 0 6 0 6.5 3M2 4q2 1 4 0c0-3-4-3-4 0M1 4v1h1m4 0h1V4M4.5 5L4 4.5l-.5.5l.5.5\"/>"}, "umbrella": {"body": "<path fill=\"currentColor\" d=\"M5 3v4Q4 9 2 7h2V3H0q4-5 8 0\"/>"}, "underground": {"body": "<path fill=\"currentColor\" d=\"M5 5v1h1V5M2 5v1h1V5M2 2v2h4V2M2 7L1 6V2l1-1h4l1 1v4L6 7M0 8h8V3c0-4-8-4-8 0\"/>"}, "underline": {"body": "<path fill=\"currentColor\" d=\"M2 7V6h4v1M3 5L2 4V1h1v3h2V1h1v3L5 5\"/>"}, "unicorn": {"body": "<path fill=\"currentColor\" d=\"m3 1l4 4q0 3-4 0l1 3H0q0-6 3-6m0 2h1V3H3m5-3L6 3L5 2\"/>"}, "unindent": {"body": "<path fill=\"currentColor\" d=\"M8 6v1H0V6m8-2v1H3V4m-3-.5L2 5V2m6 0v1H3V2m5-2v1H0V0\"/>"}, "unlock": {"body": "<path fill=\"currentColor\" d=\"M8 0v1H6v2H5V0M1 8V3h6v5\"/>"}, "up": {"body": "<path fill=\"currentColor\" d=\"m2 5l2-2l2 2l1-1l-3-3l-3 3\"/>"}, "update": {"body": "<path fill=\"currentColor\" d=\"m4 0l1 1h2v2l1 1l-1 1v2H5L4 8L3 7H1V5L0 4l1-1V1h2m2 3V2H3v2H2l2 2l2-2\"/>"}, "updouble": {"body": "<path fill=\"currentColor\" d=\"m2 7l2-2l2 2l1-1l-3-3l-3 3m1-2l2-2l2 2l1-1l-3-3l-3 3\"/>"}, "updown": {"body": "<path fill=\"currentColor\" d=\"M6 5V1H5v4H3l2.5 3L8 5M2 3v4h1V3h2L2.5 0L0 3\"/>"}, "upload": {"body": "<path fill=\"currentColor\" d=\"M1 7v1h6V7M3 3v3h2V3h2L4 0L1 3\"/>"}, "upmost": {"body": "<path fill=\"currentColor\" d=\"m2 6l2-2l2 2l1-1l-3-3l-3 3m0-3h6V1H1\"/>"}, "upright": {"body": "<path fill=\"currentColor\" d=\"M4 3H0v5h2V5h2v2l4-3l-4-3\"/>"}, "upward": {"body": "<path fill=\"currentColor\" d=\"M3 4v3h2V4h2L4 1L1 4\"/>"}, "usb": {"body": "<path fill=\"currentColor\" d=\"M3 8c-4-1-4-6 0-7m.5 1H8v5H3.5M5 4h1V3H5m0 3h1V5H5\"/>"}, "vest": {"body": "<path fill=\"currentColor\" d=\"M5 4v2h1V4M2 4v2h1V4M1 8V3l1-1V0h1v1h2V0h1v2l1 1v5\"/>"}, "vi": {"body": "<path fill=\"currentColor\" d=\"M7 4V3h1v1M7 8V5h1v3M3 1v3l3-3H5V0h3v1L2 8H1V1H0V0h4v1\"/>"}, "viking": {"body": "<path fill=\"currentColor\" d=\"M1 7C0 1 8 1 7 7M1 6q-2-3 0-5q0 2 2 3m2 0q2-1 2-3q2 2 0 5\"/>"}, "virus": {"body": "<path fill=\"currentColor\" d=\"M6 2V0l2 2M2 2H0l2-2m0 8L0 6h2C0 3 3 0 6 2c2 3-1 6-4 4m3 0l1 2H4m2-4l2-1v2M5 5l1-1l-1-1l-1 1\"/>"}, "vk": {"body": "<path fill=\"currentColor\" d=\"M3 5V2h1.5v2L6 2h2L6 5l2 2H6.5l-2-1.5V7H2L0 2h1.5\"/>"}, "volley": {"body": "<path fill=\"currentColor\" d=\"M7 2L6 1l1-1l1 1M4 3L3 2l1-1l1 1M1 2l1-2h1L2 2l1 1h5v1H5L4 6v2H2l1-1V6L2 7H0V6h2l1-2\"/>"}, "volume": {"body": "<path fill=\"currentColor\" d=\"M1 5V3h2l2-2v6L3 5\"/>"}, "vote": {"body": "<path fill=\"currentColor\" d=\"M0 8V7l2-2h4l2 2v1M3 0v3h3L4 2M1 2l2-2l3 3l-2 2M2 6L1 7h6L6 6\"/>"}, "vr": {"body": "<path fill=\"currentColor\" d=\"M0 2h8v1Q7 8 4 4Q1 8 0 3m1 0l1-3h4l1 3l-1-2H2\"/>"}, "vscode": {"body": "<path fill=\"currentColor\" d=\"M6 2L0 7V6l6-6l2 1v6L6 8L0 2V1l6 5\"/>"}, "vue": {"body": "<path fill=\"currentColor\" d=\"m0 0l4 8l4-8M1 0h1l2 4l2-4h1L4 6M3 0h2L4 2\"/>"}, "walk": {"body": "<path fill=\"currentColor\" d=\"M4 3L3 5l1 1v2H3V6L2 5L1 8H0l2-5H1v2H0V3l2-1h2l1 1h1v1H5M4 0l1 1l-1 1l-1-1\"/>"}, "wall": {"body": "<path fill=\"currentColor\" d=\"M3 3H2v2h1m3 1H5v2h1m0-8H5v2h1M0 2v1h8V2M0 5v1h8V5M0 8V0h8v8\"/>"}, "wallet": {"body": "<path fill=\"currentColor\" d=\"M1 7V1h6v1h1v4H7v1M5 3v2h2V3\"/>"}, "warning": {"body": "<path fill=\"currentColor\" d=\"M4 6V5H3v1m1-2V2H3v2M0 7V6l3-6h1l3 6v1\"/>"}, "wasp": {"body": "<path fill=\"currentColor\" d=\"M8 3L5 8H3L0 3l2-1l1-1l-1-1h1l1 1l1-1h1L5 1l1 1M2 2v1h4V2M2 4v1h4V4M2 6l1 1h2l1-1\"/>"}, "water": {"body": "<path fill=\"currentColor\" d=\"M8 6Q6 4 4 6Q2 4 0 6V5q2-2 4 0q2-2 4 0m0 3Q6 6 4 8Q2 6 0 8V7q2-2 4 0q2-2 4 0\"/>"}, "wave": {"body": "<path fill=\"currentColor\" d=\"M0 8c6-2 4-6 1-4c4-6 6-1 7 4\"/>"}, "weight": {"body": "<path fill=\"currentColor\" d=\"M0 3q0-3 3-3h2q3 0 3 3v2q0 3-3 3H3Q0 8 0 5m2-3h1l1-1v1h2V1H2\"/>"}, "west": {"body": "<path fill=\"currentColor\" d=\"m3 4l2 2V2\"/>"}, "whale": {"body": "<path fill=\"currentColor\" d=\"M6 4V3h1v1M2 2L1 3L0 2v2l2 2c9 3 7-9 0-1\"/>"}, "whatsapp": {"body": "<path fill=\"currentColor\" d=\"M3 7q5 1 5-3T4 0q-5 0-3 6L0 8m7-2Q1 7 2 1h2v2H3l2 2V4h2\"/>"}, "wheelchair": {"body": "<path fill=\"currentColor\" d=\"M4 5V2h3v1H5v1h2v3H6V5M4 1l1-1l1 1l-1 1M3.5 4C0 3 2 10 4.5 6M5 7c-5 4-5-5-2-4\"/>"}, "whistle": {"body": "<path fill=\"currentColor\" d=\"M5 4c2 3-4 4-4 0M0 4V3h1l1-1h2v1h1V2h3v1L5 4\"/>"}, "width": {"body": "<path fill=\"currentColor\" d=\"M5 3H3V1L0 4l3 3V5h2v2l3-3l-3-3\"/>"}, "wifi0": {"body": "<path fill=\"currentColor\" d=\"M8 3Q4-1 0 3l4 4m0-1L1 3q3-3 6 0\"/>"}, "wifi1": {"body": "<path fill=\"currentColor\" d=\"M8 3Q4-1 0 3l4 4M3 5L1 3q3-3 6 0L5 5\"/>"}, "wifi2": {"body": "<path fill=\"currentColor\" d=\"M8 3Q4-1 0 3l4 4M2 4L1 3q3-3 6 0L6 4\"/>"}, "wifi3": {"body": "<path fill=\"currentColor\" d=\"M8 3Q4-1 0 3l4 4M1 3q3-3 6 0\"/>"}, "wifi4": {"body": "<path fill=\"currentColor\" d=\"M8 3Q4-1 0 3l4 4\"/>"}, "wikipedia": {"body": "<path fill=\"currentColor\" d=\"m5 6l2-4h1L5 7L2 2h1m0 4l2-4h1L3 7L0 2h1\"/>"}, "wind": {"body": "<path fill=\"currentColor\" d=\"m0 4l1-1h4V2H4l1-1l1 1v1L5 4M1 8l1-1h5V6H6l1-1l1 1v1L7 8\"/>"}, "window": {"body": "<path fill=\"currentColor\" d=\"M0 0h8v8H0m3.5-1h1V1h-1M1 7h6V1H1m0 2.5v1h6v-1\"/>"}, "windowed": {"body": "<path fill=\"currentColor\" d=\"M8 5v1H6v2H5V5M0 6V5h3v3H2V6m0-6v2H0v1h3V0m3 0v2h2v1H5V0\"/>"}, "windows": {"body": "<path fill=\"currentColor\" d=\"M4 3V1l4-1v3M4 4v2l4 1V4M1 4v1l2 1V4M1 3V2l2-1v2\"/>"}, "windsocket": {"body": "<path fill=\"currentColor\" d=\"m0 1l2-1v8H1V2M3 .5L5 1v4l-2 .5m3-4L8 2v2l-2 .5\"/>"}, "woman": {"body": "<path fill=\"currentColor\" d=\"M3 2L2 3C1 7 7 7 6 3m1 4H1V3c0-4 6-4 6 0\"/>"}, "workstation": {"body": "<path fill=\"currentColor\" d=\"m7 2l1 1l-1 1l-1-1m1 5L6 6H1L0 8m8-3V0H6v5M4 4V1H1v3M0 5V0h5v5\"/>"}, "wrench": {"body": "<path fill=\"currentColor\" d=\"m4 3l4 4l-1 1l-4-4H1L0 2l2 1l1-1l-1-2l2 1\"/>"}, "xbox": {"body": "<path fill=\"currentColor\" d=\"m5 4l3 4l-4-3l-4 3l3-4l-3-4l4 2l4-2\"/>"}, "xor": {"body": "<path fill=\"currentColor\" d=\"M0 6V0h6v2h2v6H2V6M1 1v4h1V2h3V1M3 6v1h4V3H6v3\"/>"}, "yahoo": {"body": "<path fill=\"currentColor\" d=\"M6 6V5h1v1M6 4l1-4h1L7 4M4 2h2L3 8H1l1-2l-2-4h1.5L3 5\"/>"}, "yinyang": {"body": "<path fill=\"currentColor\" d=\"M0 4c0-5.5 8-5.5 8 0S0 9.5 0 4m7 0c0-4-6-4-6 0c3-4 3 4 6 0M2 5h1V4H2m3-1h1v1H5\"/>"}, "youtube": {"body": "<path fill=\"currentColor\" d=\"M6 3.5L3 2v3M0 4c0-3 0-3 4-3s4 0 4 2.5S8 6 4 6S0 6 0 3.5\"/>"}, "zip": {"body": "<path fill=\"currentColor\" d=\"M3 2v1h2V2M3 0v1h2V0M4 0v4H3V0m0 5v2h2V5M2 8V4h4v4\"/>"}}, "width": 8, "height": 8}