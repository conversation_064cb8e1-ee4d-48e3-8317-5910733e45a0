{"name": "@iconify/tailwind", "description": "Iconify plugin for Tailwind CSS", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://iconify.design)", "version": "1.2.0", "license": "MIT", "main": "./dist/plugin.js", "types": "./dist/plugin.d.ts", "bugs": "https://github.com/iconify/iconify/issues", "homepage": "https://iconify.design/", "funding": "https://github.com/sponsors/cyberalien", "repository": {"type": "git", "url": "https://github.com/iconify/iconify.git", "directory": "plugins/tailwind"}, "dependencies": {"@iconify/types": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@iconify-json/line-md": "^1.2.2", "@iconify-json/mdi-light": "^1.2.1", "@microsoft/api-extractor": "^7.48.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.1", "@typescript-eslint/eslint-plugin": "^8.17.0", "eslint": "^9.16.0", "globals": "^15.13.0", "jest": "^29.7.0", "rimraf": "^6.0.1", "rollup": "^4.28.1", "tailwindcss": "^3.4.16", "ts-jest": "^29.2.5", "typescript": "^5.7.2", "@iconify/utils": "^2.2.0"}, "scripts": {"clean": "rimraf lib dist tsconfig.tsbuildinfo", "lint": "eslint src/**/*.ts", "prebuild": "pnpm run lint && pnpm run clean", "build": "node build", "build:api": "api-extractor run --local --verbose", "build:lib": "tsc -b", "build:dist": "rollup -c rollup.config.mjs", "test": "jest --runInBand"}}