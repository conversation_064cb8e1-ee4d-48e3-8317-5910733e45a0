/**
 * Chart.js plugin for client-side initialization
 * This ensures Chart.js is properly registered only on the client side
 */

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js'

export default defineNuxtPlugin(() => {
  // Register Chart.js components on client side only
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    Filler
  )
})
