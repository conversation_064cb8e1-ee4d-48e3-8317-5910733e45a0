<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Th<PERSON><PERSON> kê
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Báo cáo và phân tích dữ liệu kinh doanh
        </p>
      </div>
      <div class="flex items-center gap-3">
        <Dropdown
          v-model="selectedPeriod"
          :options="periodOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Chọn khoảng thời gian"
          class="w-48"
        />
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          outlined
          @click="refreshData"
        />
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              <PERSON><PERSON><PERSON> thu {{ selectedPeriod }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatCurrency(stats.revenue) }}
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-dollar text-green-600 dark:text-green-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span :class="stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
            {{ stats.revenueGrowth >= 0 ? '+' : '' }}{{ stats.revenueGrowth }}%
          </span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với kỳ trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Đơn hàng {{ selectedPeriod }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(stats.orders) }}
            </p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-shopping-cart text-blue-600 dark:text-blue-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span :class="stats.ordersGrowth >= 0 ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
            {{ stats.ordersGrowth >= 0 ? '+' : '' }}{{ stats.ordersGrowth }}%
          </span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với kỳ trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Khách hàng mới
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatNumber(stats.newCustomers) }}
            </p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-user-plus text-purple-600 dark:text-purple-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span :class="stats.customersGrowth >= 0 ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
            {{ stats.customersGrowth >= 0 ? '+' : '' }}{{ stats.customersGrowth }}%
          </span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với kỳ trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Tỷ lệ chuyển đổi
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.conversionRate }}%
            </p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-percentage text-orange-600 dark:text-orange-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span :class="stats.conversionGrowth >= 0 ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
            {{ stats.conversionGrowth >= 0 ? '+' : '' }}{{ stats.conversionGrowth }}%
          </span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với kỳ trước</span>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Doanh thu theo thời gian</h3>
          <p class="card-subtitle">Biểu đồ doanh thu {{ selectedPeriod }}</p>
        </div>
        <div class="card-content">
          <div class="h-80">
            <LineChart
              :data="revenueChartData"
              :height="320"
            />
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Top sản phẩm bán chạy</h3>
          <p class="card-subtitle">{{ selectedPeriod }}</p>
        </div>
        <div class="card-content">
          <div class="space-y-4">
            <div v-for="(product, index) in topProducts" :key="product.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <span class="text-sm font-bold text-blue-600 dark:text-blue-400">{{ index + 1 }}</span>
                </div>
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">
                    {{ product.name }}
                  </p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ product.category }}
                  </p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-gray-900 dark:text-white">
                  {{ product.sold }} đã bán
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatCurrency(product.revenue) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Phân tích đơn hàng</h3>
        </div>
        <div class="card-content">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Đơn hàng hoàn thành</span>
              <span class="font-medium">{{ stats.completedOrders }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Đơn hàng đang xử lý</span>
              <span class="font-medium">{{ stats.processingOrders }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Đơn hàng bị hủy</span>
              <span class="font-medium text-red-600">{{ stats.cancelledOrders }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-400">Giá trị đơn hàng TB</span>
              <span class="font-medium">{{ formatCurrency(stats.avgOrderValue) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Hiệu suất bán hàng</h3>
        </div>
        <div class="card-content">
          <div class="h-48">
            <PieChart
              :data="salesPerformanceData"
              :height="192"
            />
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Xu hướng khách hàng</h3>
        </div>
        <div class="card-content">
          <div class="h-48">
            <BarChart
              :data="customerTrendsData"
              :height="192"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const selectedPeriod = ref('tháng này')
const toast = useToast()
const { generateRevenueData, generateSalesPerformanceData, generateCustomerTrendsData } = useChart()

const periodOptions = [
  { label: 'Hôm nay', value: 'hôm nay' },
  { label: 'Tuần này', value: 'tuần này' },
  { label: 'Tháng này', value: 'tháng này' },
  { label: 'Quý này', value: 'quý này' },
  { label: 'Năm này', value: 'năm này' }
]

// Mock statistics data
const stats = reactive({
  revenue: 125000000,
  revenueGrowth: 12.5,
  orders: 1234,
  ordersGrowth: 8.2,
  newCustomers: 156,
  customersGrowth: 15.3,
  conversionRate: 3.2,
  conversionGrowth: 0.8,
  completedOrders: 1089,
  processingOrders: 145,
  cancelledOrders: 23,
  avgOrderValue: 18500000
})

const topProducts = ref([
  { id: 1, name: 'MacBook Pro M3', category: 'Laptop', sold: 45, revenue: 1350000000 },
  { id: 2, name: 'Dell XPS 13', category: 'Laptop', sold: 38, revenue: 950000000 },
  { id: 3, name: 'ThinkPad X1 Carbon', category: 'Laptop', sold: 32, revenue: 800000000 },
  { id: 4, name: 'Gaming Mouse Pro', category: 'Phụ kiện', sold: 156, revenue: 78000000 },
  { id: 5, name: 'Mechanical Keyboard', category: 'Phụ kiện', sold: 89, revenue: 89000000 }
])

// Chart data - reactive to period changes
const revenueChartData = computed(() => generateRevenueData(selectedPeriod.value))
const salesPerformanceData = computed(() => generateSalesPerformanceData())
const customerTrendsData = computed(() => generateCustomerTrendsData(selectedPeriod.value))

const refreshData = () => {
  toast.add({
    severity: 'success',
    summary: 'Thành công',
    detail: `Đã làm mới dữ liệu thống kê ${selectedPeriod.value}`,
    life: 3000
  })
}

// Set page title
useHead({
  title: 'Thống kê - LapXpert Admin'
})
</script>
