<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-auto flex items-center justify-center">
          <svg viewBox="0 0 54 40" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-12 w-12">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              fill="var(--primary-color)"
              d="M2524 1391 c-67 -41 -98 -122 -75 -194 26 -78 132 -135 201 -109 21
8 34 5 65 -14 496 -306 1091 -528 1668 -624 186 -30 466 -60 567 -60 48 0 60
-4 81 -26 114 -124 324 -10 279 151 -35 123 -181 165 -272 78 l-27 -26 -143 6
c-320 15 -693 83 -1053 192 -277 84 -693 270 -945 423 -80 48 -95 62 -101 88
-9 49 -46 100 -83 118 -47 22 -123 21 -162 -3z"
            />
          </svg>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
          Đăng nhập vào LapXpert
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Quản lý hệ thống laptop chuyên nghiệp
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm space-y-4">
          <div>
            <label for="taiKhoan" class="sr-only">Tài khoản</label>
            <InputText
              id="taiKhoan"
              v-model="loginForm.taiKhoan"
              type="text"
              placeholder="Tài khoản"
              class="w-full"
              :class="{ 'p-invalid': errors.taiKhoan }"
              required
            />
            <small v-if="errors.taiKhoan" class="p-error">{{ errors.taiKhoan }}</small>
          </div>
          
          <div>
            <label for="matKhau" class="sr-only">Mật khẩu</label>
            <Password
              id="matKhau"
              v-model="loginForm.matKhau"
              placeholder="Mật khẩu"
              class="w-full"
              :class="{ 'p-invalid': errors.matKhau }"
              :feedback="false"
              toggleMask
              required
            />
            <small v-if="errors.matKhau" class="p-error">{{ errors.matKhau }}</small>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <Checkbox
              id="remember-me"
              v-model="rememberMe"
              binary
            />
            <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
              Ghi nhớ đăng nhập
            </label>
          </div>

          <div class="text-sm">
            <a href="#" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
              Quên mật khẩu?
            </a>
          </div>
        </div>

        <div>
          <Button
            type="submit"
            label="Đăng nhập"
            class="w-full"
            :loading="isLoading"
            :disabled="isLoading"
          />
        </div>

        <div v-if="loginError" class="rounded-md bg-red-50 dark:bg-red-900 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="pi pi-exclamation-triangle text-red-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Đăng nhập thất bại
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                {{ loginError }}
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: false,
  middleware: 'guest'
})

const { login } = useAuth()
const router = useRouter()
const toast = useToast()

const loginForm = reactive({
  taiKhoan: '',
  matKhau: ''
})

const errors = reactive({
  taiKhoan: '',
  matKhau: ''
})

const rememberMe = ref(false)
const isLoading = ref(false)
const loginError = ref('')

const validateForm = () => {
  errors.taiKhoan = ''
  errors.matKhau = ''
  
  let isValid = true
  
  if (!loginForm.taiKhoan.trim()) {
    errors.taiKhoan = 'Vui lòng nhập tài khoản'
    isValid = false
  }
  
  if (!loginForm.matKhau.trim()) {
    errors.matKhau = 'Vui lòng nhập mật khẩu'
    isValid = false
  } else if (loginForm.matKhau.length < 6) {
    errors.matKhau = 'Mật khẩu phải có ít nhất 6 ký tự'
    isValid = false
  }
  
  return isValid
}

const handleLogin = async () => {
  loginError.value = ''
  
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    await login(loginForm)
    
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Đăng nhập thành công!',
      life: 3000
    })
    
    // Redirect to dashboard
    await router.push('/')
  } catch (error) {
    loginError.value = error.message || 'Tài khoản hoặc mật khẩu không chính xác'
  } finally {
    isLoading.value = false
  }
}

// Set page title
useHead({
  title: 'Đăng nhập - LapXpert Admin'
})
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
