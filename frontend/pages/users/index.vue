<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Qu<PERSON>n lý người dùng
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Quản lý tài khoản người dùng trong hệ thống
        </p>
      </div>
    </div>

    <!-- User Table -->
    <UserTable
      :users="users"
      :loading="loading"
      @add-user="handleAddUser"
      @view-user="handleViewUser"
      @edit-user="handleEditUser"
      @delete-user="handleDeleteUser"
      @toggle-status="handleToggleStatus"
      @refresh="refreshUsers"
      @export="exportUsers"
    />
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

// Mock data - will be replaced with real API calls
const users = ref([
  {
    id: 1,
    hoTen: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    soDienThoai: '0123456789',
    vaiTro: 'ADMIN',
    trangThai: true,
    ngayTao: '2024-01-15T10:30:00Z',
    avatar: null
  },
  {
    id: 2,
    hoTen: 'Trần Thị Staff',
    email: '<EMAIL>',
    soDienThoai: '0987654321',
    vaiTro: 'STAFF',
    trangThai: true,
    ngayTao: '2024-02-20T14:15:00Z',
    avatar: null
  },
  {
    id: 3,
    hoTen: 'Lê Văn Customer',
    email: '<EMAIL>',
    soDienThoai: '0555666777',
    vaiTro: 'CUSTOMER',
    trangThai: false,
    ngayTao: '2024-03-10T09:45:00Z',
    avatar: null
  }
])

const loading = ref(false)
const toast = useToast()

const handleAddUser = () => {
  // Navigate to add user page
  navigateTo('/users/create')
}

const handleViewUser = (user) => {
  // Navigate to user detail page
  navigateTo(`/users/${user.id}`)
}

const handleEditUser = (user) => {
  // Navigate to edit user page
  navigateTo(`/users/${user.id}/edit`)
}

const handleDeleteUser = (user) => {
  // Show confirmation dialog
  console.log('Delete user:', user)
  toast.add({
    severity: 'info',
    summary: 'Thông báo',
    detail: `Chức năng xóa người dùng ${user.hoTen} sẽ được triển khai`,
    life: 3000
  })
}

const handleToggleStatus = (user) => {
  // Toggle user status
  const newStatus = !user.trangThai
  user.trangThai = newStatus
  
  toast.add({
    severity: 'success',
    summary: 'Thành công',
    detail: `Đã ${newStatus ? 'kích hoạt' : 'vô hiệu hóa'} người dùng ${user.hoTen}`,
    life: 3000
  })
}

const refreshUsers = () => {
  loading.value = true
  
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Đã làm mới danh sách người dùng',
      life: 3000
    })
  }, 1000)
}

const exportUsers = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông báo',
    detail: 'Chức năng xuất dữ liệu sẽ được triển khai',
    life: 3000
  })
}

// Set page title
useHead({
  title: 'Quản lý người dùng - LapXpert Admin'
})
</script>
