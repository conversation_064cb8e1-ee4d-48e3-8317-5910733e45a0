<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Thêm người dùng mới
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Tạo tài khoản người dùng mới trong hệ thống
        </p>
      </div>
      <Button
        icon="pi pi-arrow-left"
        label="Quay lại"
        outlined
        @click="$router.back()"
      />
    </div>

    <!-- User Form -->
    <div class="card max-w-2xl">
      <div class="card-header">
        <h3 class="card-title">Thông tin người dùng</h3>
      </div>
      
      <div class="card-content">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="hoTen" class="form-label">H<PERSON> và tên *</label>
              <InputText
                id="hoTen"
                v-model="userForm.hoTen"
                class="w-full"
                :class="{ 'p-invalid': errors.hoTen }"
                placeholder="Nhập họ và tên"
              />
              <small v-if="errors.hoTen" class="form-error">{{ errors.hoTen }}</small>
            </div>

            <div class="form-group">
              <label for="email" class="form-label">Email *</label>
              <InputText
                id="email"
                v-model="userForm.email"
                type="email"
                class="w-full"
                :class="{ 'p-invalid': errors.email }"
                placeholder="Nhập địa chỉ email"
              />
              <small v-if="errors.email" class="form-error">{{ errors.email }}</small>
            </div>

            <div class="form-group">
              <label for="soDienThoai" class="form-label">Số điện thoại</label>
              <InputText
                id="soDienThoai"
                v-model="userForm.soDienThoai"
                class="w-full"
                :class="{ 'p-invalid': errors.soDienThoai }"
                placeholder="Nhập số điện thoại"
              />
              <small v-if="errors.soDienThoai" class="form-error">{{ errors.soDienThoai }}</small>
            </div>

            <div class="form-group">
              <label for="vaiTro" class="form-label">Vai trò *</label>
              <Dropdown
                id="vaiTro"
                v-model="userForm.vaiTro"
                :options="roleOptions"
                optionLabel="label"
                optionValue="value"
                class="w-full"
                :class="{ 'p-invalid': errors.vaiTro }"
                placeholder="Chọn vai trò"
              />
              <small v-if="errors.vaiTro" class="form-error">{{ errors.vaiTro }}</small>
            </div>

            <div class="form-group">
              <label for="matKhau" class="form-label">Mật khẩu *</label>
              <Password
                id="matKhau"
                v-model="userForm.matKhau"
                class="w-full"
                :class="{ 'p-invalid': errors.matKhau }"
                placeholder="Nhập mật khẩu"
                toggleMask
                :feedback="true"
              />
              <small v-if="errors.matKhau" class="form-error">{{ errors.matKhau }}</small>
            </div>

            <div class="form-group">
              <label for="xacNhanMatKhau" class="form-label">Xác nhận mật khẩu *</label>
              <Password
                id="xacNhanMatKhau"
                v-model="userForm.xacNhanMatKhau"
                class="w-full"
                :class="{ 'p-invalid': errors.xacNhanMatKhau }"
                placeholder="Nhập lại mật khẩu"
                :feedback="false"
                toggleMask
              />
              <small v-if="errors.xacNhanMatKhau" class="form-error">{{ errors.xacNhanMatKhau }}</small>
            </div>
          </div>

          <div class="form-group">
            <div class="flex items-center gap-2">
              <Checkbox
                id="trangThai"
                v-model="userForm.trangThai"
                binary
              />
              <label for="trangThai" class="form-label mb-0">Kích hoạt tài khoản</label>
            </div>
            <small class="form-help">Tài khoản sẽ được kích hoạt ngay sau khi tạo</small>
          </div>

          <div class="flex gap-3 pt-4">
            <Button
              type="submit"
              label="Tạo người dùng"
              icon="pi pi-check"
              :loading="loading"
              :disabled="loading"
            />
            <Button
              type="button"
              label="Hủy"
              icon="pi pi-times"
              outlined
              @click="$router.back()"
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const router = useRouter()
const toast = useToast()

const userForm = reactive({
  hoTen: '',
  email: '',
  soDienThoai: '',
  vaiTro: '',
  matKhau: '',
  xacNhanMatKhau: '',
  trangThai: true
})

const errors = reactive({
  hoTen: '',
  email: '',
  soDienThoai: '',
  vaiTro: '',
  matKhau: '',
  xacNhanMatKhau: ''
})

const loading = ref(false)

const roleOptions = [
  { label: 'Quản trị viên', value: 'ADMIN' },
  { label: 'Nhân viên', value: 'STAFF' },
  { label: 'Khách hàng', value: 'CUSTOMER' }
]

const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  // Validate required fields
  if (!userForm.hoTen.trim()) {
    errors.hoTen = 'Vui lòng nhập họ và tên'
    isValid = false
  }

  if (!userForm.email.trim()) {
    errors.email = 'Vui lòng nhập email'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userForm.email)) {
    errors.email = 'Email không hợp lệ'
    isValid = false
  }

  if (!userForm.vaiTro) {
    errors.vaiTro = 'Vui lòng chọn vai trò'
    isValid = false
  }

  if (!userForm.matKhau.trim()) {
    errors.matKhau = 'Vui lòng nhập mật khẩu'
    isValid = false
  } else if (userForm.matKhau.length < 6) {
    errors.matKhau = 'Mật khẩu phải có ít nhất 6 ký tự'
    isValid = false
  }

  if (!userForm.xacNhanMatKhau.trim()) {
    errors.xacNhanMatKhau = 'Vui lòng xác nhận mật khẩu'
    isValid = false
  } else if (userForm.matKhau !== userForm.xacNhanMatKhau) {
    errors.xacNhanMatKhau = 'Mật khẩu xác nhận không khớp'
    isValid = false
  }

  // Validate phone number if provided
  if (userForm.soDienThoai && !/^[0-9]{10,11}$/.test(userForm.soDienThoai)) {
    errors.soDienThoai = 'Số điện thoại không hợp lệ'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: `Đã tạo người dùng ${userForm.hoTen} thành công`,
      life: 3000
    })

    // Redirect to users list
    await router.push('/users')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Có lỗi xảy ra khi tạo người dùng',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

// Set page title
useHead({
  title: 'Thêm người dùng - LapXpert Admin'
})
</script>
