<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Qu<PERSON><PERSON> lý sản phẩm
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Quản lý danh sách laptop và phụ kiện
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          outlined
          @click="refreshData"
          :loading="loading"
        />
        <Button
          icon="pi pi-plus"
          label="Thêm sản phẩm"
          @click="navigateTo('/products/create')"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            T<PERSON><PERSON> kiế<PERSON>
          </label>
          <InputText
            v-model="filters.tenSanPham"
            placeholder="Tên sản phẩm..."
            class="w-full"
            @input="debouncedSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Thương hiệu
          </label>
          <Dropdown
            v-model="filters.thuongHieuId"
            :options="brands"
            optionLabel="moTaThuongHieu"
            optionValue="id"
            placeholder="Chọn thương hiệu"
            class="w-full"
            showClear
            @change="searchProducts"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Danh mục
          </label>
          <MultiSelect
            v-model="filters.danhMucIds"
            :options="categories"
            optionLabel="tenDanhMuc"
            optionValue="id"
            placeholder="Chọn danh mục"
            class="w-full"
            @change="searchProducts"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Trạng thái
          </label>
          <Dropdown
            v-model="filters.trangThai"
            :options="statusOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Chọn trạng thái"
            class="w-full"
            showClear
            @change="searchProducts"
          />
        </div>
      </div>
    </div>

    <!-- Products DataTable -->
    <div class="card">
      <DataTable
        v-model:selection="selectedProducts"
        :value="products"
        :loading="loading"
        dataKey="id"
        paginator
        :rows="10"
        :rowsPerPageOptions="[5, 10, 25, 50]"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Hiển thị {first} đến {last} trong tổng số {totalRecords} sản phẩm"
        responsiveLayout="scroll"
        :globalFilterFields="['tenSanPham', 'maSanPham', 'thuongHieu.moTaThuongHieu']"
        selectionMode="multiple"
        :metaKeySelection="false"
        class="p-datatable-sm"
      >
        <template #header>
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-3">
              <h3 class="text-lg font-semibold">Danh sách sản phẩm</h3>
              <Badge :value="products.length" severity="info" />
            </div>
            <div class="flex gap-2">
              <Button
                v-if="selectedProducts.length > 0"
                icon="pi pi-trash"
                label="Xóa đã chọn"
                severity="danger"
                outlined
                @click="confirmDeleteSelected"
              />
              <Button
                icon="pi pi-download"
                label="Xuất Excel"
                outlined
                @click="exportToExcel"
              />
            </div>
          </div>
        </template>

        <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

        <Column field="maSanPham" header="Mã SP" sortable style="min-width: 120px">
          <template #body="{ data }">
            <span class="font-mono text-sm">{{ data.maSanPham }}</span>
          </template>
        </Column>

        <Column field="tenSanPham" header="Tên sản phẩm" sortable style="min-width: 200px">
          <template #body="{ data }">
            <div class="flex items-center gap-3">
              <img
                v-if="data.hinhAnh && data.hinhAnh[0]"
                :src="data.hinhAnh[0]"
                :alt="data.tenSanPham"
                class="w-10 h-10 rounded-lg object-cover"
              />
              <div v-else class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <i class="pi pi-image text-gray-400"></i>
              </div>
              <div>
                <div class="font-medium">{{ data.tenSanPham }}</div>
                <div class="text-sm text-gray-500">{{ truncateText(data.moTa, 50) }}</div>
              </div>
            </div>
          </template>
        </Column>

        <Column field="thuongHieu.moTaThuongHieu" header="Thương hiệu" sortable style="min-width: 120px">
          <template #body="{ data }">
            <Tag v-if="data.thuongHieu" :value="data.thuongHieu.moTaThuongHieu" severity="info" />
            <span v-else class="text-gray-400">Chưa có</span>
          </template>
        </Column>

        <Column field="trangThai" header="Trạng thái" sortable style="min-width: 100px">
          <template #body="{ data }">
            <Tag
              :value="data.trangThai ? 'Hoạt động' : 'Ngừng bán'"
              :severity="data.trangThai ? 'success' : 'danger'"
            />
          </template>
        </Column>

        <Column header="Thao tác" style="min-width: 120px">
          <template #body="{ data }">
            <div class="flex gap-2">
              <Button
                icon="pi pi-eye"
                size="small"
                outlined
                @click="viewProduct(data)"
                v-tooltip.top="'Xem chi tiết'"
              />
              <Button
                icon="pi pi-pencil"
                size="small"
                outlined
                @click="editProduct(data)"
                v-tooltip.top="'Chỉnh sửa'"
              />
              <Button
                icon="pi pi-trash"
                size="small"
                outlined
                severity="danger"
                @click="confirmDelete(data)"
                v-tooltip.top="'Xóa'"
              />
            </div>
          </template>
        </Column>

        <template #empty>
          <div class="text-center py-8">
            <i class="pi pi-search text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">Không tìm thấy sản phẩm nào</p>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import type { Product, ProductSearchFilters } from '~/composables/useProduct'

definePageMeta({
  middleware: 'auth'
})

const { getAllProducts, searchProducts, deleteProduct } = useProduct()
const { getAllBrands, getAllCategories } = useProductAttributes()
const confirm = useConfirm()
const toast = useToast()

// Reactive data
const loading = ref(false)
const products = ref<Product[]>([])
const selectedProducts = ref<Product[]>([])
const brands = ref([])
const categories = ref([])

// Filters
const filters = ref<ProductSearchFilters>({
  tenSanPham: '',
  thuongHieuId: undefined,
  danhMucIds: [],
  trangThai: undefined
})

const statusOptions = [
  { label: 'Hoạt động', value: true },
  { label: 'Ngừng bán', value: false }
]

// Debounced search
const debouncedSearch = useDebounceFn(() => {
  searchProductsData()
}, 500)

/**
 * Load initial data
 */
const loadData = async () => {
  loading.value = true
  try {
    const [productsData, brandsData, categoriesData] = await Promise.all([
      getAllProducts(),
      getAllBrands(),
      getAllCategories()
    ])

    products.value = productsData
    brands.value = brandsData
    categories.value = categoriesData
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tải dữ liệu sản phẩm',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

/**
 * Search products with filters
 */
const searchProductsData = async () => {
  loading.value = true
  try {
    const searchFilters = { ...filters.value }

    // Remove empty values
    Object.keys(searchFilters).forEach(key => {
      if (searchFilters[key] === '' || searchFilters[key] === null || searchFilters[key] === undefined) {
        delete searchFilters[key]
      }
    })

    const data = await searchProducts(searchFilters)
    products.value = data
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tìm kiếm sản phẩm',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

/**
 * Refresh data
 */
const refreshData = () => {
  // Reset filters
  filters.value = {
    tenSanPham: '',
    thuongHieuId: undefined,
    danhMucIds: [],
    trangThai: undefined
  }

  loadData()
}

/**
 * View product details
 */
const viewProduct = (product: Product) => {
  navigateTo(`/products/${product.id}`)
}

/**
 * Edit product
 */
const editProduct = (product: Product) => {
  navigateTo(`/products/${product.id}/edit`)
}

/**
 * Confirm delete single product
 */
const confirmDelete = (product: Product) => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa sản phẩm "${product.tenSanPham}"?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteProductData(product.id!)
  })
}

/**
 * Confirm delete selected products
 */
const confirmDeleteSelected = () => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa ${selectedProducts.value.length} sản phẩm đã chọn?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteSelectedProducts()
  })
}

/**
 * Delete product
 */
const deleteProductData = async (id: number) => {
  try {
    await deleteProduct(id)
    await loadData()
    selectedProducts.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Delete selected products
 */
const deleteSelectedProducts = async () => {
  try {
    await Promise.all(
      selectedProducts.value.map(product => deleteProduct(product.id!))
    )
    await loadData()
    selectedProducts.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Export to Excel
 */
const exportToExcel = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông tin',
    detail: 'Tính năng xuất Excel sẽ được triển khai trong phiên bản tiếp theo',
    life: 3000
  })
}

// Load data on mount
onMounted(() => {
  loadData()
})

// Set page title
useHead({
  title: 'Quản lý sản phẩm - LapXpert Admin'
})
</script>
