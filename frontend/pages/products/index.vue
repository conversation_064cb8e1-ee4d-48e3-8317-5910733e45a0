<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Quản lý sản phẩm
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Quản lý danh sách laptop và phụ kiện
        </p>
      </div>
      <Button
        icon="pi pi-plus"
        label="Thêm sản phẩm"
        @click="navigateTo('/products/create')"
      />
    </div>

    <!-- Coming Soon Card -->
    <div class="card text-center py-12">
      <div class="space-y-4">
        <i class="pi pi-desktop text-6xl text-gray-400"></i>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
          Quản lý sản phẩm
        </h3>
        <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          Trang quản lý sản phẩm sẽ được triển khai trong Phase 3 của kế hoạch migration.
          Hiện tại đang trong giai đoạn Phase 1 - Foundation & Core Infrastructure.
        </p>
        <div class="flex justify-center gap-3 pt-4">
          <Button
            label="Xem kế hoạch migration"
            icon="pi pi-external-link"
            outlined
            @click="openMigrationPlan"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const toast = useToast()

const openMigrationPlan = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông tin',
    detail: 'Trang quản lý sản phẩm sẽ được triển khai trong Phase 3 (Weeks 9-13)',
    life: 5000
  })
}

// Set page title
useHead({
  title: 'Quản lý sản phẩm - LapXpert Admin'
})
</script>
