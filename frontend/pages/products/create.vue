<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Thêm sản phẩm mới
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Thêm laptop hoặc phụ kiện mới vào hệ thống
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          outlined
          @click="$router.back()"
        />
        <Button
          icon="pi pi-save"
          label="Lưu sản phẩm"
          @click="saveProduct"
          :loading="saving"
          :disabled="!isFormValid"
        />
      </div>
    </div>

    <!-- Product Form -->
    <form @submit.prevent="saveProduct" class="space-y-6">
      <!-- Basic Information -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Thông tin cơ bản</h3>
        </div>
        <div class="card-content space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tên sản phẩm <span class="text-red-500">*</span>
              </label>
              <InputText
                v-model="form.tenSanPham"
                placeholder="Nhập tên sản phẩm"
                class="w-full"
                :class="{ 'p-invalid': errors.tenSanPham }"
              />
              <small v-if="errors.tenSanPham" class="p-error">{{ errors.tenSanPham }}</small>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Thương hiệu <span class="text-red-500">*</span>
              </label>
              <Dropdown
                v-model="form.thuongHieu"
                :options="brands"
                optionLabel="moTaThuongHieu"
                placeholder="Chọn thương hiệu"
                class="w-full"
                :class="{ 'p-invalid': errors.thuongHieu }"
              />
              <small v-if="errors.thuongHieu" class="p-error">{{ errors.thuongHieu }}</small>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Danh mục <span class="text-red-500">*</span>
              </label>
              <MultiSelect
                v-model="form.danhMucs"
                :options="categories"
                optionLabel="tenDanhMuc"
                placeholder="Chọn danh mục"
                class="w-full"
                :class="{ 'p-invalid': errors.danhMucs }"
              />
              <small v-if="errors.danhMucs" class="p-error">{{ errors.danhMucs }}</small>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mô tả sản phẩm
              </label>
              <Textarea
                v-model="form.moTa"
                placeholder="Nhập mô tả chi tiết về sản phẩm"
                rows="4"
                class="w-full"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày ra mắt
              </label>
              <Calendar
                v-model="form.ngayRaMat"
                placeholder="Chọn ngày ra mắt"
                class="w-full"
                dateFormat="dd/mm/yy"
                showIcon
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Trạng thái
              </label>
              <div class="flex items-center">
                <InputSwitch v-model="form.trangThai" />
                <span class="ml-3">{{ form.trangThai ? 'Hoạt động' : 'Ngừng bán' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Images -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Hình ảnh sản phẩm</h3>
        </div>
        <div class="card-content">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                URL hình ảnh
              </label>
              <div class="flex gap-2">
                <InputText
                  v-model="newImageUrl"
                  placeholder="Nhập URL hình ảnh"
                  class="flex-1"
                />
                <Button
                  icon="pi pi-plus"
                  label="Thêm"
                  @click="addImage"
                  :disabled="!newImageUrl"
                />
              </div>
            </div>

            <div v-if="form.hinhAnh && form.hinhAnh.length > 0" class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="(image, index) in form.hinhAnh"
                :key="index"
                class="relative group"
              >
                <img
                  :src="image"
                  :alt="`Product image ${index + 1}`"
                  class="w-full h-32 object-cover rounded-lg border"
                />
                <Button
                  icon="pi pi-times"
                  size="small"
                  severity="danger"
                  class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  @click="removeImage(index)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Variants -->
      <div class="card">
        <div class="card-header">
          <div class="flex justify-between items-center">
            <h3 class="card-title">Biến thể sản phẩm</h3>
            <Button
              icon="pi pi-plus"
              label="Thêm biến thể"
              @click="addVariant"
            />
          </div>
        </div>
        <div class="card-content">
          <div v-if="form.sanPhamChiTiets && form.sanPhamChiTiets.length > 0" class="space-y-4">
            <div
              v-for="(variant, index) in form.sanPhamChiTiets"
              :key="index"
              class="border rounded-lg p-4 space-y-4"
            >
              <div class="flex justify-between items-center">
                <h4 class="font-medium">Biến thể {{ index + 1 }}</h4>
                <Button
                  icon="pi pi-trash"
                  size="small"
                  severity="danger"
                  outlined
                  @click="removeVariant(index)"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Giá bán <span class="text-red-500">*</span>
                  </label>
                  <InputNumber
                    v-model="variant.giaBan"
                    placeholder="Nhập giá bán"
                    class="w-full"
                    :min="0"
                    :step="1000"
                    currency="VND"
                    locale="vi-VN"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Số lượng tồn
                  </label>
                  <InputNumber
                    v-model="variant.soLuongTon"
                    placeholder="Nhập số lượng"
                    class="w-full"
                    :min="0"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Trạng thái
                  </label>
                  <div class="flex items-center">
                    <InputSwitch v-model="variant.trangThai" />
                    <span class="ml-3">{{ variant.trangThai ? 'Hoạt động' : 'Ngừng bán' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            <i class="pi pi-plus-circle text-4xl mb-4"></i>
            <p>Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.</p>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import type { Product, ProductVariant } from '~/composables/useProduct'

definePageMeta({
  middleware: 'auth'
})

const { createProduct } = useProduct()
const {
  getAllBrands,
  getAllCategories,
  getAllCpus,
  getAllRams,
  getAllGpus,
  getAllColors,
  getAllStorages,
  getAllScreens
} = useProductAttributes()
const toast = useToast()
const router = useRouter()

// Form data
const form = ref<Product>({
  tenSanPham: '',
  moTa: '',
  hinhAnh: [],
  ngayRaMat: null,
  trangThai: true,
  thuongHieu: null,
  danhMucs: [],
  sanPhamChiTiets: []
})

// Form state
const saving = ref(false)
const newImageUrl = ref('')

// Validation errors
const errors = ref({
  tenSanPham: '',
  thuongHieu: '',
  danhMucs: ''
})

// Attributes data
const brands = ref([])
const categories = ref([])
const cpus = ref([])
const rams = ref([])
const gpus = ref([])
const colors = ref([])
const storages = ref([])
const screens = ref([])

/**
 * Load attributes data
 */
const loadAttributes = async () => {
  try {
    const [
      brandsData,
      categoriesData,
      cpusData,
      ramsData,
      gpusData,
      colorsData,
      storagesData,
      screensData
    ] = await Promise.all([
      getAllBrands(),
      getAllCategories(),
      getAllCpus(),
      getAllRams(),
      getAllGpus(),
      getAllColors(),
      getAllStorages(),
      getAllScreens()
    ])

    brands.value = brandsData
    categories.value = categoriesData
    cpus.value = cpusData
    rams.value = ramsData
    gpus.value = gpusData
    colors.value = colorsData
    storages.value = storagesData
    screens.value = screensData
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tải dữ liệu thuộc tính sản phẩm',
      life: 5000
    })
  }
}

/**
 * Validate form
 */
const validateForm = () => {
  errors.value = {
    tenSanPham: '',
    thuongHieu: '',
    danhMucs: ''
  }

  let isValid = true

  if (!form.value.tenSanPham?.trim()) {
    errors.value.tenSanPham = 'Tên sản phẩm là bắt buộc'
    isValid = false
  }

  if (!form.value.thuongHieu) {
    errors.value.thuongHieu = 'Thương hiệu là bắt buộc'
    isValid = false
  }

  if (!form.value.danhMucs || form.value.danhMucs.length === 0) {
    errors.value.danhMucs = 'Ít nhất một danh mục là bắt buộc'
    isValid = false
  }

  return isValid
}

/**
 * Check if form is valid
 */
const isFormValid = computed(() => {
  return form.value.tenSanPham?.trim() &&
         form.value.thuongHieu &&
         form.value.danhMucs &&
         form.value.danhMucs.length > 0
})

/**
 * Add image URL
 */
const addImage = () => {
  if (newImageUrl.value.trim()) {
    if (!form.value.hinhAnh) {
      form.value.hinhAnh = []
    }
    form.value.hinhAnh.push(newImageUrl.value.trim())
    newImageUrl.value = ''
  }
}

/**
 * Remove image
 */
const removeImage = (index: number) => {
  if (form.value.hinhAnh) {
    form.value.hinhAnh.splice(index, 1)
  }
}

/**
 * Add product variant
 */
const addVariant = () => {
  if (!form.value.sanPhamChiTiets) {
    form.value.sanPhamChiTiets = []
  }

  const newVariant: ProductVariant = {
    giaBan: 0,
    soLuongTon: 0,
    trangThai: true,
    cpu: null,
    ram: null,
    gpu: null,
    mauSac: null,
    oCung: null,
    manHinh: null
  }

  form.value.sanPhamChiTiets.push(newVariant)
}

/**
 * Remove product variant
 */
const removeVariant = (index: number) => {
  if (form.value.sanPhamChiTiets) {
    form.value.sanPhamChiTiets.splice(index, 1)
  }
}

/**
 * Save product
 */
const saveProduct = async () => {
  if (!validateForm()) {
    return
  }

  saving.value = true
  try {
    await createProduct(form.value)

    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Sản phẩm đã được tạo thành công',
      life: 3000
    })

    // Navigate back to products list
    router.push('/products')
  } catch (error) {
    // Error is handled by the composable
  } finally {
    saving.value = false
  }
}

// Load attributes on mount
onMounted(() => {
  loadAttributes()
})

// Set page title
useHead({
  title: 'Thêm sản phẩm - LapXpert Admin'
})
</script>
