<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Thêm sản phẩm mới
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Thêm laptop hoặc phụ kiện mới vào hệ thống
        </p>
      </div>
      <Button
        icon="pi pi-arrow-left"
        label="Quay lại"
        outlined
        @click="$router.back()"
      />
    </div>

    <!-- Coming Soon Card -->
    <div class="card text-center py-12">
      <div class="space-y-4">
        <i class="pi pi-plus-circle text-6xl text-gray-400"></i>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
          Thêm sản phẩm
        </h3>
        <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          Form thêm sản phẩm sẽ được triển khai trong Phase 3 của kế hoạch migration.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

// Set page title
useHead({
  title: 'Thêm sản phẩm - LapXpert Admin'
})
</script>
