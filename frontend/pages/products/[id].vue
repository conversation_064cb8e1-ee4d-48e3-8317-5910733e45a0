<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          <PERSON> tiết sản phẩm
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Xem thông tin chi tiết sản phẩm
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          outlined
          @click="$router.back()"
        />
        <Button
          icon="pi pi-pencil"
          label="Chỉnh sửa"
          @click="editProduct"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="card">
      <div class="flex items-center justify-center py-12">
        <ProgressSpinner />
      </div>
    </div>

    <!-- Product Details -->
    <div v-else-if="product" class="space-y-6">
      <!-- Basic Information -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Thông tin cơ bản</h3>
        </div>
        <div class="card-content">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mã sản phẩm
              </label>
              <p class="font-mono text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded">
                {{ product.maSanPham }}
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tên sản phẩm
              </label>
              <p class="font-medium">{{ product.tenSanPham }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Thương hiệu
              </label>
              <Tag v-if="product.thuongHieu" :value="product.thuongHieu.moTaThuongHieu" severity="info" />
              <span v-else class="text-gray-400">Chưa có</span>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Trạng thái
              </label>
              <Tag
                :value="product.trangThai ? 'Hoạt động' : 'Ngừng bán'"
                :severity="product.trangThai ? 'success' : 'danger'"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày ra mắt
              </label>
              <p>{{ product.ngayRaMat ? formatDate(product.ngayRaMat) : 'Chưa có' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Danh mục
              </label>
              <div class="flex flex-wrap gap-2">
                <Tag
                  v-for="category in product.danhMucs"
                  :key="category.id"
                  :value="category.tenDanhMuc"
                  severity="secondary"
                />
              </div>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mô tả
              </label>
              <p class="text-gray-600 dark:text-gray-400">
                {{ product.moTa || 'Chưa có mô tả' }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Images -->
      <div v-if="product.hinhAnh && product.hinhAnh.length > 0" class="card">
        <div class="card-header">
          <h3 class="card-title">Hình ảnh sản phẩm</h3>
        </div>
        <div class="card-content">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="(image, index) in product.hinhAnh"
              :key="index"
              class="relative group cursor-pointer"
              @click="viewImage(image)"
            >
              <img
                :src="image"
                :alt="`Product image ${index + 1}`"
                class="w-full h-32 object-cover rounded-lg border hover:shadow-lg transition-shadow"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Product Variants -->
      <div v-if="product.sanPhamChiTiets && product.sanPhamChiTiets.length > 0" class="card">
        <div class="card-header">
          <h3 class="card-title">Biến thể sản phẩm</h3>
        </div>
        <div class="card-content">
          <DataTable
            :value="product.sanPhamChiTiets"
            responsiveLayout="scroll"
            class="p-datatable-sm"
          >
            <Column field="sku" header="SKU" style="min-width: 120px">
              <template #body="{ data }">
                <span class="font-mono text-sm">{{ data.sku || 'Chưa có' }}</span>
              </template>
            </Column>

            <Column field="giaBan" header="Giá bán" style="min-width: 120px">
              <template #body="{ data }">
                <span class="font-medium">{{ formatCurrency(data.giaBan) }}</span>
              </template>
            </Column>

            <Column field="giaKhuyenMai" header="Giá KM" style="min-width: 120px">
              <template #body="{ data }">
                <span v-if="data.giaKhuyenMai" class="text-red-600 font-medium">
                  {{ formatCurrency(data.giaKhuyenMai) }}
                </span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </Column>

            <Column field="soLuongTon" header="Tồn kho" style="min-width: 100px">
              <template #body="{ data }">
                <Badge
                  :value="data.soLuongTon || 0"
                  :severity="(data.soLuongTon || 0) > 0 ? 'success' : 'danger'"
                />
              </template>
            </Column>

            <Column field="trangThai" header="Trạng thái" style="min-width: 100px">
              <template #body="{ data }">
                <Tag
                  :value="data.trangThai ? 'Hoạt động' : 'Ngừng bán'"
                  :severity="data.trangThai ? 'success' : 'danger'"
                />
              </template>
            </Column>

            <template #empty>
              <div class="text-center py-4">
                <p class="text-gray-500">Chưa có biến thể nào</p>
              </div>
            </template>
          </DataTable>
        </div>
      </div>

      <!-- Audit Information -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Thông tin kiểm toán</h3>
        </div>
        <div class="card-content">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Người tạo
              </label>
              <p>{{ product.nguoiTao || 'Hệ thống' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày tạo
              </label>
              <p>{{ formatDateTime(product.createdAt) }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Người cập nhật
              </label>
              <p>{{ product.nguoiCapNhat || 'Chưa có' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày cập nhật
              </label>
              <p>{{ formatDateTime(product.updatedAt) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else class="card">
      <div class="text-center py-12">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-4"></i>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Không tìm thấy sản phẩm
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          Sản phẩm có thể đã bị xóa hoặc không tồn tại.
        </p>
      </div>
    </div>

    <!-- Image Viewer Dialog -->
    <Dialog v-model:visible="imageViewerVisible" modal :style="{ width: '80vw' }" :maximizable="true">
      <template #header>
        <span>Xem hình ảnh</span>
      </template>
      <div class="flex justify-center">
        <img :src="selectedImage" alt="Product image" class="max-w-full max-h-96 object-contain" />
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import type { Product } from '~/composables/useProduct'

definePageMeta({
  middleware: 'auth'
})

const route = useRoute()
const router = useRouter()
const { getProductById } = useProduct()

// Reactive data
const loading = ref(true)
const product = ref<Product | null>(null)
const imageViewerVisible = ref(false)
const selectedImage = ref('')

/**
 * Load product data
 */
const loadProduct = async () => {
  const productId = parseInt(route.params.id as string)
  
  if (isNaN(productId)) {
    router.push('/products')
    return
  }

  loading.value = true
  try {
    product.value = await getProductById(productId)
  } catch (error) {
    console.error('Error loading product:', error)
  } finally {
    loading.value = false
  }
}

/**
 * Edit product
 */
const editProduct = () => {
  router.push(`/products/${route.params.id}/edit`)
}

/**
 * View image in dialog
 */
const viewImage = (imageUrl: string) => {
  selectedImage.value = imageUrl
  imageViewerVisible.value = true
}

// Load product on mount
onMounted(() => {
  loadProduct()
})

// Set page title
useHead({
  title: computed(() => product.value ? `${product.value.tenSanPham} - LapXpert Admin` : 'Chi tiết sản phẩm - LapXpert Admin')
})
</script>
